import datetime
from typing import Any, Generic, Literal, Optional, TypeVar

from pydantic import BaseModel, Field, SerializeAsAny
from typing_extensions import TypedDict

from services.studio._text_to_workflow.common.api_workflow.schema import API_WF_EXPRESSION_LANGUAGE
from services.studio._text_to_workflow.common.schema import (
    Connection,
    File,
    TargetFramework,
    UIObject,
    Variable,
    WorkflowError,
    WorkflowFileMetadata,
)
from services.studio._text_to_workflow.utils.request_schema import BasePydanticRequest, BasePydanticResponse
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import ProjectFile, WorkflowProcessingError

LOGGER = AppInsightsLogger()

# Define scenario types as a Literal type for type safety
# TODO: change to enum
WorkflowScenarioType = Literal["generate-workflow", "rewrite-workflow", "edit-workflow", "fix-workflow", "analyze-workflow"]
APIWorkflowScenarioType = Literal["generate-workflow", "rewrite-workflow", "edit-workflow", "analyze-workflow"]


class ChatMessage(BaseModel):
    role: str
    content: str
    category: str | None = "chat"
    filePath: str | None = None
    timestamp: datetime.datetime


class UiPathEntities(BaseModel):  # TODO: for api wfs do we need assets/queues etc? or just connections?
    assets: list[str] = []
    queues: list[str] = []
    processes: list[str] = []
    connections: list[Connection] = []


class ProjectDefinition(BaseModel):
    name: str
    description: str | None = None
    files: list[ProjectFile] = []


# Define a TypeVar bound to ProjectDefinition
PD = TypeVar("PD", bound=ProjectDefinition)


class RPAProjectDefinition(ProjectDefinition):
    objects: list[UIObject] = []
    targetFramework: TargetFramework


class APIWFProjectDefinition(ProjectDefinition):
    pass


class WorkflowDefinition(BaseModel):
    content: str
    path: str | None = None
    metadata: WorkflowFileMetadata = Field(default_factory=WorkflowFileMetadata)


class WorkflowDesignerState(BaseModel):
    availableVariables: list[Variable]
    availableAdditionalTypeDefinitions: str
    selectedActivityId: str | None = None
    selectedProperty: str | None = None


class WorkflowAssistantBaseRequest(BasePydanticRequest, Generic[PD]):
    userRequest: str
    messageHistory: list[ChatMessage]
    projectDefinition: PD
    currentWorkflow: WorkflowDefinition | None = None
    currentWorkflowDesignerState: WorkflowDesignerState
    attachments: list[File] | None = None
    additionalInstructions: str | None = ""
    availableEntities: UiPathEntities
    runId: Optional[str] = None


class RPAWorkflowAssistantRequest(WorkflowAssistantBaseRequest[RPAProjectDefinition]):
    pass


class APIWorkflowAssistantRequest(WorkflowAssistantBaseRequest[APIWFProjectDefinition]):
    expressionLanguage: API_WF_EXPRESSION_LANGUAGE


class WorkflowAssistantChangeResponse(BasePydanticResponse):
    path: str | None = None
    scenario: str
    newWorkflowContent: str | None
    previousWorkflowContent: str | None
    message: str | None = None
    jitCommands: list[dict[str, Any]] = []
    testCase: bool = False


class APIWorkflowAssistantChangeResponse(WorkflowAssistantChangeResponse):
    errors: list[SerializeAsAny[WorkflowProcessingError]] | None = None
    summary: str | None = None


ScenarioT = TypeVar("ScenarioT", bound=WorkflowScenarioType)


class WorkflowAssistantRouterModelResponse(BaseModel, Generic[ScenarioT]):
    thought: str = Field(
        description="A brief analysis of the user query in the context of the chat history, the current workflow, the project definition and the available files and entities. Think about whether the user query refers to the current workflow, a workflow in the chat history, or multiple workflows (from the chat history and/or the current workflow). Think about the scenario and the user's intent. If it's between edit and append, think very, very deeply whether this is an append or edit in case it's not very clear from the user query and workflow history. Use at most 50 words for this analysis."
    )
    valid: bool = Field(description="A boolean value indicating whether the user query relates to an automation or development task. If not, return false.")
    request: str = Field(
        description="A refined version of the user query. If the query is not valid, leave this empty. Incorporate any useful details from the chat history and attachments into the refined user request."
    )
    followUp: bool = Field(
        description="A boolean value indicating whether the user query needs a follow-up to be answered. If yes, provide a follow-up question that helps to clarify the user query."
    )
    question: str | None = Field(
        description="A concise follow-up question in the language that the user queried in, that helps to clarify the user query, if the user query is highly ambiguous. If not needed, return null."
    )
    score: int = Field(
        description="A score between 1 and 5, representing the complexity of the user query. 1 is used for simple queries, 5 for complex queries that require heavily editing the workflow."
    )
    entities: list[int] = Field(description="The list of entity ids that are relevant for the user query.")
    scenario: ScenarioT = Field(
        description="The scenario of the user query. Use the following values: generate-workflow, edit-workflow, rewrite-workflow, fix-workflow, analyze-workflow."
    )
    workflowRefs: list[int] = Field(
        description="If the user query is about a specific workflow, list the workflow id here. You must always add at least one workflow id (from the current workflow or the chat history). Only list more than one workflow reference if the user query mentions multiple workflows (necessarily from the chat history or the current workflow) or parts of multiple workflows seen in the history."
    )
    instructions: str | None = Field(
        description="Additional instructions or constraints for the workflow generation, if any. May stem from the chat history (e.g. you already tried a particular approach in the past and it didn't work, so the user complained about it and you can use that info to improve the new request) or from the attachment contents (do not mention 'attachments' here, as they will not be sent further after your routing). Copy useful instructions or info that come from an attachment because the attachment will not be sent further after your routing."
    )
    path: str | None = Field(
        description="If the scenario is generate-workflow, this should propose a new file path, e.g. NewWorkflow.xaml. Take a look at the project files to ensure we don't have conflicting names, and feel free to propose a subfolder that you see there, if the project files seem to be organized in subfolders, e.g. subfolder/NewWorkflow.xaml. For other scenarios, this should be null."
    )
    testCase: bool = Field(
        description="A boolean value indicating whether the user query is a test case (when the user is asking for a test case in the generate-workflow scenario or when the user wants to edit a workflow to be a test case). If yes, return true."
    )
    message: str | None = Field(
        description="A message which will inform the user about the next steps. The message should be in the language that user queried in. Be friendly, fun, brief and concise, use at most 50 words. Make it second person when referring to the user (refer to the user as \"You\"), as we will send this message to them. Be friendly and polite, but clear and mindful in your thoughts, as this message will be sent to the user. IMPORTANT: NEVER mention any scenario names verbatim - do not use terms like 'generate-workflow', 'edit-workflow', 'rewrite-workflow', 'fix-workflow', or 'analyze-workflow' in your message, nor mention any workflow or attachment ids, as these are internal technical categories not meant for users. For example, instead of saying 'this is an edit-workflow scenario', say 'you want to change a specific property value of an activity'. Describe the user's intent naturally without using scenario terminology."
    )


class APIWorkflowAssistantRouterModelResponse(WorkflowAssistantRouterModelResponse[APIWorkflowScenarioType]):
    scenario: APIWorkflowScenarioType = Field(
        description="The scenario of the user query. Use the following values: generate-workflow, edit-workflow, rewrite-workflow, analyze-workflow."
    )
    path: str | None = Field(
        description="If the scenario is generate-workflow, this should propose a new file path, e.g. NewWorkflow.json. Take a look at the project files to ensure we don't have conflicting names, and feel free to propose a subfolder that you see there, if the project files seem to be organized in subfolders, e.g. subfolder/NewWorkflow.json. For other scenarios, this should be null."
    )


class WorkflowAssistantRouterResponse(BaseModel):
    """
    A response class that mimics WorkflowAssistantRouterModelResponse but contains actual objects
    referenced by the IDs instead of just the IDs.
    """

    thought: str
    valid: bool
    request: str
    followUp: bool
    question: str | None
    score: int
    entities: list[str] = []  # String representation of entities (assets, queues, processes)
    scenario: WorkflowScenarioType
    workflowRefs: list[WorkflowDefinition] = []
    instructions: str | None = None
    path: str | None = None
    message: str | None = None
    testCase: bool = False

    @classmethod
    def from_model_response(
        cls, model_response: WorkflowAssistantRouterModelResponse, context: "WorkflowAssistantContext"
    ) -> "WorkflowAssistantRouterResponse":
        """
        Create a WorkflowAssistantRouterResponse from a WorkflowAssistantRouterModelResponse
        by resolving IDs to their corresponding objects from the context.
        """

        # Create base response with fields that don't need resolution
        router_response = cls(
            thought=model_response.thought,
            valid=model_response.valid,
            request=model_response.request,
            followUp=model_response.followUp,
            question=model_response.question,
            score=model_response.score,
            scenario=model_response.scenario,
            instructions=model_response.instructions,
            path=model_response.path,
            message=model_response.message,
            testCase=model_response.testCase,
        )

        # TODO: these are currently not used downstream, TBD if we actually want a dedicated retriever for this
        # Resolve entity references (assets, queues, processes)
        for entity_id in model_response.entities:
            if entity_id in context.assets:
                router_response.entities.append(f"Asset: {context.assets[entity_id]}")
            elif entity_id in context.queues:
                router_response.entities.append(f"Queue: {context.queues[entity_id]}")
            elif entity_id in context.processes:
                router_response.entities.append(f"Process: {context.processes[entity_id]}")
            else:
                LOGGER.warning(f"Entity ID {entity_id} not found in context entities")

        # Resolve workflow references
        for workflow_id in model_response.workflowRefs:
            if workflow_id == 0 and context.current_workflow:
                # Special case: workflow ID 0 refers to the current workflow
                router_response.workflowRefs.append(context.current_workflow)
            elif workflow_id in context.message_history_workflow_ids:
                # Construct the workflow definition from the message content and file path
                workflow_definition = WorkflowDefinition(
                    content=context.message_history_workflow_ids[workflow_id][0], path=context.message_history_workflow_ids[workflow_id][1]
                )

                router_response.workflowRefs.append(workflow_definition)
            else:
                LOGGER.warning(f"Workflow reference ID {workflow_id} not found in context workflow references")

        return router_response


class WorkflowAssistantContext(BaseModel):
    # The user request is the user's request
    user_request: str
    # The current workflow is the current workflow
    current_workflow: WorkflowDefinition | None = None
    # The current workflow designer state is the current workflow designer state
    current_workflow_designer_state: WorkflowDesignerState | None = None
    # The message history is a list of messages, each with a role and content
    message_history_map: dict[int, ChatMessage] = {}
    # The project files are a list of files, each with a name and content
    project_files_map: dict[int, ProjectFile] = {}
    # The current workflow errors are a list of errors, each with an activity id, property and message
    current_workflow_errors: dict[int, WorkflowError] = {}
    # The referenced workflows are a list of workflows, each with an id and name
    referenced_workflows_map: dict[int, WorkflowDefinition] = {}
    # The assets are a list of assets, each with an id and name
    assets: dict[int, str] = {}
    # The queues are a list of queues, each with an id and name
    queues: dict[int, str] = {}
    # The processes are a list of processes, each with an id and name
    processes: dict[int, str] = {}
    # The attachments are a list of attachments, each with an id and name
    attachments: dict[int, File] = {}
    # The message history workflow ids are a list of message ids, each with a workflow id
    message_history_workflow_ids: dict[int, tuple[str, str | None]] = {}
    # Workflow id counter, starting at 1 because we use 0 for the current workflow
    workflow_id_counter: int = 1
    # The current time in UTC
    current_time: datetime.datetime = datetime.datetime.now(datetime.timezone.utc)


class WorkflowAssistantPromptContent(TypedDict):
    user_query: str
    current_time: str
    user_localization: str
    message_history: str
    current_workflow: str
    additional_instructions: str
    available_entities: str


class WorkflowRouterException(Exception):
    """Exception raised when there is an error in routing the workflow request."""

    pass
