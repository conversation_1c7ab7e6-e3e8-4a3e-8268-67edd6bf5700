import asyncio
import json
import os
import pathlib
import pickle
import time
import typing as t
from abc import abstractmethod

from langchain_core.exceptions import OutputParserException
from openai import LengthFinishReasonError
from tqdm import tqdm

from services.studio._text_to_workflow.common.schema import Connection
from services.studio._text_to_workflow.common.tasks import generate_tasks_and_execute
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.serialization_utils import sanitize_object_for_yaml_dump
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.draft_generation import DraftGenerationEval


class BaseEvalService:
    """Basic structure for the evaluation logic of each step of a generation pipeline"""

    def __init__(self, output_file_prefix: str, batch_size: int = 4, retry_count: int = 3):
        self.output_file_prefix = output_file_prefix
        self.batch_size = batch_size
        self.retry_count = retry_count
        self.results: list[DraftGenerationEval] = []
        self.parsing_errors = []

    async def run(
        self,
        dataset_dict: dict[str, t.Any],  # the key should be the path to the sample, value is the sample itself
        connections: list[Connection],
        output_folder_path: str | pathlib.Path | None = None,
        generate_raw_results_attachment: bool = False,
    ):
        # we shouldn't execute this in parallel
        self.results = []
        self.parsing_errors = []
        output_folder_path = pathlib.Path(output_folder_path or os.getcwd())
        assert output_folder_path.is_dir(), f"Output folder path {output_folder_path} is not a directory"

        request_context = get_testing_request_context("en", "Autopilot for Developers Evaluation")
        request_utils.set_request_context(request_context)

        log_file = output_folder_path / f"{self.output_file_prefix}_{time.strftime('%Y%m%d-%H%M%S')}.html"

        async def task_generator() -> t.AsyncGenerator[asyncio.Task[None], None]:
            for path, sample in tqdm(dataset_dict.items(), desc="Evaluating samples", total=len(dataset_dict)):
                yield asyncio.create_task(self._process_sample(pathlib.Path(path), sample, connections))

        try:
            await generate_tasks_and_execute(task_generator(), max_concurrent_tasks=self.batch_size)
        finally:
            self._print_samples(log_file)
            if generate_raw_results_attachment:
                raw_log_file = log_file.with_suffix(".yaml")
                self._dump_raw_results(raw_log_file)

    async def _process_sample(self, path: pathlib.Path, sample: t.Any, connections: list[Connection]):
        for i in range(self.retry_count):
            try:
                self.results.append(await self._evaluate_sample(path, sample, connections))
                break  # on successful attempt, break the loop
            except LengthFinishReasonError as e:
                print(f"LengthFinishReasonError - Completion Tokens exceeded for content {e.completion.choices[0].message.content}.")
            except OutputParserException as e:
                self.parsing_errors.append({"sample": sample, "output": e.llm_output})
                print(f"OutputParserException - Parsing error for content {e.llm_output}.")
            except Exception as e:
                print(f"⚠️ Sample generation failed with: {e}.\n Sample: {json.dumps(sample) if isinstance(sample, dict) else sample}")
                import traceback

                traceback.print_exc()
            finally:
                if i == self.retry_count - 1:
                    print("❌ Fatal exception.")

    @abstractmethod
    async def _evaluate_sample(self, path: pathlib.Path, sample, connections: list[Connection]):
        raise NotImplementedError()

    @abstractmethod
    def _print_samples(self, log_file: pathlib.Path):
        raise NotImplementedError()

    def _dump_raw_results(self, raw_log_file: pathlib.Path):
        with raw_log_file.open("w") as f:
            dump_object = {
                "eval_name": self.output_file_prefix,
                "batch_size": self.batch_size,
                "retry_count": self.retry_count,
                "timestamp": time.strftime("%Y%m%d-%H%M%S"),
                "results": sanitize_object_for_yaml_dump(self.results),
                "parsing_errors": self.parsing_errors,
            }
            yaml_dump(dump_object, f)

    @staticmethod
    def _serialize_result(result_path: pathlib.Path, result: t.Any):
        """
        Serializes the result to a .pkl file given by the result_path.
        """
        result_path.parent.mkdir(parents=True, exist_ok=True)
        with result_path.open("wb") as f:
            pickle.dump(result, f)
