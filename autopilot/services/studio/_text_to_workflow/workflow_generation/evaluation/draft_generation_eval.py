import argparse
import async<PERSON>
import copy
import html
import pathlib
import time
import typing as t

import <PERSON><PERSON><PERSON><PERSON>

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.common.schema import (
    ActivitiesGenerationMode,
    Connection,
    WorkflowDict,
)
from services.studio._text_to_workflow.common.walkers import SequenceGenerationGenericActivityReplacer
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.workflow_utils import (
    cleanup_workflow_for_ted,
    delete_sequence_thought,
    get_tld_rpa,
    remove_activity_ids,
)
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import (
    EvalMode,
    FilterParamsMode,
    core_wf_workflow_edit_score,
)
from services.studio._text_to_workflow.workflow_generation.evaluation.draft_eval_base import BaseDraftEvalService
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.dataset import (
    Supported_Excel_Packages,
    Supported_Mail_Packages,
    get_cached_datapoint,
    get_corresponding_pkl_path,
    get_wf_generation_dataset,
)
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.draft_generation import DraftGenerationEval
from services.studio._text_to_workflow.workflow_generation.services.common.generation_settings_builder import GenerationSettingsBuilder
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import ActivityRetrievalResult
from services.studio._text_to_workflow.workflow_generation.services.helpers.draft_generation import DraftCreationDemonstration, DraftGenerationResult
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_activity_retrieval_service import (
    WorkflowGenerationActivityRetrievalService,
)
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_draft_service import WorkflowGenerationDraftService
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_postprocess_component import WorkflowGenerationPostProcessComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_prompt_builder_component import WorkflowGenerationPromptBuilderComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import WfGenDataPointV2, WorkflowGenerationResult
from services.studio._text_to_workflow.workflow_generation.workflow_generation_test import ActivityTreeAccuracyCalculator


class DraftGenerationEvalService(BaseDraftEvalService):
    def __init__(
        self,
        wf_gen_activities_retriever_service: WorkflowGenerationActivityRetrievalService,
        wf_gen_draft_creation_service: WorkflowGenerationDraftService,
        postprocess_component: WorkflowGenerationPostProcessComponent,
        output_file_prefix: str,
        batch_size: int = 4,
        retry_count: int = 3,
        force_recompute: bool = False,
    ):
        super().__init__(output_file_prefix, batch_size, retry_count, force_recompute)

        config_path = pathlib.Path(__file__).absolute().parent.parent / "config" / "draft_creation_prompt.yaml"
        self.draft_creation_config = yaml_load(config_path)

        self.wf_gen_activities_retriever = wf_gen_activities_retriever_service
        self.wf_gen_draft_generator = wf_gen_draft_creation_service
        self.postprocess_component = postprocess_component

    async def _evaluate_sample(self, path: pathlib.Path, sample: WfGenDataPointV2, connections: list[Connection]) -> DraftGenerationEval:
        existing_workflow = sample["existing_workflow_sequence"]
        if existing_workflow is not None:
            existing_workflow = Workflow("", "", existing_workflow)

        # get path where activity retrieval results are stored
        activity_retrieval_results_path = get_corresponding_pkl_path(path)

        # try to load the cached activity retrieval result, this helps with consistency and speed
        if not self.force_recompute and activity_retrieval_results_path.exists():
            activity_retrieval_result: ActivityRetrievalResult = get_cached_datapoint(activity_retrieval_results_path)
        else:
            # if the activity_retrieval_result is not cached OR force recompute is true, we need to compute/refresh it
            generation_settings = GenerationSettingsBuilder.build_generation_settings(
                sample["query"], sample["target_framework"], None, sample["used_activities"], Supported_Excel_Packages, Supported_Mail_Packages
            )
            activity_retrieval_result: ActivityRetrievalResult = await self.wf_gen_activities_retriever.generate_relevant_activities(
                sample["query"], existing_workflow, connections, sample["mode"], sample["target_framework"], generation_settings, True
            )

        start = time.time()
        draft_creation_result: DraftGenerationResult = await self.wf_gen_draft_generator.generate_workflow_draft(
            query=sample["query"],
            workflow=existing_workflow,
            connections=connections,
            variables=[],
            objects=[],
            mode=sample["mode"],
            target_framework=sample["target_framework"],
            activity_retrieval_result=activity_retrieval_result,
            localization="en",
            seed=sample.get("seed", None),
        )
        elapsed = time.time() - start

        # the current wf result is in draft mode, so we need to postprocess it again and convert it to a production-ready workflow
        raw_wf = draft_creation_result.result_workflow_details.workflow_generation["workflow_valid"]
        gen_wf: WorkflowGenerationResult = await self.postprocess_component.postprocess_generation(
            model=None,
            workflow_raw_yaml=raw_wf,
            query=sample["query"],
            jit_types=draft_creation_result.jit_types,
            activities_config_map=draft_creation_result.activities_config_map,
            connections_by_key={conn["connector"]: conn for conn in connections if "connector" in conn},
            target_framework=sample["target_framework"],
            objects=[],
            activities_and_triggers=draft_creation_result.activity_defs + draft_creation_result.trigger_defs,
            variables=[],
            localization="en",
            consuming_feature_type=ConsumingFeatureType.WORKFLOW_GENERATION,
            allow_triggers=sample["mode"] in {"workflow", "edit"},
            allow_assign_activity_on_uia_expansion=sample["mode"] == "testcase",
        )

        gen_wf_dict = yaml_load(gen_wf["workflow_valid"])
        # during evaluation, we add the activity ids to the workflow, we should remove them before comparing the workflows, so we can obtain a relevant score
        remove_activity_ids(gen_wf_dict)

        clean_gen_wf = t.cast(WorkflowDict, cleanup_workflow_for_ted(gen_wf_dict))
        clean_gt_workflow_yaml = t.cast(WorkflowDict, cleanup_workflow_for_ted(copy.deepcopy(sample["solution_workflow"])))

        lev_score = Levenshtein.ratio(yaml_dump(gen_wf["workflow_valid"]), yaml_dump(sample["solution_workflow"]))
        lev_score_no_extras = Levenshtein.ratio(yaml_dump(clean_gen_wf), yaml_dump(clean_gt_workflow_yaml))

        good, total = ActivityTreeAccuracyCalculator().tree_accuracy(clean_gt_workflow_yaml, clean_gen_wf)
        activity_tree_accuracy = float(good) / total

        ted_score, _, ted_nodes_mapping = self._compute_score(clean_gen_wf, clean_gt_workflow_yaml)

        ted_levenstein_params_score, levenstein_params_score, _ = self._compute_score(clean_gen_wf, clean_gt_workflow_yaml, "levenshtein", None)

        ted_exact_params_score, exact_params_score, _ = self._compute_score(clean_gen_wf, clean_gt_workflow_yaml, "exact", None)

        tld_metric = get_tld_rpa(existing_workflow.to_dict(), sample["solution_workflow"], gen_wf_dict) if existing_workflow is not None else None

        # format the prompt
        messages = draft_creation_result.prompt.to_messages()
        prompt = f"\nSYSTEM:\n {messages[0].content}\n\nUSER:\n {messages[1].content}"

        return DraftGenerationEval(
            query=sample["query"],
            ground_truth_activities=sample["used_activities"] + sample["used_triggers"],
            solution_workflow=sample["solution_workflow"],  # type: ignore
            existing_workflow_sequence=sample["existing_workflow_sequence"],  # type: ignore
            elapsed_time=elapsed,
            raw_workflow=draft_creation_result.result_workflow_details.workflow_generation["workflow_valid"],
            post_generation_errors=draft_creation_result.result_workflow_details.post_generation_errors,
            proposed_activities=draft_creation_result.activity_defs + draft_creation_result.trigger_defs,
            token_usage=draft_creation_result.token_usage.to_json(),
            demonstrations=draft_creation_result.demonstrations,
            prompt=prompt,
            processed_draft_workflow=clean_gen_wf,
            raw_model_prediction=draft_creation_result.raw_model_prediction,
            ted_nodes_mapping=ted_nodes_mapping,
            lev_score=lev_score,
            lev_score_no_extras=lev_score_no_extras,
            activity_tree_accuracy=activity_tree_accuracy,
            ted_score=ted_score,
            ted_levenstein_params_score=ted_levenstein_params_score,
            levenstein_params_score=levenstein_params_score,
            ted_exact_params_score=ted_exact_params_score,
            exact_params_score=exact_params_score,
            tld_score=tld_metric,
        )

    def serialize_demonstration(self, demonstration: DraftCreationDemonstration):
        return self.draft_generation_templates["sample_row_demonstrations_template"].format(
            query=demonstration.query,
            type=demonstration.type,
            plan=demonstration.data_point["plan"].replace("\n", "<br>") if demonstration.data_point is not None else "",
            solution_workflow=self.serialize_workflow(demonstration.workflow),  # type: ignore
        )

    def serialize_workflow(self, workflow: str | dict | None) -> str:
        if workflow is None:
            return ""
        if isinstance(workflow, dict):
            workflow = yaml_dump(workflow)
        return html.escape(workflow)

    @staticmethod
    def _compute_score(
        result_workflow_dict: WorkflowDict, gt_workflow_dict: WorkflowDict, eval_mode: EvalMode = "noparams", filter_params_mode: FilterParamsMode | None = None
    ) -> t.Tuple[float, float, list[tuple[str | None, str | None, float, float]]]:
        try:
            score, _, _, mapping = core_wf_workflow_edit_score(gt_workflow_dict, result_workflow_dict, param_match=eval_mode, filter_params=filter_params_mode)
            mapping_scores = [m[3] for m in mapping if m[3] >= 0]
            mapping_score = sum(mapping_scores) / len(mapping_scores) if mapping_scores else -1.0

            return score, mapping_score, mapping
        except Exception as e:
            print(f"ERROR computing {result_workflow_dict.get('thought', 'N/A')} score. Exception: {e}")
            return -1, -1, []

    def _get_model_settings(self):
        return ModelManager().get_llm_config(WorkflowGenerationDraftService.draft_generation_model_name)

    def _get_system_prompt_config(self):
        return self.draft_creation_config["system_msg"]


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--evaluation-dir", type=pathlib.Path, default=pathlib.Path(".").absolute())
    parser.add_argument("--name", type=str, default="draft_generation_eval")
    parser.add_argument("--mode", type=str, choices=["all"] + list(t.get_args(ActivitiesGenerationMode)), default="all")
    parser.add_argument("--batch-size", type=int, default=8)
    parser.add_argument("--limit", type=int, default=None)
    parser.add_argument("--replication", type=int, default=1)
    parser.add_argument("--filter-query", type=str, default=None)
    parser.add_argument("--exclude-raw-results", action="store_true")
    args = parser.parse_args()

    activitiesFetcher = ActivitiesRetriever()

    act_retrieval_config_path = pathlib.Path(__file__).absolute().parent.parent / "config" / "activity_retrieval_prompt.yaml"
    act_retrieval_config = yaml_load(act_retrieval_config_path)
    embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activitiesFetcher, embedding_model)

    prompt_builder_component = WorkflowGenerationPromptBuilderComponent(activitiesFetcher)

    activitiesRetrievalService = WorkflowGenerationActivityRetrievalService(activitiesFetcher, connection_embeddings_retriever, prompt_builder_component)

    dynamic_activities_component = WorkflowGenerationDynamicActivitiesComponent(activitiesFetcher)
    postprocess_component = WorkflowGenerationPostProcessComponent(
        activitiesFetcher, dynamic_activities_component, activitiesRetrievalService.ignored_activities
    )

    draftCreationService = WorkflowGenerationDraftService(activitiesFetcher, postprocess_component, prompt_builder_component)
    asyncio.run(draftCreationService.init_and_load())

    draftEvalService = DraftGenerationEvalService(
        activitiesRetrievalService, draftCreationService, postprocess_component, f"{args.name}-{args.mode}", args.batch_size
    )
    default_connections = get_connections_data()

    dataset_dict = get_wf_generation_dataset(
        ["Portable", "Windows"],
        ["test"],
    )
    if args.mode != "all":
        print(f"Filtering dataset for mode: {args.mode}. There are {len(dataset_dict)} total datapoints in the dataset.")
        if args.mode == "edit":
            dataset_dict: dict[str, WfGenDataPointV2] = {k: v | {"mode": "edit"} for k, v in dataset_dict.items() if v["mode"] == "sequence"}  # type: ignore
            if args.filter_query is not None:
                dataset_dict = {k: v for k, v in dataset_dict.items() if args.filter_query.lower() in v["query"].lower()}
            for _, v in dataset_dict.items():
                sequence = Workflow("", "", v["solution_workflow"])
                assert v["existing_workflow_sequence"] is not None
                existing_workflow_object = Workflow("", "", v["existing_workflow_sequence"])
                SequenceGenerationGenericActivityReplacer(sequence.activities).replace(existing_workflow_object)
                v["existing_workflow_sequence"], _ = delete_sequence_thought(v["existing_workflow_sequence"])  # type: ignore
                v["solution_workflow"] = existing_workflow_object.to_dict()
            if args.replication > 1:
                new_dataset = {}
                for i in range(args.replication):
                    for k, v in dataset_dict.items():
                        altered_datapoint = copy.deepcopy(v)
                        if i != 0:  # default seed for the first replication
                            altered_datapoint["seed"] = i * 1337
                        new_dataset[f"{k}_{i}"] = altered_datapoint
                dataset_dict = new_dataset
        else:
            dataset_dict = {k: v for k, v in dataset_dict.items() if v["mode"] == args.mode}
        print(f"Remaining datapoints: {len(dataset_dict)}")
    if args.limit is not None:
        dataset_dict = {k: v for idx, (k, v) in enumerate(dataset_dict.items()) if idx < args.limit}
        print(f"Remaining datapoints after limit enforcement: {len(dataset_dict)}")
    result = asyncio.run(
        draftEvalService.run(
            dataset_dict,
            connections=default_connections,
            output_folder_path=args.evaluation_dir,
            generate_raw_results_attachment=not args.exclude_raw_results,
        )
    )
