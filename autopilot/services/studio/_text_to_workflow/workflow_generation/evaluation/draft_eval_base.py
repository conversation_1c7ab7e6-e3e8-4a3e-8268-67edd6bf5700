import html
import json
import os
import pathlib
import time
import typing as t
from abc import abstractmethod

import numpy as np
from pydantic import TypeAdapter

from services.studio._text_to_workflow.common.schema import Connection
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import (
    same_activity_in_mapping,
)
from services.studio._text_to_workflow.workflow_generation.evaluation.eval_base import BaseEvalService
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.draft_generation import DraftGenerationEval
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    ActivityError,
    EditPatchStructureError,
    InvalidConnectionError,
    InvalidExpressionError,
    MissingParamError,
    MissingRequiredFieldError,
    ParamValueMismatchError,
    UndefinedVariableError,
    WorkflowProcessingError,
)

# Adapter for deserializing the post generation errors (used for the comparison report)
ERROR_SERIALIZATION_ADAPTER = TypeAdapter(
    list[
        t.Union[
            ActivityError,
            EditPatchStructureError,
            InvalidConnectionError,
            InvalidExpressionError,
            MissingParamError,
            MissingRequiredFieldError,
            ParamValueMismatchError,
            UndefinedVariableError,
            WorkflowProcessingError,
        ]
    ]
)


class BaseDraftEvalService(BaseEvalService):
    """Base class for draft evaluation"""

    def __init__(
        self,
        output_file_prefix: str,
        batch_size: int = 4,
        retry_count: int = 3,
        force_recompute: bool = False,
    ):
        super().__init__(output_file_prefix, batch_size, retry_count)
        self.force_recompute = force_recompute

        # Load the common templates and configs
        eval_config_path = (pathlib.Path(__file__).parent.parent).absolute() / "config" / "eval_templates.yaml"
        eval_config = yaml_load(eval_config_path)
        self.common_templates = eval_config["common"]
        self.draft_generation_templates = eval_config["draft_generation"]

    @abstractmethod
    async def _evaluate_sample(self, path: pathlib.Path, sample: t.Any, connections: list[Connection]):
        """Method to be implemented by subclasses to evaluate a specific sample"""
        raise NotImplementedError()

    def _print_samples(self, log_file: pathlib.Path):
        with log_file.open("w") as f:
            f.write(self.common_templates["html_template_start"])
            f.write(self.draft_generation_templates["body_template_start"])
            f.write(self.draft_generation_templates["sample_table_header_template"])
            eval_results: list[DraftGenerationEval] = t.cast(list[DraftGenerationEval], self.results)

            # collect all samples with critique data or exceptions
            serialized_exception_samples: list[str] = []

            for eval in eval_results:
                formatted_errors = self._get_formatted_errors(eval)
                self._print_sample_row(f, eval, formatted_errors)

                if len(formatted_errors) > 0:
                    exception_sample = self._format_exception_row(eval, formatted_errors)
                    serialized_exception_samples.append(exception_sample)

            self._print_averages_row(f, eval_results)
            f.write(self.draft_generation_templates["sample_table_end"])

            exceptions_section = (
                self.common_templates["parsing_exceptions_section_template"].format(
                    exception_count=len(serialized_exception_samples), exception_rows="\n".join(serialized_exception_samples)
                )
                if len(serialized_exception_samples) > 0
                else ""
            )

            # Write general information
            model_settings = self._get_model_settings()
            f.write(
                self.draft_generation_templates["general_info_template"].format(
                    exceptions_section=exceptions_section,
                    prompt=html.escape(self._get_system_prompt_config()).replace("\n", "<br>"),
                    model=json.dumps(model_settings, indent=4),
                )
            )
            f.write(self.common_templates["html_template_end"])

    def _print_sample_row(self, f: t.TextIO, eval: DraftGenerationEval, formatted_errors: list[str], additional_details: str = ""):
        """Print a single sample row to the file."""
        # determine which of the used activities were not proposed activities
        proposed_activities = {a["fullClassName"] for a in eval.proposed_activities}
        used_activities = {a for a in eval.ground_truth_activities}
        missed_activities = used_activities - proposed_activities

        # sometimes the demonstrations are not present, such as when we are comparing 2 eval results
        serialized_demonstrations: list[str] = (
            [self.serialize_demonstration(demonstration) for demonstration in eval.demonstrations] if eval.demonstrations else []
        )

        raw_gt_wf = self.serialize_workflow(eval.solution_workflow)
        existing_wf = self.serialize_workflow(eval.existing_workflow_sequence)
        raw_gen_wf = self.serialize_workflow(eval.raw_workflow)
        processed_draft_workflow = self.serialize_workflow(eval.processed_draft_workflow)
        error_text = "<hr>".join(formatted_errors)

        # format the TED nodes mapping
        formatted_ted_nodes_mapping = self._format_ted_nodes_mapping(eval.ted_nodes_mapping)

        f.write(
            self.draft_generation_templates["sample_row_template"].format(
                query=eval.query,
                elapsed_time=eval.elapsed_time,
                gen_wf=raw_gen_wf,
                gen_wf_processed=processed_draft_workflow,
                gt_wf=raw_gt_wf,
                existing_workflow=existing_wf,
                lev_score=eval.lev_score,
                lev_score_no_extras=eval.lev_score_no_extras,
                activity_tree_accuracy=eval.activity_tree_accuracy,
                ted_score=eval.ted_score,
                ted_levenstein_params_score=eval.ted_levenstein_params_score,
                levenstein_params_score=eval.levenstein_params_score,
                ted_exact_params_score=eval.ted_exact_params_score,
                exact_params_score=eval.exact_params_score,
                demonstrations="\n".join(serialized_demonstrations),
                missed_activities="<br>".join(missed_activities),
                token_usage=json.dumps(eval.token_usage, indent=4),
                post_generation_errors=error_text,
                ted_nodes_mapping=formatted_ted_nodes_mapping,
                prompt=html.escape(eval.prompt),
                raw_model_prediction=html.escape(eval.raw_model_prediction),
                tld_score=eval.tld_score if eval.tld_score is not None else -1,
                additional_details=additional_details,
            )
        )

    def _get_formatted_errors(self, eval: DraftGenerationEval) -> list[str]:
        """Get formatted error strings for the given evaluation result. Returns empty list if no errors."""
        post_generation_errors = eval.post_generation_errors or []

        # exclude undefined variable errors and invalid connection errors
        post_generation_errors = [error for error in post_generation_errors if not isinstance(error, (UndefinedVariableError, InvalidConnectionError))]

        if len(post_generation_errors) == 0:
            return []

        formatted_post_generation_errors = [self._format_generation_errors(error) for error in post_generation_errors]
        # add the index to the formatted post generation errors
        formatted_post_generation_errors = [f"{i + 1}. {error}" for i, error in enumerate(formatted_post_generation_errors)]

        return formatted_post_generation_errors

    def _format_exception_row(self, eval: DraftGenerationEval, formatted_errors: list[str]) -> str:
        """Create an exception template string for the given evaluation result and formatted errors"""
        error_text = "<hr>".join(formatted_errors)
        raw_gt_wf = self.serialize_workflow(eval.solution_workflow)
        raw_gen_wf = self.serialize_workflow(eval.raw_workflow)

        return self.draft_generation_templates["errors_row_template"].format(
            query=eval.query,
            exceptions=error_text,
            gt_wf=raw_gt_wf,
            gen_wf_processed=raw_gen_wf,
        )

    def _print_averages_row(self, f: t.TextIO, eval_results: list[DraftGenerationEval]):
        """Print the averages row to the file"""
        f.write(
            self.draft_generation_templates["averages_row_template"].format(
                elapsed_time=np.mean([eval.elapsed_time for eval in eval_results]),
                lev_score=np.mean([eval.lev_score for eval in eval_results]),
                lev_score_no_extras=np.mean([eval.lev_score_no_extras for eval in eval_results]),
                activity_tree_accuracy=np.mean([eval.activity_tree_accuracy for eval in eval_results]),
                ted_score=np.mean([eval.ted_score for eval in eval_results]),
                ted_levenstein_params_score=np.mean([eval.ted_levenstein_params_score for eval in eval_results]),
                levenstein_params_score=np.mean([eval.levenstein_params_score for eval in eval_results]),
                ted_exact_params_score=np.mean([eval.ted_exact_params_score for eval in eval_results]),
                exact_params_score=np.mean([eval.exact_params_score for eval in eval_results]),
                tld_score=np.mean(tld_scores) if len(tld_scores := [eval.tld_score for eval in eval_results if eval.tld_score is not None]) > 0 else -1,
            )
        )

    @abstractmethod
    def serialize_demonstration(self, demonstration) -> str:
        raise NotImplementedError()

    @abstractmethod
    def serialize_workflow(self, workflow) -> str:
        raise NotImplementedError()

    @staticmethod
    def _format_generation_errors(error: WorkflowProcessingError) -> str:
        processed_segments = []
        current_segment = ""
        for segment in error.workflow_path:
            if segment.segment_type == "activity":
                # if we have a current segment, we need to add it to the processed segments and reset it
                if current_segment != "":
                    processed_segments.append(current_segment)
                current_segment = segment.name
            elif segment.segment_type == "property":
                # add the property to the current activity segment
                current_segment += f".{segment.name}"

        if current_segment != "":
            processed_segments.append(current_segment)

        path = "   =>   ".join(processed_segments) if processed_segments else "ROOT"
        return html.escape(f"{path}: {BaseDraftEvalService._extract_error_specific_info(error)} {error.type.name}")

    @staticmethod
    def _extract_error_specific_info(error: WorkflowProcessingError) -> str:
        if isinstance(error, UndefinedVariableError):
            return f"{error.activity_identifier}: Property {error.param_name} has undefined variable {error.variable_name}."
        elif isinstance(error, InvalidExpressionError):
            return f"{error.activity_identifier}: Property {error.param_name} has invalid expression {error.expression}. Exception: {error.error}."
        elif isinstance(error, MissingParamError):
            return f"{error.activity_identifier}: Property {error.param_name} is hallucinated."
        elif isinstance(error, ParamValueMismatchError):
            return f"{error.activity_identifier}: Property {error.param_name} of type {error.param_category} has an incompatible value of type {error.value_category}."
        elif isinstance(error, MissingRequiredFieldError):
            return f"{error.activity_identifier}: Property {error.field_name} is missing."
        elif isinstance(error, ActivityError):
            return f"{error.activity_identifier}: Activity definition error."
        elif isinstance(error, InvalidConnectionError):
            return f"Unable to connect to '{error.connector_key}'. Please check that this connection is properly configured."
        elif isinstance(error, EditPatchStructureError):
            return f"Unsolved merge conflict: {error.error}"
        return ""

    @staticmethod
    def _format_ted_nodes_mapping(ted_nodes_mapping: list) -> str:
        """Format TED nodes mapping into a string with HTML escaped content."""
        return html.escape("\n".join([f"same {m[0]}" if same_activity_in_mapping(m) else f"distinct {m}" for m in ted_nodes_mapping[1:]]))

    def _get_model_settings(self):
        """Override this method to return the model settings used for evaluation"""
        raise NotImplementedError()

    def _get_system_prompt_config(self):
        """Override this method to return the system prompt configuration"""
        raise NotImplementedError()

    def create_comparison_report(
        self,
        first_eval_results: list[DraftGenerationEval] | pathlib.Path,
        second_eval_results: list[DraftGenerationEval] | pathlib.Path,
        evaluation_dir: pathlib.Path | None = None,
        output_file_name: str = "comparison_report.html",
    ):
        """
        Create a comparison report between two sets of evaluation results.

        Args:
            first_eval_dataset: First set of evaluation results (either list of DraftGenerationEval or path to YAML file)
            second_eval_dataset: Second set of evaluation results (either list of DraftGenerationEval or path to YAML file)
            output_file: Path to output the comparison report
        """
        # create the log file path
        evaluation_dir = pathlib.Path(evaluation_dir or os.getcwd())
        assert evaluation_dir.is_dir(), f"Output folder path {evaluation_dir} is not a directory"
        log_file = evaluation_dir / f"{output_file_name}_{time.strftime('%Y%m%d-%H%M%S')}.html"

        # Handle YAML file paths by deserializing them
        if isinstance(first_eval_results, pathlib.Path):
            first_eval_name = first_eval_results.stem
            first_eval_results = self._deserialize_draft_generation_eval_results(first_eval_results)
        else:
            first_eval_name = "First Results Set"
        if isinstance(second_eval_results, pathlib.Path):
            second_eval_name = second_eval_results.stem
            second_eval_results = self._deserialize_draft_generation_eval_results(second_eval_results)
        else:
            second_eval_name = "Second Results Set"

        # Create query to result mapping for both lists
        first_eval_map = {eval.query: eval for eval in first_eval_results}
        second_eval_map = {eval.query: eval for eval in second_eval_results}

        # Find common queries
        common_queries = set(first_eval_map.keys()) & set(second_eval_map.keys())

        # Separate results based on TED score comparison
        first_dataset_better_results: list[tuple[DraftGenerationEval, DraftGenerationEval]] = []  # Items from A with smaller TED than B
        second_dataset_better_results: list[tuple[DraftGenerationEval, DraftGenerationEval]] = []  # Items from B with smaller TED than A

        for query in common_queries:
            eval_a = first_eval_map[query]
            eval_b = second_eval_map[query]

            # Skip if TED score differences are smaller than 0.03
            if abs(eval_a.ted_score - eval_b.ted_score) < 0.03:
                continue

            if eval_a.ted_score < eval_b.ted_score:
                first_dataset_better_results.append((eval_a, eval_b))
            else:
                second_dataset_better_results.append((eval_b, eval_a))

        # Generate the comparison report
        with open(log_file, "w") as f:
            f.write(self.common_templates["html_template_start"])
            f.write(self.draft_generation_templates["comparison_body_template_start"])

            # First table: Samples where the first dataset has weaker results
            self._print_comparison_table(f, f"Weaker Results In {first_eval_name}", first_dataset_better_results)

            # Second table: Samples where the second dataset has better results
            self._print_comparison_table(f, f"Weaker Results In {second_eval_name}", second_dataset_better_results)

            f.write(self.common_templates["html_template_end"])

    @staticmethod
    def _deserialize_draft_generation_eval_results(yaml_file_path: pathlib.Path) -> list[DraftGenerationEval]:
        """
        Deserialize DraftGenerationEval results from a YAML file.
        """
        if not yaml_file_path.exists():
            raise FileNotFoundError(f"YAML file not found: {yaml_file_path}")

        try:
            yaml_data = yaml_load(yaml_file_path)
        except Exception as e:
            raise ValueError(f"Failed to load YAML file {yaml_file_path}: {e}")

        results_data = yaml_data["results"]
        if not isinstance(results_data, list):
            raise ValueError(f"Results in {yaml_file_path} is not a list")

        deserialized_results: list[DraftGenerationEval] = []
        for res in results_data:
            deserialized_result = DraftGenerationEval(**res)

            # TO DO: improve serialization/deserialization of demonstrations - for now we'll just ignore them
            deserialized_result.demonstrations = None

            # Simple polymorphic deserialization using Pydantic for the post generation errors
            deserialized_result.post_generation_errors = ERROR_SERIALIZATION_ADAPTER.validate_python(res["post_generation_errors"])

            deserialized_results.append(deserialized_result)

        return deserialized_results

    def _print_comparison_table(self, f: t.TextIO, header: str, eval_results: list[tuple[DraftGenerationEval, DraftGenerationEval]]):
        """Print a comparison table for the given evaluation results and return exception samples"""

        # sort the samples from the biggest difference, to the smallest
        eval_results.sort(key=lambda x: abs(x[0].ted_score - x[1].ted_score), reverse=True)

        f.write(self.draft_generation_templates["header_template"].format(header=header))
        f.write(self.draft_generation_templates["sample_table_header_template"])

        if len(eval_results) == 0:
            # if there are no results, print an empty table placeholder row
            f.write(self.draft_generation_templates["empty_table_placeholder_row"])
            f.write(self.draft_generation_templates["sample_table_end"])
            return

        # collect all samples with critique data or exceptions
        serialized_exception_samples: list[str] = []

        for original_eval, comparison_val in eval_results:
            # format the comparison_val generation details and add them to the original_eval row
            alternate_generation_details = self.draft_generation_templates["comparison_details_template"].format(
                ted_score=comparison_val.ted_score,
                ted_score_diff=abs(original_eval.ted_score - comparison_val.ted_score),
                ted_nodes_mapping=self._format_ted_nodes_mapping(comparison_val.ted_nodes_mapping),
            )
            formatted_errors = self._get_formatted_errors(original_eval)
            self._print_sample_row(f, original_eval, formatted_errors, alternate_generation_details)

            if len(formatted_errors) > 0:
                exception_sample = self._format_exception_row(original_eval, formatted_errors)
                serialized_exception_samples.append(exception_sample)

        f.write(self.draft_generation_templates["sample_table_end"])

        # Write exceptions section if there are any
        if len(serialized_exception_samples) > 0:
            f.write(
                self.common_templates["parsing_exceptions_section_template"].format(
                    exception_count=len(serialized_exception_samples), exception_rows="\n".join(serialized_exception_samples)
                )
            )
