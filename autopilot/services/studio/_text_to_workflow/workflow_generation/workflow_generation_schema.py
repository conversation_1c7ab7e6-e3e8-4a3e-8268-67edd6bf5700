import time
from enum import Enum
from pathlib import Path
from typing import Any, List, Optional, TypedDict

import numpy as np
import typing_extensions as t
from pydantic import BaseModel, Field

from services.studio._text_to_workflow.common.schema import (
    ActivitiesGenerationMode,
    ActivityDefinition,
    Argument,
    Connection,
    PlanStep,
    SubsetName,
    TargetFramework,
    UIObject,
    Variable,
    WorkflowDict,
)
from services.studio._text_to_workflow.common.workflow import Activity
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage, TokenUsageJson
from services.studio._text_to_workflow.utils.request_schema import BasePydanticRequest, BaseRequest, BaseResponse, ModelOptions

SegmentType: t.TypeAlias = t.Literal["activity", "property"]


class WorkflowProcessingErrorType(Enum):
    INVALID_TRIGGER_STRUCTURE = "INVALID_TRIGGER_STRUCTURE"
    MISSING_TRIGGER_TYPE = "MISSING_TRIGGER_TYPE"
    TRIGGER_TYPE_NOT_STRING = "TRIGGER_TYPE_NOT_STRING"
    INVALID_STRUCTURE = "INVALID_STRUCTURE"
    INVALID_SINGLE_QUOTES_PLACEMENT = "INVALID_SINGLE_QUOTES_PLACEMENT"
    TRIGGER_DOES_NOT_EXIST = "TRIGGER_DOES_NOT_EXIST"
    TRIGGER_USED_IN_WORKFLOW = "TRIGGER_USED_IN_WORKFLOW"
    ACTIVITY_DOES_NOT_EXIST = "ACTIVITY_DOES_NOT_EXIST"
    UNEXPECTED_ACTIVITY_USED = "UNEXPECTED_ACTIVITY_USED"
    INVALID_PARAMS_STRUCTURE = "INVALID_PARAMS_STRUCTURE"
    MISSING_PARAM = "MISSING_PARAM"
    UNDEFINED_VARIABLE = "UNDEFINED_VARIABLE"
    PARAM_VALUE_MISSMATCH = "PARAM_VALUE_MISSMATCH"
    INVALID_EXPRESSION = "INVALID_EXPRESSION"
    MISSING_OR_INVALID_CONNECTION = "MISSING_OR_INVALID_CONNECTION"
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"
    EDIT_PATCH_STRUCTURE_ERROR = "EDIT_PATCH_STRUCTURE_ERROR"
    UNKNOWN = "UNKNOWN"


WORKFLOW_PROCESSING_ERROR_SEVERITY = {
    0: [WorkflowProcessingErrorType.EDIT_PATCH_STRUCTURE_ERROR],
    1: [
        WorkflowProcessingErrorType.ACTIVITY_DOES_NOT_EXIST,
        WorkflowProcessingErrorType.TRIGGER_DOES_NOT_EXIST,
        WorkflowProcessingErrorType.TRIGGER_USED_IN_WORKFLOW,
        WorkflowProcessingErrorType.INVALID_STRUCTURE,
        WorkflowProcessingErrorType.INVALID_TRIGGER_STRUCTURE,
    ],
    2: [
        WorkflowProcessingErrorType.TRIGGER_TYPE_NOT_STRING,
        WorkflowProcessingErrorType.MISSING_PARAM,
        WorkflowProcessingErrorType.INVALID_PARAMS_STRUCTURE,
        WorkflowProcessingErrorType.MISSING_TRIGGER_TYPE,
    ],
    3: [WorkflowProcessingErrorType.MISSING_REQUIRED_FIELD, WorkflowProcessingErrorType.PARAM_VALUE_MISSMATCH],
    4: [WorkflowProcessingErrorType.UNDEFINED_VARIABLE, WorkflowProcessingErrorType.INVALID_EXPRESSION, WorkflowProcessingErrorType.UNEXPECTED_ACTIVITY_USED],
}


class ProjectFile(t.TypedDict):
    path: str
    description: t.NotRequired[str | None]
    arguments: t.NotRequired[list[Argument]]


class GenerateWorkflowRequest(BaseRequest):
    userRequest: str
    targetFramework: TargetFramework
    connections: list[Connection]


class GenerateSequenceRequest(GenerateWorkflowRequest):
    objects: t.NotRequired[list[UIObject]]
    workflowName: str
    workflowDescription: t.NotRequired[str | None]
    projectDescription: t.NotRequired[str | None]
    isTestCase: bool
    availableVariables: list[Variable]
    availableWorkflows: t.NotRequired[list[ProjectFile] | None]
    availableAdditionalTypeDefinitions: str
    currentWorkflow: str


# We require Pydantic models because UIObject is a self-referencing TypedDict.
# Pydantic models resolve the forward references internally.
# This does not happen in the case of FastAPI conversion of TypedDict.
class GenerateWorkflowRequestPydantic(BasePydanticRequest):
    userRequest: str
    targetFramework: TargetFramework
    connections: list[Connection]


class GenerateSequenceRequestPydantic(GenerateWorkflowRequestPydantic):
    objects: Optional[list[UIObject]] = Field(default_factory=list)
    workflowName: str
    workflowDescription: Optional[str | None] = None
    projectDescription: Optional[str | None] = None
    isTestCase: bool
    availableVariables: list[Variable]
    availableWorkflows: Optional[list[ProjectFile] | None] = Field(default_factory=list)
    availableAdditionalTypeDefinitions: str
    currentWorkflow: str


class WorkflowGenerationDuration:
    planning: float
    demonstrations_retrieval: float
    metadata_retrieval: float
    generation: float
    post_processing: float
    init: float

    def __init__(self):
        self.init: float = 0
        self.planning: float = 0
        self.demonstrations_retrieval: float = 0
        self.metadata_retrieval: float = 0
        self.generation: float = 0
        self.post_processing: float = 0
        self._last_checkpoint: float = time.time()

    def set_checkpoint(self, key: str) -> None:
        """Update the duration for the given key and set a new checkpoint."""
        now = time.time()
        setattr(self, key, now - self._last_checkpoint)
        self._last_checkpoint = now

    def to_dict(self) -> dict[str, float]:
        return {
            "init": self.init,
            "planning": self.planning,
            "demonstrations_retrieval": self.demonstrations_retrieval,
            "metadata_retrieval": self.metadata_retrieval,
            "generation": self.generation,
            "post_processing": self.post_processing,
        }


class WorkflowGenerationResult(t.TypedDict):
    is_response_valid_yaml: bool
    workflow_valid: str
    workflow_raw: str
    used_jit_types: list[dict[str, str | None]]
    used_connectors: list[dict[str, str | None]]
    used_trigger: str | None
    used_activities: list[str]
    used_packages: list[str]
    used_variables: list[Variable]
    used_local_variables: list[Variable]
    used_namespaces: list[str]


class WorkflowGenerationTaskResult(t.TypedDict):
    plan: str
    plan_whole: str  # for sequence generation
    steps: list["PlanStep"]
    workflow_result: WorkflowGenerationResult
    sequence_result: WorkflowGenerationResult  # for sequence generation
    retrieved_triggers: list[ActivityDefinition]
    retrieved_activities: list[ActivityDefinition]
    retrieved_triggers_after_pruning: list[ActivityDefinition]
    retrieved_activities_after_pruning: list[ActivityDefinition]
    retrieved_packages: list[str]
    retrieval_prompt: str
    retrieval_usage: TokenUsage
    generation_prompt: str
    generation_usage: TokenUsage
    demonstrations: dict[SubsetName, list[dict]]
    duration: WorkflowGenerationDuration
    prompt_class: str | None
    prompt_score: int | None


class WorkflowGenerationUsage(t.TypedDict):
    retrieval: TokenUsageJson
    generation: TokenUsageJson


class WorkflowGenerationResponse(BaseResponse):
    result: str
    jitCommands: list[dict[str, Any]]
    connectors: list[dict[str, Any]]
    usage: t.NotRequired[WorkflowGenerationUsage | None]
    prompt_class: t.NotRequired[str | None]
    prompt_score: t.NotRequired[int | None]
    retrieved_triggers: t.NotRequired[list[str]]
    retrieved_activities: t.NotRequired[list[str]]
    retrieved_triggers_after_pruning: t.NotRequired[list[str]]
    retrieved_activities_after_pruning: t.NotRequired[list[str]]
    demonstrations: t.NotRequired[list[object]]
    plan: t.NotRequired[str]
    duration: t.NotRequired[dict[str, float]]


class FixWorkflowYamlRequest(t.TypedDict):
    workflow: str
    errors: str


class FixWorkflowYamlResponse(t.TypedDict):
    fixed_workflow: dict
    prompt: str


class DemoRetrieverState(t.TypedDict):
    similarities: np.ndarray
    embeddings: np.ndarray
    filepath2index: dict[Path, int]
    index2filepath: dict[int, Path]


class WorkflowQueryEvalRequest(t.TypedDict):
    query: str


class WorkflowGenerationParams(t.TypedDict):
    localization: str | None
    model: dict[str, ModelOptions] | None


class WorkflowActivityDetails(t.TypedDict):
    trigger_full_name: t.Optional[str]
    trigger: t.Optional[Activity]
    activities: dict[str, Activity]


class ProposedActivity(TypedDict):
    type_full_name: str
    display_name: str
    description: str
    category: str
    id: int
    connectorKey: str | None
    namespace: str


class WfGenDataPointV2(TypedDict):
    target_framework: TargetFramework
    mode: ActivitiesGenerationMode
    query: str
    original_query: str  # the original, non-revised query. Should be of inferior quality than the query and might be useful for evals or debugging
    plan: str  # planning details - might be useful to use as thinking tokens
    thinking: str  # currently, we only use it as thinking tokens when generating the dataset
    ambiguities: str | None
    score: int
    # activities that are actually used to build the workflow for the current dataset
    used_activities: list[str]
    used_triggers: list[str]
    existing_workflow_sequence: WorkflowDict | None
    solution_workflow: WorkflowDict

    # for evaluation
    seed: int | None


class UndocumentedHttpRequest(BaseModel):
    name: str = Field(description="The fictional activity type name you intended to retrieve, but which does not exist.", max_length=100)
    description: str = Field(description="The description of the desired activity, which was not retrieved", max_length=200)
    provider: str = Field(description="The provider of the desired activity, which was not retrieved")


class ActivityRetrievalGenerationBase(BaseModel):
    plan: str = Field(description="How would you plan to solve the user query?", default="")
    ambiguities: str | None = Field(description="What are the ambiguities in the query? What is missing?")
    score: int = Field(
        description="A score equal to -1 or between 0 and 100, representing how much sense the query makes for an automation and whether it has many ambiguities. -1 is only used when the query could never be translated into an automation."
    )
    activities: list[int] = Field(
        description="The ids of the activities relevant for building the workflow described by the query. Must only contain int values representing ids."
    )
    undocumentedHttpRequests: list[UndocumentedHttpRequest] | None = Field(
        description="List of fictional operations that you would like to make, but do not have an activity for. Consider the ambiguities in the query and the activities that you have retrieved to generate the most relevant HTTP request operations. You MUST generate these operations for all the operations that you would need to solve the user query, but do not have activities for.",
        default=None,
    )
    inexistentActivities: list[str] | None = Field(
        description="List fictional activity type names that you would have liked to have retrieved, but which do not exist. If you think you did a good job, this should be empty.",
        default=None,
    )


class ActivityRetrievalGeneration(ActivityRetrievalGenerationBase):
    triggers: list[int] = Field(description="The ids of the triggers relevant for deciding when the workflow should be run.")
    inexistentTriggers: list[str] | None = Field(
        description="List fictional trigger type names that you would have liked to have retrieved, but which do not exist. If you think you did a good job, this should be empty.",
        default=None,
    )


class PathSegment(BaseModel):
    """A segment of the path to a node in the workflow tree"""

    segment_type: SegmentType = Field(description="The type of the segment")
    name: str = Field(description="The name of the segment")


class WorkflowProcessingError(BaseModel):
    """Error encountered during the post-processing of a generated workflow"""

    workflow_path: list[PathSegment] = Field(description="The path to the node in the workflow tree that caused the error")
    type: WorkflowProcessingErrorType = Field(description="The type of the error")


class EditPatchStructureError(WorkflowProcessingError):
    type: WorkflowProcessingErrorType = Field(default=WorkflowProcessingErrorType.EDIT_PATCH_STRUCTURE_ERROR, description="The type of the error")
    error: str = Field(description="The error message")
    workflow_path: list[PathSegment] = Field(default=[])


class YamlStructureError(WorkflowProcessingError):
    """Yaml structure error"""

    pass


class ActivityError(WorkflowProcessingError):
    """Error encountered during the activity definition post-processing"""

    activity_identifier: str = Field(description="The identifier of the activity that caused the error")


class InvalidConnectionError(ActivityError):
    """Error encountered during when using a connection during post-processing"""

    connector_key: str = Field(description="The key of the connector that caused the error")
    type: WorkflowProcessingErrorType = Field(default=WorkflowProcessingErrorType.MISSING_OR_INVALID_CONNECTION, description="The type of the error")
    class_name: str = Field(description="The class name of the activity that caused the error")
    category: str = Field(description="The connector category that caused the error")
    activity_name: str = Field(description="The name of the activity that caused the error")


class MissingParamError(ActivityError):
    param_name: str = Field(description="The name of the parameter that is missing")
    type: WorkflowProcessingErrorType = Field(default=WorkflowProcessingErrorType.MISSING_PARAM, description="The type of the error")


class MissingRequiredFieldError(ActivityError):
    field_name: str = Field(description="The name of the field that is missing")
    type: WorkflowProcessingErrorType = Field(default=WorkflowProcessingErrorType.MISSING_REQUIRED_FIELD, description="The type of the error")


class InvalidExpressionError(ActivityError):
    param_name: str = Field(description="The name of the parameter that is missing")
    expression: str = Field(description="The expression that caused the error")
    error: str = Field(description="The error message")
    evaluated_expression: str = Field(description="The expression that was evaluated")
    type: WorkflowProcessingErrorType = Field(default=WorkflowProcessingErrorType.INVALID_EXPRESSION, description="The type of the error")


class UndefinedVariableError(ActivityError):
    param_name: str = Field(description="The name of the parameter that is missing")
    variable_name: str = Field(description="The name of the variable that is undefined")
    type: WorkflowProcessingErrorType = Field(default=WorkflowProcessingErrorType.UNDEFINED_VARIABLE, description="The type of the error")


class ParamValueMismatchError(ActivityError):
    """Param found with a miss-matching value type"""

    param_name: str = Field(description="The name of the parameter that is missing")
    value: t.Any = Field(description="The value that caused the error")
    param_category: str = Field(description="The category of the parameter")
    value_category: str = Field(description="The category of the value")
    type: WorkflowProcessingErrorType = Field(default=WorkflowProcessingErrorType.PARAM_VALUE_MISSMATCH, description="The type of the error")


class ParsedWorkflowDetails:
    """Results of the post-processing of a generated workflow. Includes validation errors"""

    def __init__(
        self,
        workflow_generation: WorkflowGenerationResult,
        post_generation_errors: list[WorkflowProcessingError],  # any errors encountered while doing a post processing of the generated workflow
    ):
        self.workflow_generation = workflow_generation
        self.post_generation_errors = post_generation_errors


class AgentRetrievalGeneration(BaseModel):
    """Model for the LLM's response when determining relevant agents."""

    reasoning: str = Field(description="Explanation for why these agents were selected")
    ambiguities: str = Field(description="Any ambiguities or uncertainties in the agent selection")
    relevant_agents: List[int] = Field(description="List of relevant agent IDs")
