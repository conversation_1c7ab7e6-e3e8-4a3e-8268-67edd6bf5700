import argparse
import asyncio
import json
import pathlib

import requests
import tempenv

import services.studio._text_to_workflow.utils.dotnet_dynamic_activities_discovery as ddad
from services.studio._text_to_workflow.common import connections_loader, helpers, typedefs
from services.studio._text_to_workflow.common.schema import ActivitiesGenerationMode, SubsetName, TargetFramework
from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.utils import paths, request_utils
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump
from services.studio._text_to_workflow.workflow_generation import workflow_generation_test
from services.studio._text_to_workflow.workflow_generation.workflow_generation_helpers import (
    load_predefined_additional_type_definitions,
    load_predefined_variables_from_dump,
    load_predefined_workflow_from_dump,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_task import run_workflow_generation_build


async def run_workflow_generation_test(
    query: str | None,
    target_framework: TargetFramework,
    subsets: list[SubsetName],
    retrieval_model: str | None,
    generation_model: str | None,
    mode: ActivitiesGenerationMode,
    localization: str = "en",
    log_file: str | None = None,
    **kwargs,
):
    dataset_connections_path = paths.get_dataset_connections_cache_path()
    ddad.load_cached_connections(dataset_connections_path)
    connections = connections_loader.get_connections_data()

    request_context = get_testing_request_context(localization, client_name="Workflow Generation Evaluation")

    request_utils.set_request_context(request_context)

    ted_scores, elapsed_times = await workflow_generation_test.run(
        query=query,
        target_framework=target_framework,
        subsets=subsets,
        connections=connections,
        retrieval_model_name=retrieval_model,
        generation_model_name=generation_model,
        mode=mode,
        localization=localization,
        log_file=log_file,
        **kwargs,
    )

    return ted_scores, elapsed_times


def parse_additional_context(args: argparse.Namespace) -> dict:
    additional_context = {}
    if args.request_dump is not None:
        content = args.request_dump.read_text()
        assert content[:4] == "POST"
        _headers, content = content.split("\n\n", 1)
        request_dump = json.loads(content)

        additional_context["predefined_current_workflow"] = request_dump["currentWorkflow"]
        additional_context["predefined_variables"] = request_dump["availableVariables"]
        additional_context["predefined_additional_type_definitions"] = request_dump["availableAdditionalTypeDefinitions"]
        additional_context["query"] = request_dump["userRequest"]
        additional_context["target_framework"] = request_dump["targetFramework"]
        additional_context["predefined_connections"] = request_dump["connections"]

        # TODO: these are not passed
        additional_context["objects"] = request_dump["objects"]
        additional_context["description"] = request_dump["workflowDescription"]
        return additional_context

    if args.current_workflow:
        additional_context["predefined_current_workflow"] = yaml_dump(load_predefined_workflow_from_dump(args.current_workflow))
    if args.current_variables:
        additional_context["predefined_variables"] = load_predefined_variables_from_dump(args.current_variables)
    if args.current_additional_type_defs:
        additional_context["predefined_additional_type_definitions"] = load_predefined_additional_type_definitions(args.current_additional_type_defs)
    if args.current_connections:
        additional_context["predefined_connections"] = json.loads(args.current_connections.read_text())
    return additional_context


def send_request_to_server(request_dump_path: pathlib.Path, server: str, port: int, mode: str = "workflow"):
    """
    `request_dump_path` can contain either
    - a dump of the entire request: "POST...\n\n{..." (having the headers, as well as the body)
    - a json: containing the request body only "{..." (url to be inferred based on the other args)
    """
    content = args.request_dump.read_text()
    relative_url = "v2/generate-workflow" if mode == "workflow" else "v2/generate-sequence"
    if content[:4] == "POST":
        headers, content = content.split("\n\n", 1)
        _, url, _ = headers.split(" ", 2)
        _, version, endpoint = url.rsplit("/", 2)
        relative_url = f"{version}/{endpoint}"

    url = f"{server}:{port}/{relative_url}"
    headers = {"Authorization": settings.UIPATH_TOKEN}  # TODO: more headers such as localization can be added
    body = json.loads(content)
    print(f"Sending request to {url}")
    response = requests.post(url, headers=headers, json=body)
    response.raise_for_status()
    response = response.json()
    print("-" * 80)
    print(json.dumps(response, indent=2))
    print("-" * 80)

    if "result" in response:
        print(response["result"])
        print("-" * 80)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("job", default="test", choices=["build", "test", "bt", "request"])
    args, _unk = ap.parse_known_args()
    if args.job == "request":
        ap.add_argument("--request-dump", "--request_dump", type=pathlib.Path, help="Path to the request dump to be used for testing")
        ap.add_argument("--server", type=str, default="http://localhost")
        ap.add_argument("--port", type=int, default=settings.PORT)
        ap.add_argument("--mode", type=str, default="workflow", choices=["workflow", "sequence"])
        args = ap.parse_args()
        send_request_to_server(args.request_dump, args.server, args.port)
        exit()

    ap.add_argument("--query", type=str, default=None)  # TODO: we should ideally split this into separate query / dataset based tests
    ap.add_argument("--target-framework", "--target_framework", type=str, default=None, choices=["Portable", "Windows"])
    ap.add_argument("--subsets", "--subset", nargs="*", type=str, default=["test"], choices=["test", "train"])
    ap.add_argument("--mode", type=str, default="workflow", choices=["workflow", "sequence"])

    # alternative evaluation setup
    ap.add_argument(
        "--evaluation-index",
        "--evaluation_index",
        type=pathlib.Path,
        default=None,
        help="Path to a file containing absolute paths to evaluation datapoints. Use absolute paths to dataset yamls inside.",
    )
    ap.add_argument(
        "--evaluation-cadence", "--evaluation_cadence", type=str, default=None, help="Cadence to use on dataset. Can be 1/5 to eval every 1st/5th example, etc."
    )
    ap.add_argument("--evaluation-glob", "--evaluation_glob", type=str, default=None, help="Path glob to include only specific examples for evaluation.")
    ap.add_argument(
        "--evaluation-key",
        "--evaluation_key",
        type=str,
        default=None,
        help="Key to use for evaluation. Defaults to `subset.yaml` file if no `evaluation-index` is provided.",
    )

    # check studio/text_to_workflow/workflow_generation/config/prompt.yaml for choices of models
    ap.add_argument("--retrieval-model", "--retrieval_model", "--model", type=str, default=None)
    ap.add_argument("--generation-model", "--generation_model", type=str, default=None)
    ap.add_argument("--seed", type=int, default=None, help="run in deterministic mode with given seed")

    # specific arguments
    ap.add_argument("--localization", type=str, default=None)
    ap.add_argument("--limit", type=int, default=None, help="Limit the number of examples to run")
    ap.add_argument("--nogeneration", action="store_true", help="Use generation for the test")
    ap.add_argument("--use-ideal-plan", "--use_ideal_plan", action="store_true", help="Use grouthtruth plan of the dataset")

    # other
    ap.add_argument("--replication", type=int, default=1, help="Number of times to run the test")
    ap.add_argument("--token", type=str, default=None, help="Token to be used for testing. If not provided, it will be inferred internally.")
    ap.add_argument("--log-file", "--log_file", type=str, default=None, help="path to json file to write log")
    ap.add_argument("--results-file", "--results_file", type=pathlib.Path, default=None, help="path to html file where to dump evaluation.")
    ap.add_argument("--notify-slack", "--notify_slack", action="store_true", help="Notify on slack after termination. Requires `SLACK_WEBHOOK_URL` to be set.")

    args, unk = ap.parse_known_args()
    if args.mode == "workflow":
        args = ap.parse_args()  # reparse to get specific errors for invalid/missing/extra args

    def parse_evaluation_cadence(provided: str | None) -> tuple[int, int] | None:
        """Parses "3/5" or "4" and returns (3, 5) and (1, 4) respectively"""
        if provided is None:
            return None
        if "/" in provided:
            a, b = map(int, provided.split("/"))
            assert 0 < a <= b, f"Invalid evaluation cadence: {provided}"
            return (a, b)
        return (1, int(provided))

    forwarded_kwargs = dict(
        localization=args.localization,
        limit=args.limit,
        run_generation=not args.nogeneration,
        use_ideal_plan=args.use_ideal_plan,
        seed=args.seed,
        token=args.token,
        evaluation_index=args.evaluation_index,
        evaluation_cadence=parse_evaluation_cadence(args.evaluation_cadence),
        evaluation_glob=args.evaluation_glob,
        evaluation_key=args.evaluation_key,
    )
    if args.mode == "sequence":
        ap.add_argument("--force-sequence-generation-entire-sequences", action="store_true", help="Generate everything for sequence generation")

        # sequence generation additional context
        ap.add_argument("--current-workflow", "--current_workflow", default=None, type=pathlib.Path, help="Path to the current workflow to be used for testing")
        ap.add_argument(
            "--current-variables", "--current_variables", default=None, type=pathlib.Path, help="Path to the current variables to be used for testing"
        )
        ap.add_argument(
            "--current-connections", "--current_connections", default=None, type=pathlib.Path, help="Path to the current connections to be used for testing"
        )
        ap.add_argument(
            "--current-additional-type-defs",
            "--current_additional_type_defs",
            default=None,
            type=pathlib.Path,
            help="Path to the current additional type definitions to be used for testing",
        )
        ap.add_argument(
            "--request-dump",
            "--request_dump",
            default=None,
            type=pathlib.Path,
            help="Path to the request dump to be used for testing. "
            "Overwrites all the current workflow, variables, additional type definitions, query, target framework",
        )

        args = ap.parse_args()
        additional_context = parse_additional_context(args)
        forwarded_kwargs.update(
            force_sequence_generation_entire_sequences=args.force_sequence_generation_entire_sequences,
            predefined_current_workflow=additional_context.get("predefined_current_workflow"),
            predefined_variables=additional_context.get("predefined_variables"),
            predefined_additional_type_definitions=additional_context.get("predefined_additional_type_definitions"),
            predefined_connections=additional_context.get("predefined_connections"),
        )
        args.query = args.query or additional_context.get("query", args.query)
        args.target_framework = args.target_framework or additional_context.get("target_framework", args.target_framework)

    if args.job == "build" or args.job == "bt":
        run_workflow_generation_build()
        # ddad.load_cached_connections(paths.get_dataset_connections_cache_path())  # this could be omitted if the lazy load functions properly
        with tempenv.TemporaryEnvironment(dict(USE_CACHED_CONNECTIONS="true" if args.job == "bt" else "false")):
            asyncio.run(typedefs.build(show_progress_bar=True))
    if args.job == "test" or args.job == "bt":
        typedefs.load()

        async def _run():
            for replication_index in range(args.replication):
                results_file = args.results_file
                if args.replication != 1:
                    results_file = results_file.with_name(f"{results_file.stem}-{replication_index}{results_file.suffix}")
                with helpers.Tee(results_file):
                    await run_workflow_generation_test(
                        query=args.query,
                        target_framework=args.target_framework,
                        subsets=args.subsets,
                        retrieval_model=args.retrieval_model,
                        generation_model=args.generation_model,
                        mode=args.mode,
                        log_file=args.log_file,
                        **forwarded_kwargs,
                    )

        asyncio.run(_run())

    if args.notify_slack:
        try:
            from experimental.utils.slack import send_slack_notification

            send_slack_notification(args)
        except Exception as e:
            print(f"Failed to send slack notification: {e}")
