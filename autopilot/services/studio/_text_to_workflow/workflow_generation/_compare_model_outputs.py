# run all the eval samples and compare the results with the expected results.
import itertools
import json
import os
import pathlib

from services.studio._text_to_workflow.common import connections_loader
from services.studio._text_to_workflow.utils import uipath_cloud_platform
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset, workflow_generation_task

workflow_generation = workflow_generation_task.WorkflowGenerationTask("prompt.yaml").load()
testset: dict[pathlib.Path, dict] = workflow_generation_dataset.load_workflow_generation_subset("Portable", "test")
out_folder = pathlib.Path(__file__).parent / "test_output"

os.makedirs(out_folder, exist_ok=True)

token = uipath_cloud_platform.get_token_from_env()
connections = connections_loader.get_connections_data()

skip_if_exists = True

queries = []
for sample in testset.values():
    queries.append(sample["description"])

for model_name in ["default"]:  # for model_name in ["default", "gpt-35-turbo-16k", "gpt-4"]:
    for idx, (sample_path, sample) in enumerate(testset.items()):
        query = sample["description"]
        if skip_if_exists and os.path.exists(out_folder / f"{idx}_{model_name}.yaml"):
            continue

        ground_truth = yaml_load(sample_path)
        ground_truth_process = ground_truth["process"]

        yaml_dump(ground_truth_process, out_folder / f"{idx}_gt.yaml")

        print(f"⏭  Evaluating on model {model_name}: {query}")
        try:
            result = workflow_generation.run(
                query=query,
                variables=[],
                additional_type_definitions="",
                connections=connections,
                target_framework="Portable",
                objects=[],
                model_options={"planning": {"model_name": model_name}, "generation": {}} if model_name != "default" else None,
            )
            if result["workflow_result"]["workflow_valid"]:
                print("  ✅  Workflow is valid")
            else:
                print("  ❌  Workflow is not valid")

            with open(out_folder / f"{idx}_{model_name}.yaml", "w") as f:
                f.write(result["workflow_result"]["workflow_valid"])

            with open(out_folder / f"{idx}_{model_name}_raw.yaml", "w") as f:
                f.write(result["workflow_result"]["workflow_raw"])

            for step in result["steps"]:
                del step["embedding"]

            for demo in itertools.chain(*result["demonstrations"].values()):
                del demo["raw_scores"]
            with open(out_folder / f"result_{idx}_{model_name}.json", "w") as f:
                json.dump(result, f, indent=2)
        except OverflowError:
            print("  💥  Workflow could not be generated (Prompt Overflow)")

        print()
