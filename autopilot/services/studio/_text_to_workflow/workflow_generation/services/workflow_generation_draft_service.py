import asyncio
import copy
import pathlib
import typing as t

from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.prompt_values import PromptValue
from langchain_core.prompts.chat import Chat<PERSON>romptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate

from services.studio._text_to_workflow.activity_config import activity_config_demo_retriever
from services.studio._text_to_workflow.activity_config.activity_config_schema import ActivityConfigExample
from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.common.constants import BASIC_FULL_ACTIVITY_IDS
from services.studio._text_to_workflow.common.schema import (
    ActivitiesGenerationMode,
    ActivityDict,
    Connection,
    TargetFramework,
    UIObject,
    Variable,
)
from services.studio._text_to_workflow.common.walkers import SequenceGenerationGenericActivityReplacer
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.models.model_manager import <PERSON><PERSON>anager
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.activity_utils import build_activity_type_definitions, build_additional_type_definitions
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.request_utils import get_prompt_values_from_request_context
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.utils.unidiff_utils import get_merge_conflict, prepend_line_numbers
from services.studio._text_to_workflow.utils.workflow_utils import delete_sequence_thought
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation.services.base_generator_service import BaseDraftGeneratorService
from services.studio._text_to_workflow.workflow_generation.services.common.generation_settings_builder import GenerationSettingsBuilder
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import ActivityRetrievalResult
from services.studio._text_to_workflow.workflow_generation.services.helpers.draft_generation import DraftCreationDemonstration, DraftGenerationResult
from services.studio._text_to_workflow.workflow_generation.services.helpers.ignored_activities_helper import get_ignored_activities_map
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_activity_retrieval_service import (
    WorkflowGenerationActivityRetrievalService,
)
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_postprocess_component import WorkflowGenerationPostProcessComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_prompt_builder_component import WorkflowGenerationPromptBuilderComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import ParsedWorkflowDetails, WfGenDataPointV2

LOGGER = AppInsightsLogger()


class WorkflowGenerationDraftService(BaseDraftGeneratorService):
    draft_generation_model_name = "workflow_draft_generation_gemini_model"

    def __init__(
        self,
        activities_retriever: ActivitiesRetriever,
        postprocess_component: WorkflowGenerationPostProcessComponent,
        prompt_builder_component: WorkflowGenerationPromptBuilderComponent,
    ):
        # TO DO: this should be a parameter
        config_path = (pathlib.Path(__file__).parent.parent).absolute() / "config" / "draft_creation_prompt.yaml"
        self.prompt_config = yaml_load(config_path)
        self.token_config = self.prompt_config["token_config"]  # token size settings for the prompt

        # Initialize the base class with the token configuration
        super().__init__(
            model_name=self.draft_generation_model_name,
            consuming_feature_type=ConsumingFeatureType.WORKFLOW_GENERATION,
            retry_count=3,
        )

        self.activities_retriever = activities_retriever
        self.postprocess_component = postprocess_component
        self.prompt_builder_component = prompt_builder_component

    async def init_and_load(self):
        await activity_config_demo_retriever.init_and_load()

    async def generate_workflow_draft(
        self,
        query: str,
        workflow: t.Optional[Workflow],
        connections: list[Connection],
        variables: list[Variable],
        objects: list[UIObject],
        mode: ActivitiesGenerationMode,
        target_framework: TargetFramework,
        activity_retrieval_result: ActivityRetrievalResult,
        localization: str | None,
        additional_type_definitions: str = "",
        user_prompt_additional_instructions: str = "",
        seed: int | None = None,
    ) -> DraftGenerationResult:
        # just to make sure we're not using the wrong mode when presenting/prompting the model
        if workflow is not None and mode == "workflow":
            raise ValueError("Workflow mode does not support existing workflow")
        if workflow is None and mode in {"edit", "testcase", "sequence"}:
            raise ValueError("Workflow is required for edit, testcase, and sequence modes")

        model = self._initialize_model(
            query,
            self.token_config["completion_tokens_min_bound"],
            self.token_config["completion_tokens_max_bound"],
            self.token_config["token_per_query_character"],
            seed=seed,
        )

        activity_ids = set(activity_retrieval_result.retrieved_activities) | set(BASIC_FULL_ACTIVITY_IDS["activity"])
        activity_defs = [act for a in activity_ids if (act := self.activities_retriever.get(a, target_framework, "activity")) is not None]
        trigger_defs = []
        if mode == "workflow":
            trigger_ids = set(activity_retrieval_result.retrieved_triggers) | set(BASIC_FULL_ACTIVITY_IDS["trigger"])
            trigger_defs = [trig for t in trigger_ids if (trig := self.activities_retriever.get(t, target_framework, "trigger")) is not None]
        connections_by_key = {conn["connector"]: conn for conn in connections if "connector" in conn}

        chat_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(self.prompt_config["system_msg"]),
                HumanMessagePromptTemplate.from_template(self.prompt_config["user_msg_template"]),
            ]
        )

        # prepare the prompt params
        workflow_presentation, workflow_serialization = self._prepare_serialized_workflow_for_prompt(workflow, model, mode)

        # if we're in workflow generation mode, but we receive an existing workflow, we will perform a full rewrite
        prompt_mode = "full_rewrite" if mode == "workflow" and workflow is not None else mode
        prompt_values = (
            self._get_flair_specific_prompt_values(prompt_mode, self.prompt_config["flair_specific_sections"])
            | {
                "query": query,
                "activity_type_definitions": build_activity_type_definitions(activity_defs),
                "trigger_type_definitions": build_activity_type_definitions(trigger_defs),
                "additional_type_definitions": build_additional_type_definitions(activity_defs, additional_type_definitions),
                "variables": yaml_dump(variables).strip(),
                "demo_examples": "",  # will be populated later
                "patch_examples": "",  # will be populated later
                "activity_examples": "",  # will be populated later
                "existing_workflow_template": workflow_presentation,
                "additional_user_prompt_instructions": user_prompt_additional_instructions,
            }
            | get_prompt_values_from_request_context()
        )

        # TDB: we might want to move this outside the draft creation service, but we'll see when we integrate the V2 workflow generation pipeline
        # do not wait for this task to complete, it should run in parallel with the chat completion
        augmented_type_defs_task = WorkflowGenerationDynamicActivitiesComponent.augment_type_definitions_for_dynamic_activities(
            {activity["fullClassName"]: activity for activity in activity_defs},
            {trigger["fullClassName"]: trigger for trigger in trigger_defs},
            connections_by_key,
        )

        activity_demos, workflow_demos, patch_demos = self._get_draft_demonstrations(
            activity_ids, activity_retrieval_result.demonstrations, target_framework, mode
        )

        # determine how many demos we can fit and add them in the prompt
        available_tokens = model.max_total_tokens - model.max_model_tokens  # type: ignore
        formatted_workflow_demos = self._get_formatted_demonstrations(
            model,
            workflow_demos,
            self.prompt_config["demonstration_template"],
            available_tokens - model.get_num_tokens(chat_prompt.format(**prompt_values)),
        )
        prompt_values["demo_examples"] = "\n\n".join(formatted_workflow_demos)

        # determine how many activity demos we can fit and add them in the prompt
        formatted_activity_demos = self._get_formatted_demonstrations(
            model,
            activity_demos,
            self.prompt_config["demonstration_template"],
            available_tokens - model.get_num_tokens(chat_prompt.format(**prompt_values)),
        )
        if len(formatted_activity_demos) > 0:
            prompt_values["activity_examples"] = self.prompt_config["activity_demonstrations_template"].format(
                activity_examples="\n\n".join(formatted_activity_demos)
            )

        # determine how many patch demos we can fit and add them in the prompt (only for edit mode)
        LIMIT_PATCH_DEMOS = 2  # TODO: expose this
        if mode == "edit" and len(patch_demos) > 0:
            if LIMIT_PATCH_DEMOS is not None:
                patch_demos = patch_demos[:LIMIT_PATCH_DEMOS]
            formatted_patch_demos = self._get_formatted_demonstrations(
                model,
                patch_demos,
                self.prompt_config["patch_demonstration_template"],
                available_tokens - model.get_num_tokens(chat_prompt.format(**prompt_values)),
            )
            if len(formatted_patch_demos) > 0:
                prompt_values["patch_examples"] = self.prompt_config["patch_demonstrations_template"].format(patch_examples="\n\n".join(formatted_patch_demos))

        messages: PromptValue = chat_prompt.invoke(prompt_values)
        try:
            raw_model_prediction, usage = await self._get_model_response(
                query, model, messages, self.token_config["completion_tokens_max_bound_for_overflow_retry"]
            )
            workflow_raw_yaml = raw_model_prediction
        except Exception as e:
            augmented_type_defs_task.close()  # type: ignore
            raise e

        # wait for the type definitions augmentation before we postprocess the workflow
        jit_types, activities_config_map = await augmented_type_defs_task

        reasoning = None  # Initialize reasoning variable

        if workflow is not None and (mode == "sequence" or mode == "testcase"):
            # some postprocessing to merge the sequence into the existing workflow
            _, workflow_raw_yaml = await self.postprocess_component.postprocess_sequence_generation(model, workflow_raw_yaml, workflow.lmyaml(), False)

        if mode == "edit":
            # Extract reasoning before applying the diff
            reasoning, patch = self._parse_reasoning_and_patch(raw_model_prediction)
            workflow_raw_yaml, _ = self._apply_diff_to_workflow(patch, workflow_serialization)

        parsing_workflow_details: ParsedWorkflowDetails = await self.postprocess_component.process_and_validate_generation(
            model=model,
            workflow_raw_yaml=workflow_raw_yaml,
            query=query,
            jit_types=jit_types,
            activities_config_map=activities_config_map,
            connections_by_key=connections_by_key,
            target_framework=target_framework,
            objects=objects,
            activities_and_triggers=activity_defs + trigger_defs,
            variables=variables,
            localization=localization,
            consuming_feature_type=self.consuming_feature_type,
            draft_only=True,
            add_activity_ids=True,
            allow_triggers=mode in {"workflow", "edit"},
            allow_assign_activity_on_uia_expansion=mode == "testcase",  # TODO: should we allow this for edit as well?
            merge_lists_on_conflicting_keys_in_yaml=mode in {"edit"},
        )

        return DraftGenerationResult(
            usage,
            parsing_workflow_details,
            workflow_demos + activity_demos + patch_demos,
            trigger_defs,
            activity_defs,
            jit_types,
            activities_config_map,
            messages,
            raw_model_prediction,
            reasoning,
        )

    def _get_draft_demonstrations(
        self,
        activities_to_cover: set[str],
        retrieval_demonstrations: list[WfGenDataPointV2],
        target_framework: TargetFramework,
        mode: ActivitiesGenerationMode,
    ) -> t.Tuple[list[DraftCreationDemonstration], list[DraftCreationDemonstration], list[DraftCreationDemonstration]]:
        """
        Builds the draft demonstrations for the workflow generation.
        Returns:
            - activity_demos: list[DraftCreationDemonstration]
            - workflow_demos: list[DraftCreationDemonstration]
            - patch_demos: list[DraftCreationDemonstration]
        """
        workflow_demos: list[DraftCreationDemonstration] = []
        patch_demos: list[DraftCreationDemonstration] = []

        # convert activity retrieval data points to draft demonstrations
        for demonstration in retrieval_demonstrations:
            curated_workflow, existing_workflow = self.prompt_builder_component.get_curated_demonstration_workflow(demonstration)
            workflow_demos.append(DraftCreationDemonstration(demonstration["query"], curated_workflow, "Workflow", demonstration))

            # If we're in edit mode and the demonstration has an existing workflow sequence, generate a patch demonstration
            if mode == "edit" and existing_workflow is not None:
                stitched_workflow_object = Workflow("", "", existing_workflow)
                SequenceGenerationGenericActivityReplacer(Workflow("", "", curated_workflow).activities).replace(stitched_workflow_object)
                stitched_workflow = stitched_workflow_object.to_dict(include_packages=False, include_name=False)
                existing_workflow, _ = delete_sequence_thought(existing_workflow)  # type: ignore
                patch_demos.append(
                    DraftCreationDemonstration(demonstration["query"], curated_workflow, "Patch", demonstration, existing_workflow, stitched_workflow)  # type: ignore
                )

        # build a one activity workflow used as demonstration for proposed activities that are not represented in the demonstrations
        unrepresented_activities = activities_to_cover - {act for demo in retrieval_demonstrations for act in demo["used_activities"]}

        activity_demos: list[DraftCreationDemonstration] = []
        for unrepresented_activity in unrepresented_activities:
            examples = activity_config_demo_retriever.get_top_k_demos_by_most_configured_properties(unrepresented_activity, 1)

            if len(examples) == 0:
                continue

            activity_demo = self._build_activity_demo_wf(unrepresented_activity, examples[0], target_framework)
            activity_demos.append(DraftCreationDemonstration(examples[0]["input"]["user_query"], activity_demo, "Activity"))

        return activity_demos, workflow_demos, patch_demos

    def serialize_demonstration(self, demonstration: DraftCreationDemonstration, prompt_template: str, **serialization_kwargs) -> str:
        demo_message: str = ""
        if demonstration.type == "Patch":
            assert demonstration.existing_workflow is not None  # should not have been added as patch demonstration if this was None
            existing_workflow_copy = copy.deepcopy(demonstration.existing_workflow)
            existing_workflow_copy.pop("namespaceImports", None)
            existing_workflow_yaml = yaml_dump(existing_workflow_copy).strip()

            assert demonstration.stitched_workflow is not None  # should not have been added as patch demonstration if this was None
            solution_workflow_copy = copy.deepcopy(demonstration.stitched_workflow)
            solution_workflow_copy.pop("namespaceImports", None)
            solution_yaml = yaml_dump(solution_workflow_copy).strip()

            patch = get_merge_conflict(existing_workflow_yaml, solution_yaml, use_ellipsis=True)
            existing_workflow_yaml = prepend_line_numbers(existing_workflow_yaml)

            patch_template = self.prompt_config["patch_demonstration_template"]
            demo_message = patch_template.format(query=demonstration.query, existing_workflow=existing_workflow_yaml, patch=patch)
            demo_message = demo_message.replace("{", "{{").replace("}", "}}")  # escape literal curly braces
        else:
            workflow_copy = copy.deepcopy(demonstration.workflow)
            workflow_copy.pop("namespaceImports", None)
            workflow_copy.pop("variables", None)  # type: ignore
            workflow_copy.pop("arguments", None)
            process_yaml = yaml_dump(workflow_copy, default_style="'").strip()
            demo_message = prompt_template.format(query=demonstration.query, workflow=process_yaml)

        return demo_message

    def _build_activity_demo_wf(self, unrepresented_activity: str, example: ActivityConfigExample, target_framework: TargetFramework) -> dict:
        """
        Build a one activity workflow for an unrepresented activity.
        """
        demo_workflow: dict = {}

        # build one activity workflow for the unrepresented activity
        demo_workflow["workflow"] = [
            ActivityDict(
                thought=example["input"]["user_query"],
                activity=unrepresented_activity,
                params=example["output"]["configuration"],
            )
        ]

        # only keep the necessary variables and namespaces
        variables, _, namespaces = self.postprocess_component.get_used_variables(demo_workflow, {}, [], target_framework)
        demo_workflow["variables"] = variables
        demo_workflow["namespaceImports"] = namespaces
        return demo_workflow

    def _prepare_serialized_workflow_for_prompt(self, workflow: Workflow | None, model: BaseChatModel, mode: str) -> tuple[str, str]:
        """Serialize the workflow and, if needed, prune it to fit in the max tokens"""
        if workflow is None:
            return "", ""
        serialized_workflow = workflow.lmyaml(
            use_dap_activity_name=True,
            include_packages=False,
            include_arguments=mode == "edit",
            include_variables=mode == "edit",
        )
        serialized_workflow = self.prompt_builder_component.prune_existing_workflow(
            serialized_workflow, model, self.token_config["existing_workflow_max_tokens"]
        )

        existing_workflow = serialized_workflow
        if mode == "edit":
            existing_workflow = prepend_line_numbers(existing_workflow)

        template = self.prompt_config["existing_workflow_template"]
        workflow_presentation = template.get(mode, template["default"]).format(existing_workflow=existing_workflow)
        return workflow_presentation, serialized_workflow


if __name__ == "__main__":
    test_prompt = "when an account is created in Salesforce, add a new task in ServiceNow"  # Default fallback
    # test_prompt = 'Access the Excel file located here https://uipath-my.sharepoint.com/:x:/r/personal/teodora_baciu_uipath_com/Documents/2_TestUseCases/UpdateSuppliers/Suppliers.xlsx?d=w0f5f04977f2d49cbbed040a9509acb83&csf=1&web=1&e=mdj3PAConnect your OneDrive account. For each of the Suppliers in the Excel file, check the Status. If the Status is New then Add the supplier in the management system here: UiPath Demo App . In order to do that you need to click on the "New Supplier" button and input the corresponding information by matching them from the Excel file. Then, save the new supplier information'
    # test_prompt = "Whenever I get an email, detect if the language of the email is Japanese using UiPath GenAI, and, if it is, translate the email to English using OpenAI and send me a copy of it."
    # testPrompt = "when an invoice is created in an application, download the file, then check with document understanding if the vendor ID and amount match the purchase order. If they match, approve for payment, else create a human task"

    activitiesFetcher = ActivitiesRetriever()
    embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activitiesFetcher, embedding_model)
    prompt_builder_component = WorkflowGenerationPromptBuilderComponent(activitiesFetcher)

    activitiesRetrievalService = WorkflowGenerationActivityRetrievalService(activitiesFetcher, connection_embeddings_retriever, prompt_builder_component)

    dynamic_activities_component = WorkflowGenerationDynamicActivitiesComponent(activitiesFetcher)
    postprocess_component = WorkflowGenerationPostProcessComponent(
        activitiesFetcher, dynamic_activities_component, get_ignored_activities_map(activitiesFetcher)
    )

    draftCreationService = WorkflowGenerationDraftService(activitiesFetcher, postprocess_component, prompt_builder_component)
    asyncio.run(draftCreationService.init_and_load())

    default_connections = get_connections_data()

    request_context = get_testing_request_context("it", "Workflow Dataset Generation")
    request_utils.set_request_context(request_context)

    generation_settings = GenerationSettingsBuilder.build_generation_settings(test_prompt, "Windows", None, [], [], [])

    activity_retrieval = asyncio.run(
        activitiesRetrievalService.generate_relevant_activities(
            query=test_prompt,
            workflow=None,
            connections=default_connections,
            mode="workflow",
            target_framework="Windows",
            settings=generation_settings,
            eval_mode=True,
        )
    )
    draft: DraftGenerationResult = asyncio.run(
        draftCreationService.generate_workflow_draft(
            query=test_prompt,
            variables=[],
            workflow=None,
            connections=default_connections,
            objects=[],
            mode="workflow",
            target_framework="Windows",
            activity_retrieval_result=activity_retrieval,
            localization=None,
        )
    )
