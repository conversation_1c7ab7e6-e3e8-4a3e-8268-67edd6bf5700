import asyncio
import json
import pathlib

import langchain_community.callbacks
import numpy as np
from langchain_core.language_models import BaseChatModel
from langchain_core.prompts.chat import ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.agent_fetcher import AgentFetcher
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.common.schema import AgentDefinition, Connection, TargetFramework
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.models.output_parsers import MinifiedPydanticOutputParser
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils import paths, request_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_retrievers
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    AgentRetrievalGeneration,
    TokenUsage,
    WfGenDataPointV2,
)

LOGGER = AppInsightsLogger()

DEFAULT_RETRIEVAL_MODEL_NAME = "agent_retrieval_gemini_model"


class AgentRetrievalResult:
    """Result of agent retrieval containing relevant agents and metadata."""

    def __init__(
        self,
        generation: AgentRetrievalGeneration,
        prompt: str,
        token_usage: TokenUsage,
        retrieved_agents: list[AgentDefinition],
        reasoning: str,
    ):
        self.generation = generation
        self.prompt = prompt
        self.token_usage = token_usage
        self.retrieved_agents = retrieved_agents
        self.reasoning = reasoning


class WorkflowGenerationAgentRetrievalService:
    """Service for determining which agents are most relevant for a given query and plan."""

    def __init__(
        self,
        agent_fetcher: AgentFetcher,
        activities_retriever: ActivitiesRetriever,
        connection_embeddings_retriever: ConnectionEmbeddingsRetriever,
    ):
        self.retry_count = 3
        self.agent_fetcher = agent_fetcher
        self.activities_retriever = activities_retriever
        self.connection_embeddings_retriever = connection_embeddings_retriever

        config_path = (pathlib.Path(__file__).parent.parent).absolute() / "config" / "agent_retrieval_prompt.yaml"
        self.prompt_config = yaml_load(config_path)

        self.model: BaseChatModel = ModelManager().get_llm_model(DEFAULT_RETRIEVAL_MODEL_NAME, ConsumingFeatureType.WORKFLOW_GENERATION)

        self.embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")

        self.demo_retriever = workflow_generation_retrievers.DemonstrationsRetriever(
            config_path.as_posix(),
            paths.get_wf_gen_activity_retriever_dataset_path(),
            paths.get_wf_gen_activity_retriever_path(),
            activities_retriever,
        )

    def _get_relevant_demonstrations(
        self,
        query: str,
        query_embedding: np.ndarray,
        connections_embedding: np.ndarray,
        target_framework: TargetFramework,
        ignored_namespaces: set[str],
        ignored_activities: set[str],
    ) -> list[WfGenDataPointV2]:
        demonstrations = self.demo_retriever.get_relevant(
            query,
            query_embedding,
            connections_embedding,
            {
                "static": 1,
                "uia": 0,
                "testcase": 0,
            },
            target_framework,
            ignored_namespaces,
            set(),
            ignored_activities,
        )

        demonstration_details: list[WfGenDataPointV2] = [WfGenDataPointV2(**demo) for demos in demonstrations.values() for demo in demos]
        demos_needed = self.prompt_config["demonstration_filtering"]["planing_demonstrations_limit"]
        return demonstration_details[:demos_needed]

    @log_execution_time("agent_retrieval_service.generate_relevant_agents")
    async def generate_relevant_agents(
        self,
        query: str,
        context: RequestContext,
        connections: list[Connection],
    ) -> AgentRetrievalResult | None:
        """Determines which agents are most relevant for the given query."""
        all_agents = await self.agent_fetcher.get_agents(context)

        # Format agents for the prompt with synthetic IDs.
        agents_info = "\n".join(
            [
                self.prompt_config["agent_info_template"].format(
                    id=i + 1,
                    name=agent.name,
                    description=agent.description,
                    arguments_schema=json.dumps(agent.arguments_schema).replace("\n", "").replace("\\n", ""),
                )
                for i, agent in enumerate(all_agents)
            ]
        )

        # Get relevant demonstrations, mainly for the plan examples.
        # TODO: Extract common demo retrieval logic to a separate service DemoPlanRetriever that processes the query beforehand.
        query_embedding = self.embedding_model.encode_batch([query], batch_size=1, instruction_set="icl", instruction_type="query")[0]
        connections_embedding, _ = self.connection_embeddings_retriever.get_connections_embedding(connections, query_embedding)
        demonstrations = self._get_relevant_demonstrations(
            query,
            query_embedding,
            connections_embedding,
            "Windows",
            set(),
            set(),
        )

        plan_examples: list[str] = []
        for demo in demonstrations:
            demo_query = demo["query"]
            demo_plan = demo["plan"]
            if demo_plan and demo_query:
                plan_examples.append(self.prompt_config["demonstration_template"].format(query=json.dumps(demo_query), plan=json.dumps(demo_plan)))

        chat_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(self.prompt_config["system_msg"]),
                HumanMessagePromptTemplate.from_template(self.prompt_config["user_msg_template"]),
            ]
        )

        parser = MinifiedPydanticOutputParser(pydantic_object=AgentRetrievalGeneration)
        chat_chain = chat_prompt | self.model | parser

        prompt_values = {
            "query": query,
            "available_agents": agents_info,
            "format_instructions": parser.get_format_instructions(),
            "plan_examples": "\n".join(plan_examples),
        }
        for attempt in range(self.retry_count):
            try:
                with langchain_community.callbacks.get_openai_callback() as cb:
                    agent_retrieval: AgentRetrievalGeneration = await chat_chain.ainvoke(prompt_values)
                    prompt = chat_prompt.format(**prompt_values)

                    relevant_agents = [all_agents[int(num_id) - 1] for num_id in agent_retrieval.relevant_agents]

                    usage = TokenUsage(
                        model=self.model.model_name,  # type: ignore
                        prompt_tokens=cb.prompt_tokens,
                        completion_tokens=cb.completion_tokens,
                        total_tokens=cb.total_tokens,
                    )

                    return AgentRetrievalResult(
                        generation=agent_retrieval,
                        prompt=prompt,
                        token_usage=usage,
                        retrieved_agents=relevant_agents,
                        reasoning=agent_retrieval.reasoning,
                    )

            except Exception as e:
                LOGGER.warning(f"⚠️ Agent retrieval attempt {attempt + 1} failed with: {e}.")
                if attempt == self.retry_count - 1:
                    raise e
                continue


if __name__ == "__main__":
    default_connections = get_connections_data()
    request_context = get_testing_request_context("it", "Agent Process Retrieval Service")
    request_utils.set_request_context(request_context)

    agent_fetcher = AgentFetcher()
    activities_retriever = ActivitiesRetriever()
    embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activities_retriever, embedding_model)
    agent_retrieval_service = WorkflowGenerationAgentRetrievalService(agent_fetcher, activities_retriever, connection_embeddings_retriever)

    test_prompt = "Get the last email and evaluate the importance of the email"
    result = asyncio.run(
        agent_retrieval_service.generate_relevant_agents(
            test_prompt,
            request_context,
            default_connections,
        )
    )
    print(result.retrieved_agents)
