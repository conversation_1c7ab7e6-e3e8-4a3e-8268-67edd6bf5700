prompt:
  system_msg:
    with_triggers: |-
      You are a YAML formatting fixer and are given formatting instructions and some issues output by the Python YAML parsing library pyyaml.
      Your task is to fix the formatting issues in the user-provided YAML and output the correctly formatted YAML.
      Generate a process with a workflow tree in YAML format. Think step by step.
      The YAML should contain the following properties:
      ```yaml
      'processName': '<process-name>'
      'trigger':
        'thought': '<reasoning>'
        'activity': '<activity-fully-qualified-name>'
        'params':
          '<param-name>': '<param-value>'
      'workflow':
      - 'thought': '<reasoning>'
        'activity': '<activity-fully-qualified-name>'
        'params':
          '<param-name>': '<param-value>'
      - 'thought': '<reasoning>'
        'activity': '<activity-fully-qualified-name>'
        'params':
          <param-name>: '<param-value>'
      ```
      VERY IMPORTANT: Do not change the content of the YAML (e.g. string names or property names or property values) or the element order in arrays or the key order in dictionaries, only fix the formatting issues. The content should remain exactly the same as the user-provided YAML.
      Also, pay attention to the indentation of the YAML and preserve it if it is not the cause of the formatting issue.
      Additional things to consider:
      - You cannot perform concatenations in a YAML value, e.g. if your value is `'Today's forecast for ' + weatherForecast.CityName` you must wrap it in a `[[...]]` expression.

      # How to fix misplaced thoughts
      If a thought is present it should be the one initiating an item.
      Good:
      ```yaml
      - 'thought': '<reasoning>'
        'activity': '<activity-fully-qualified-name>'
        'params':
          '<param-name>': '<param-value>'
      ```
      Bad:
      ```yaml
        'thought': '<reasoning>'
      -  'activity': '<activity-fully-qualified-name>'
        'params':
          '<param-name>': '<param-value>'
      ```
      Also Bad:
      ```yaml
      'thought': '<reasoning>'
        'activity': '<activity-fully-qualified-name>'
        'params':
          '<param-name>': '<param-value>'
      ```
    without_triggers: |-
      You are a YAML formatting fixer and are given formatting instructions and some issues output by the Python YAML parsing library pyyaml.
      Your task is to fix the formatting issues in the user-provided YAML and output the correctly formatted YAML.
      Generate a process with a workflow tree in YAML format. Think step by step.
      The YAML should contain the following properties:
      ```yaml
      - 'thought': '<reasoning>'
        'activity': '<activity-fully-qualified-name>'
        'params':
          '<param-name>': '<param-value>'
      - 'thought': '<reasoning>'
        'activity': '<activity-fully-qualified-name>'
        'params':
          <param-name>: '<param-value>'
      ```
      VERY IMPORTANT: Do not change the content of the YAML (e.g. string names or property names or property values) or the element order in arrays or the key order in dictionaries, only fix the formatting issues. The content should remain exactly the same as the user-provided YAML.
      Also, pay attention to the indentation of the YAML and preserve it if it is not the cause of the formatting issue.
      Additional things to consider:
      - You cannot perform concatenations in a YAML value, e.g. if your value is `'Today's forecast for ' + weatherForecast.CityName` you must wrap it in a `[[...]]` expression.      

      # How to fix misplaced thoughts
      If a thought is present it should be the one initiating an item.
      Good:
      ```yaml
      - 'thought': '<reasoning>'
        'activity': '<activity-fully-qualified-name>'
        'params':
          '<param-name>': '<param-value>'
      ```
      Bad:
      ```yaml
        'thought': '<reasoning>'
      -  'activity': '<activity-fully-qualified-name>'
        'params':
          '<param-name>': '<param-value>'
      ```
      Also Bad:
      ```yaml
      'thought': '<reasoning>'
        'activity': '<activity-fully-qualified-name>'
        'params':
          '<param-name>': '<param-value>'
      ```
  user_msg_template: |-
    Workflow YAML with formatting issues:
    ```yaml
    {workflow}
    ```

    pyyaml errors:
    ```
    {errors}
    ```
  assistant_msg_template: |-
    ```yaml
    {fixed_workflow}
    ```
examples:
  with_triggers:
  - errors: |-
      while parsing a block mapping
      in "<unicode string>", line 1, column 1:
        'processName': 'ProcessLatestOut ...
        ^
      expected <block end>, but found '-'
      in "<unicode string>", line 11, column 1:
        - 'thought': 'If the email subje ...
        ^
    workflow: |-
      'processName': 'ProcessLatestOutlookEmailAttachments'
      'trigger':
        'thought': 'When a new email is received in Outlook'
        'activity': 'UiPath.MicrosoftOffice365.Activities.Mail.Triggers.NewEmailReceived'
        'params':
          'BrowserFolderName': 'Inbox'
          'WithAttachmentsOnly': 'True'
          'IncludeAttachments': 'True'
          'MarkAsRead': 'False'
        'workflow':
      - 'thought': 'If the email subject contains "urgent"'
        'activity': 'System.Activities.Statements.If'
        'params':
          'Condition': '[[NewEmail.Subject.ToLower().Contains("urgent")]]'
          'Then':
          - 'thought': 'Download the email attachments from Outlook'
            'activity': 'UiPath.MicrosoftOffice365.Activities.Mail.DownloadEmailAttachments'
            'params':
              'SearchMode': 'UseSimple'
              'Email': '[[NewEmail]]'
              'Result': '[[DownloadedAttachments]]'
              'ExcludeInlineAttachments': 'False'
              'AuthScopesInvalid': 'False'
          - 'thought': 'Upload the attachments to Google Drive'
            'activity': 'UiPath.GSuite.Activities.UploadFilesConnections'
            'params':
              'ObjectName': 'CuratedFolder'
              'MultipleFilesToUpload': '[[DownloadedAttachments]]'
              'ConflictResolution': 'AddSeparate'
              'FolderInputMode': 'Browse'
              'BrowseFolderId': 'root'
              'FilesInputMode': 'MultipleByVariable'
              'SaveToFolderName': 'My Drive'
              'FilesBackup':
                'StoredValue': 'MultipleByVariable'
              'AuthScopesInvalid': 'False'
              'Convert': 'False'
              'FolderIdBackup':
                'StoredValue': 'Browse'
    fixed_workflow: |-
      'processName': 'ProcessLatestOutlookEmailAttachments'
      'thought': 'To process latest Outlook email attachments efficiently'
      'workflow':
      - 'thought': 'When a new email is received in Outlook'
        'activity': 'UiPath.MicrosoftOffice365.Activities.Mail.Triggers.NewEmailReceived'
        'params':
          'BrowserFolderName': 'Inbox'
          'WithAttachmentsOnly': 'True'
          'IncludeAttachments': 'True'
          'MarkAsRead': 'False'
      - 'thought': 'Check if the email subject contains "urgent"'
        'activity': 'System.Activities.Statements.If'
        'params':
          'Condition': '[[NewEmail.Subject.ToLower().Contains("urgent")]]'
          'Then':
          - 'thought': 'Download the email attachments from Outlook'
            'activity': 'UiPath.MicrosoftOffice365.Activities.Mail.DownloadEmailAttachments'
            'params':
              'SearchMode': 'UseSimple'
              'Email': '[[NewEmail]]'
              'Result': '[[DownloadedAttachments]]'
              'ExcludeInlineAttachments': 'False'
              'AuthScopesInvalid': 'False'
          - 'thought': 'Upload the attachments to Google Drive'
            'activity': 'UiPath.GSuite.Activities.UploadFilesConnections'
            'params':
              'ObjectName': 'CuratedFolder'
              'MultipleFilesToUpload': '[[DownloadedAttachments]]'
              'ConflictResolution': 'AddSeparate'
              'FolderInputMode': 'Browse'
              'BrowseFolderId': 'root'
              'FilesInputMode': 'MultipleByVariable'
              'SaveToFolderName': 'My Drive'
              'FilesBackup':
                'StoredValue': 'MultipleByVariable'
              'AuthScopesInvalid': 'False'
              'Convert': 'False'
              'FolderIdBackup':
                'StoredValue': 'Browse'
  - errors: |-
      while parsing a block mapping
      in "<unicode string>", line 3, column 3:
      - 'thought': 'Get Today's Weather  ...
        ^
      expected <block end>, but found '<scalar>'
      in "<unicode string>", line 3, column 25:
      - 'thought': 'Get Today's Weather Forecast'
                              ^
    workflow: |-
      'processName': 'Weather Forecast Slack Notifier'
      'trigger':
        'thought': 'Manual Trigger'
        'activity': 'UiPath.Core.ManualTrigger'
      'workflow':
      - 'thought': 'Get Today's Weather Forecast'
        'activity': 'UiPath.Web.Activities.HttpClient'
        'params':
          'Result': '[[WeatherForecast]]'
      - 'thought': 'Log Today's Weather Forecast'
        'activity': 'UiPath.Core.Activities.LogMessage'
        'params':
          'Message': '[[WeatherForecast]]'
    fixed_workflow: |-
      'processName': 'Weather Forecast Slack Notifier'
      'trigger':
        'thought': 'Manual Trigger'
        'activity': 'UiPath.Core.ManualTrigger'
      'workflow':
      - 'thought': 'Get Today''s Weather Forecast'
        'activity': 'UiPath.Web.Activities.HttpClient'
        'params':
          'Result': '[[WeatherForecast]]'
      - 'thought': 'Log Today''s Weather Forecast'
        'activity': 'UiPath.Core.Activities.LogMessage'
        'params':
          'Message': '[[WeatherForecast]]'
  without_triggers:
  - errors: |-
      while parsing a block mapping
        in "<unicode string>", line 1, column 3:
          - 'thought': 'If the email subject ...
            ^
      expected <block end>, but found '<block mapping start>'
        in "<unicode string>", line 2, column 5:
              'activity': 'System.Activities.S ...
              ^
    workflow: |-
      - 'thought': 'If the email subject contains "urgent"'
          'activity': 'System.Activities.Statements.If'
        'params':
          'Condition': '[[NewEmail.Subject.ToLower().Contains("urgent")]]'
          'Then':
          - 'thought': 'Download the email attachments from Outlook'
            'activity': 'UiPath.MicrosoftOffice365.Activities.Mail.DownloadEmailAttachments'
            'params':
              'SearchMode': 'UseSimple'
              'Email': '[[NewEmail]]'
              'Result': '[[DownloadedAttachments]]'
              'ExcludeInlineAttachments': 'False'
              'AuthScopesInvalid': 'False'
          - 'thought': 'Upload the attachments to Google Drive'
            'activity': 'UiPath.GSuite.Activities.UploadFilesConnections'
            'params':
              'ObjectName': 'CuratedFolder'
              'MultipleFilesToUpload': '[[DownloadedAttachments]]'
              'ConflictResolution': 'AddSeparate'
              'FolderInputMode': 'Browse'
              'BrowseFolderId': 'root'
              'FilesInputMode': 'MultipleByVariable'
              'SaveToFolderName': 'My Drive'
              'FilesBackup':
                'StoredValue': 'MultipleByVariable'
              'AuthScopesInvalid': 'False'
              'Convert': 'False'
              'FolderIdBackup':
                'StoredValue': 'Browse'
    fixed_workflow: |-
      - 'thought': 'When a new email is received in Outlook'
        'activity': 'UiPath.MicrosoftOffice365.Activities.Mail.Triggers.NewEmailReceived'
        'params':
          'BrowserFolderName': 'Inbox'
          'WithAttachmentsOnly': 'True'
          'IncludeAttachments': 'True'
          'MarkAsRead': 'False'
      - 'thought': 'Check if the email subject contains "urgent"'
        'activity': 'System.Activities.Statements.If'
        'params':
          'Condition': '[[NewEmail.Subject.ToLower().Contains("urgent")]]'
          'Then':
          - 'thought': 'Download the email attachments from Outlook'
            'activity': 'UiPath.MicrosoftOffice365.Activities.Mail.DownloadEmailAttachments'
            'params':
              'SearchMode': 'UseSimple'
              'Email': '[[NewEmail]]'
              'Result': '[[DownloadedAttachments]]'
              'ExcludeInlineAttachments': 'False'
              'AuthScopesInvalid': 'False'
          - 'thought': 'Upload the attachments to Google Drive'
            'activity': 'UiPath.GSuite.Activities.UploadFilesConnections'
            'params':
              'ObjectName': 'CuratedFolder'
              'MultipleFilesToUpload': '[[DownloadedAttachments]]'
              'ConflictResolution': 'AddSeparate'
              'FolderInputMode': 'Browse'
              'BrowseFolderId': 'root'
              'FilesInputMode': 'MultipleByVariable'
              'SaveToFolderName': 'My Drive'
              'FilesBackup':
                'StoredValue': 'MultipleByVariable'
              'AuthScopesInvalid': 'False'
              'Convert': 'False'
              'FolderIdBackup':
                'StoredValue': 'Browse'
  - errors: |-
      while parsing a block mapping
        in "<unicode string>", line 1, column 9:
                - 'thought': 'Get Today's Weather  ...
                  ^
      expected <block end>, but found '<scalar>'
        in "<unicode string>", line 1, column 31:
                - 'thought': 'Get Today's Weather Forecast'
                                        ^
    workflow: |-
      - 'thought': 'Get Today's Weather Forecast'
        'activity': 'UiPath.Web.Activities.HttpClient'
        'params':
          'Result': '[[WeatherForecast]]'
      - 'thought': 'Log Today's Weather Forecast'
        'activity': 'UiPath.Core.Activities.LogMessage'
        'params':
          'Message': '[[WeatherForecast]]'
    fixed_workflow: |-
      - 'thought': 'Get Today''s Weather Forecast'
        'activity': 'UiPath.Web.Activities.HttpClient'
        'params':
          'Result': '[[WeatherForecast]]'
      - 'thought': 'Log Today''s Weather Forecast'
        'activity': 'UiPath.Core.Activities.LogMessage'
        'params':
          'Message': '[[WeatherForecast]]'
