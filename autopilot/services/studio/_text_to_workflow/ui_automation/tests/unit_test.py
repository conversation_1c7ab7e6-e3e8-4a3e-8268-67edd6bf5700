import base64
import json
import os
import sys
import unittest
import uuid
import xml.etree.ElementTree as ET
from copy import deepcopy
from typing import List, Tuple

import cv2
import numpy as np
from uipath_cv_client.dom_processing import BoxCoordinates, DomSourceType
from uipath_cv_client.helpers import intersection_over_predicted

from services.studio._text_to_workflow.ui_automation.action import Action, Step
from services.studio._text_to_workflow.ui_automation.models.autopilot.ui_automation_activities_model import UiAutomationActivitiesModel
from services.studio._text_to_workflow.ui_automation.models.prompt_helper import UIAState
from services.studio._text_to_workflow.ui_automation.models.ui_agent.actions_model import ActionPredictionModel
from services.studio._text_to_workflow.ui_automation.models.ui_agent.advice_model import AdviceModel
from services.studio._text_to_workflow.ui_automation.options import agent_prompt_options, autopilot_prompt_options, opt
from services.studio._text_to_workflow.ui_automation.schemas.agent_act_on_screen import UiAgentRequest
from services.studio._text_to_workflow.ui_automation.ui_automation_service import (
    ExtractInformationRequest,
    PopUpMatchRequest,
    QaElementWithDomRequest,
    QaElementWithImageRequest,
    QaGetElementDescriptionRequest,
    UIAgentAutomationService,
    UiAutomationService,
)
from services.studio._text_to_workflow.ui_automation.utils.eval_utils import test_eval1
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.errors import BadRequestError
from services.studio._text_to_workflow.utils.testing import get_testing_request_context

dirname = os.path.dirname(os.path.abspath(__file__))
data_path = os.path.join(dirname, "data")


class MainTests(unittest.IsolatedAsyncioTestCase):
    async def test_model(self):
        dom_str = """<Window><Container><Container><Container><Text Text="Search" Id="108" /><Text Text="Support" Id="15" /><Text Text="**************" Id="16" /><Text Text="Contact Sales" Id="17" /><Text Text="Request a Demo" Id="18" /></Container><Container><Container><Text Text="Products" Id="81" /><Text Text="Solutions" Id="82" /><Text Text="Resources" Id="83" /><Text Text="Plans  Pricing" Id="36" /></Container><Container><Text Text="Schedule" Id="37" /><Text Text="Join" Id="38" /><Text Text="Host" Id="125" /></Container></Container><Icon Text="Zoom Logo" Id="32" /></Container><Container><Container><Container><Text Text="Profile" selected="True" Id="84" /><Text Text="Meetings" Id="85" /><Text Text="Webinars" Id="86" /><Text Text="Personal Audio Conference" Id="87" /><Text Text="Personal Contacts" Id="88" /><Text Text="Recordings" Id="89" /><Text Text="Meeting Summary with Zoom IQ" Id="126" /><Text Text="Settings" Id="90" /><Text Text="Scheduler" Id="91" /><Text Text="Reports" Id="92" /><Text Text="Account Profile" Id="93" /></Container><Container><Text Text="Zoom Learning Center" Id="94" /><Text Text="Video Tutorials" Id="95" /><Text Text="Knowledge Base" Id="96" /></Container></Container><Container><Text Text="When you join meetings, webinars, chats or channels hosted on Zoom, your profile information, including your name and profile picture, may be visible to other participants or members. Your name and email address will also be visible to the account owner and host when you join meetings, webinars, chats or channels on their account while youre signed in. The account owner and others in the meeting, webinar, chat or channel can share this information with apps and others." Id="189" /><Container><Container><Text Text="Full nameStefan Adam" Id="145" /><Text Text="Stefan Adam" Id="146" /></Container><Text Text="Edit" Id="109" /></Container><Container><Text Text="Personal" Id="98" /><Container><Container><Container><Text Text="Phone" Id="190" /><Text Text="Not set" Id="191" /></Container><Text Text="Add Phone Number" Id="162" /></Container><Container><Text Text="Zmail" Id="129" /><Container><Text Text="Add" Id="164" /><Text Text="Not set" Id="163" /></Container></Container><Container><Text Text="Language" Id="130" /><Container><Text Text="Edit" Id="166" /><Text Text="English" Id="165" /></Container></Container><Container><Text Text="Time Zone" Id="131" /><Container><Text Text="Edit" Id="168" /><Text Text="GMT300 Bucharest" Id="167" /></Container></Container><Container><Text Text="Date Format" Id="132" /><Container><Text Text="Edit" Id="171" /><Text Text="mm/dd/yyyy" Id="169" /><Text Text=" Example 06/09/2023" Id="170" /></Container></Container><Container><Text Text="Time Format" Id="133" /><Container><Text Text="Edit" Id="173" /><Text Text="Use 12-hour time Example 0200 PM" Id="172" /></Container></Container></Container></Container><Container><Text Text="Meeting" Id="100" /><Container><Container><Text Text="Personal Meeting ID" Id="134" /><Container><Container><Container><Button Id="223" /><Text Text="*** *** *581" Id="222" /></Container><Text Text="https//uipath.zoom.us/j/*******581" Id="207" /><Text Text="Disabled" Id="208" /><Text Text="Use this ID for instant meetings" Id="209" /></Container><Text Text="Edit" Id="193" /></Container></Container><Container><Container><Text Text="Not set yet." Id="177" /><Text Text="Customize" Id="194" /></Container><Container><Button Id="176" /><Text Text="Personal Link" Id="175" /></Container></Container><Container><Text Text="Host Key" Id="135" /><Container><Text Text="Edit" Id="181" /><Button Id="180" /><Text Text="********" Id="179" /></Container></Container></Container></Container><Container><Text Text="Account" Id="102" /><Container><Container><Text Text="License" Id="136" /><Container><Container><Text Text="Licensed" Id="210" /><Button Id="211" /></Container><Container><Text Text="Meeting" Id="196" /><Container><Text Text="1000 participants" Id="212" /><Button Id="213" /></Container></Container><Container><Text Text="Zoom Webinars" Id="198" /><Container><Text Text="1000 attendees" Id="214" /><Button Id="215" /></Container></Container><Container><Text Text="Zoom Whiteboard" Id="200" /><Text Text="Unlimited editable boards with standard features" Id="201" /></Container></Container></Container><Container><Text Text="********" Id="137" /><Text Text="Account NumberAccount No." Id="157" /></Container></Container></Container><Container><Text Text="Sign In" Id="104" /><Container><Container><Text Text="Sign-In Email" Id="138" /><Container><Button Id="225" /><Text Text="ste***uipath.com" Id="224" /></Container></Container><Container><Text Text="Linked Accounts" Id="139" /><Text Text="Single Sign-On" Id="140" /></Container><Container><Text Text="Signed-In Device" Id="141" /><Container><Text Text="Sign Me Out From All Devices" Id="185" /><Button Id="186" /></Container></Container></Container></Container><Container><Text Text="Others" Id="106" /><Container><Container><Text Text="Calendar and Contacts Integration" Id="142" /><Container><Container><Text Text="We support the following services Google Calendar, Microsoft Exchange, and Microsoft Office 365" Id="217" /><Text Text="If you want to add your contacts by importing a CSV file, go to Personal Contacts." Id="218" /></Container><Text Text="Configure Calendar and Contacts Service" Id="203" /></Container></Container><Container><Text Text="Data Configuration" Id="143" /><Container><Container><Container><Text Text="Meetings/webinars/whiteboards data center" Id="219" /><Text Text="Zoom data centers process real-time meeting/webinar video, audio, and shared content when you host meetings and webinars. They can also process your Zoom Whiteboard data. Selecting all data centers can provide the best experience for participants joining from all regions. Opting out of data centers may limit participants joining from those regions from using CRC, Dial-in, Call Me, and Invite by Phone." Id="220" /></Container><CheckBox Text="" Id="221" /></Container><Container><CheckBox Text="Australia" Id="241" /><CheckBox Text="Japan" Id="249" /><CheckBox Text="Brazil" Id="242" /><CheckBox Text="Mexico" Id="250" /><CheckBox Text="Canada" Id="243" /><CheckBox Text="Netherlands" Id="251" /><CheckBox Text="China" Id="244" /><CheckBox Text="Singapore" Id="252" /><CheckBox Text="Germany" Id="245" /><CheckBox Text="Switzerland" Id="253" /><CheckBox Text="Hong Kong SAR" Id="246" /><CheckBox Text="Taiwan" Id="254" /><CheckBox Text="India" Id="247" /><CheckBox Text="United States" Id="255" /><CheckBox Text="Ireland" Id="248" /></Container></Container></Container></Container></Container></Container></Container><Container><Container><Container><Text Text="About" Id="23" /><Container><Text Text="Zoom Blog" Id="46" /><Text Text="Customers" Id="47" /><Text Text="Our Team" Id="48" /><Text Text="Careers" Id="49" /><Text Text="Integrations" Id="50" /><Text Text="Partners" Id="51" /><Text Text="Investors" Id="52" /><Text Text="Press" Id="53" /><Text Text="ESG Responsibillity" Id="54" /><Text Text="Media Kit" Id="55" /><Text Text="How to Videos" Id="56" /><Text Text="Developer Platform" Id="57" /><Text Text="Zoom Ventures" Id="58" /></Container></Container><Container><Text Text="Download" Id="25" /><Container><Text Text="Meetings Client" Id="59" /><Text Text="Zoom Rooms Client" Id="60" /><Text Text="Browser Extension" Id="61" /><Text Text="Outlook Plug-in" Id="62" /><Text Text="Lync Plug-in" Id="63" /><Text Text="Android App" Id="64" /><Text Text="Zoom Virtual Backgrounds" Id="65" /></Container></Container><Container><Text Text="Sales" Id="27" /><Container><Text Text="**************" Id="66" /><Text Text="Contact Sales" Id="67" /><Text Text="Plans  Pricing" Id="68" /><Text Text="Request a Demo" Id="69" /><Text Text="Webinars and Events" Id="70" /></Container></Container><Container><Text Text="Support" Id="29" /><Container><Text Text="Test Zoom" Id="71" /><Text Text="Account" Id="72" /><Text Text="Support Center" Id="73" /><Text Text="Learning Center" Id="74" /><Text Text="Feedback" Id="75" /><Text Text="Contact Us" Id="76" /><Text Text="Accessibility" Id="77" /><Text Text="Developer Support" Id="78" /><Text Text="Privacy, Security, Legal Policies, and Modern Slavery Act Transparency Statement" Id="79" /></Container></Container><Container><Text Text="Language" Id="31" /><Text Text="English" Id="144" /></Container></Container><Text Text="Copyright 2023 Zoom Video Communications, Inc. All rights reserved. Terms Privacy Trust Center Acceptable Use Guidelines Legal  Compliance Do Not Sell/Share My Personal Information Cookie Preferences" Id="14" /></Container></Container><Button Id="256" /><Button Id="257" /></Window>"""  # noqa
        xml_dom = ET.fromstring(dom_str)
        task = "Export Last Zoom Recording"
        url = "https://uipath.zoom.us/profile"
        title = "My Profile - Zoom"

        request_context = get_testing_request_context()
        request_utils.set_request_context(request_context)
        options = deepcopy(opt)
        options["prompt_options"].update(autopilot_prompt_options)
        model = UiAutomationActivitiesModel(options)
        image_base64 = None
        uia_state = UIAState(
            task_description=task,
            variables={},
            input_variables={},
            output_variables={},
            title=title,
            url=url,
            xml_dom=xml_dom,
            is_object_dom=False,
            previous_steps=[],
            raw_dom=[],
            image_base64=image_base64,
            image_size=None,
        )

        predicted_step, _ = await model.predict(uia_state)

        top_action = predicted_step.actions[0]
        assert top_action.name == "click"
        assert top_action.parameters["element_id"] == 89

        # test with structured output, temporally disabled since structured output is disbled
        # test_opt = deepcopy(opt)
        # test_opt["engine_config"]["structured_output"] = True
        # test_opt["engine_config"]["structured_output_method"] = "function_calling"
        # model = UiAutomationActivitiesModel(test_opt)
        # predicted_step, _ = await model.predict(uia_state)

        # top_action = predicted_step.actions[0]
        # assert top_action.name == "click"
        # assert top_action.parameters["element_id"] == 89

        # test running with a previous step not empty
        predicted_step.screen_info = {"title": uia_state.title, "url": uia_state.url}
        uia_state.previous_steps.append(predicted_step)
        predicted_step, _ = await model.predict(uia_state)
        top_action = predicted_step.actions[0]
        assert top_action.name in ("click", "finish")

    async def test_agent_model(self):
        dom_str = """<Window><Container><Container><Container><Text Text="Search" Id="108" /><Text Text="Support" Id="15" /><Text Text="**************" Id="16" /><Text Text="Contact Sales" Id="17" /><Text Text="Request a Demo" Id="18" /></Container><Container><Container><Text Text="Products" Id="81" /><Text Text="Solutions" Id="82" /><Text Text="Resources" Id="83" /><Text Text="Plans  Pricing" Id="36" /></Container><Container><Text Text="Schedule" Id="37" /><Text Text="Join" Id="38" /><Text Text="Host" Id="125" /></Container></Container><Icon Text="Zoom Logo" Id="32" /></Container><Container><Container><Container><Text Text="Profile" selected="True" Id="84" /><Text Text="Meetings" Id="85" /><Text Text="Webinars" Id="86" /><Text Text="Personal Audio Conference" Id="87" /><Text Text="Personal Contacts" Id="88" /><Text Text="Recordings" Id="89" /><Text Text="Meeting Summary with Zoom IQ" Id="126" /><Text Text="Settings" Id="90" /><Text Text="Scheduler" Id="91" /><Text Text="Reports" Id="92" /><Text Text="Account Profile" Id="93" /></Container><Container><Text Text="Zoom Learning Center" Id="94" /><Text Text="Video Tutorials" Id="95" /><Text Text="Knowledge Base" Id="96" /></Container></Container><Container><Text Text="When you join meetings, webinars, chats or channels hosted on Zoom, your profile information, including your name and profile picture, may be visible to other participants or members. Your name and email address will also be visible to the account owner and host when you join meetings, webinars, chats or channels on their account while youre signed in. The account owner and others in the meeting, webinar, chat or channel can share this information with apps and others." Id="189" /><Container><Container><Text Text="Full nameStefan Adam" Id="145" /><Text Text="Stefan Adam" Id="146" /></Container><Text Text="Edit" Id="109" /></Container><Container><Text Text="Personal" Id="98" /><Container><Container><Container><Text Text="Phone" Id="190" /><Text Text="Not set" Id="191" /></Container><Text Text="Add Phone Number" Id="162" /></Container><Container><Text Text="Zmail" Id="129" /><Container><Text Text="Add" Id="164" /><Text Text="Not set" Id="163" /></Container></Container><Container><Text Text="Language" Id="130" /><Container><Text Text="Edit" Id="166" /><Text Text="English" Id="165" /></Container></Container><Container><Text Text="Time Zone" Id="131" /><Container><Text Text="Edit" Id="168" /><Text Text="GMT300 Bucharest" Id="167" /></Container></Container><Container><Text Text="Date Format" Id="132" /><Container><Text Text="Edit" Id="171" /><Text Text="mm/dd/yyyy" Id="169" /><Text Text=" Example 06/09/2023" Id="170" /></Container></Container><Container><Text Text="Time Format" Id="133" /><Container><Text Text="Edit" Id="173" /><Text Text="Use 12-hour time Example 0200 PM" Id="172" /></Container></Container></Container></Container><Container><Text Text="Meeting" Id="100" /><Container><Container><Text Text="Personal Meeting ID" Id="134" /><Container><Container><Container><Button Id="223" /><Text Text="*** *** *581" Id="222" /></Container><Text Text="https//uipath.zoom.us/j/*******581" Id="207" /><Text Text="Disabled" Id="208" /><Text Text="Use this ID for instant meetings" Id="209" /></Container><Text Text="Edit" Id="193" /></Container></Container><Container><Container><Text Text="Not set yet." Id="177" /><Text Text="Customize" Id="194" /></Container><Container><Button Id="176" /><Text Text="Personal Link" Id="175" /></Container></Container><Container><Text Text="Host Key" Id="135" /><Container><Text Text="Edit" Id="181" /><Button Id="180" /><Text Text="********" Id="179" /></Container></Container></Container></Container><Container><Text Text="Account" Id="102" /><Container><Container><Text Text="License" Id="136" /><Container><Container><Text Text="Licensed" Id="210" /><Button Id="211" /></Container><Container><Text Text="Meeting" Id="196" /><Container><Text Text="1000 participants" Id="212" /><Button Id="213" /></Container></Container><Container><Text Text="Zoom Webinars" Id="198" /><Container><Text Text="1000 attendees" Id="214" /><Button Id="215" /></Container></Container><Container><Text Text="Zoom Whiteboard" Id="200" /><Text Text="Unlimited editable boards with standard features" Id="201" /></Container></Container></Container><Container><Text Text="********" Id="137" /><Text Text="Account NumberAccount No." Id="157" /></Container></Container></Container><Container><Text Text="Sign In" Id="104" /><Container><Container><Text Text="Sign-In Email" Id="138" /><Container><Button Id="225" /><Text Text="ste***uipath.com" Id="224" /></Container></Container><Container><Text Text="Linked Accounts" Id="139" /><Text Text="Single Sign-On" Id="140" /></Container><Container><Text Text="Signed-In Device" Id="141" /><Container><Text Text="Sign Me Out From All Devices" Id="185" /><Button Id="186" /></Container></Container></Container></Container><Container><Text Text="Others" Id="106" /><Container><Container><Text Text="Calendar and Contacts Integration" Id="142" /><Container><Container><Text Text="We support the following services Google Calendar, Microsoft Exchange, and Microsoft Office 365" Id="217" /><Text Text="If you want to add your contacts by importing a CSV file, go to Personal Contacts." Id="218" /></Container><Text Text="Configure Calendar and Contacts Service" Id="203" /></Container></Container><Container><Text Text="Data Configuration" Id="143" /><Container><Container><Container><Text Text="Meetings/webinars/whiteboards data center" Id="219" /><Text Text="Zoom data centers process real-time meeting/webinar video, audio, and shared content when you host meetings and webinars. They can also process your Zoom Whiteboard data. Selecting all data centers can provide the best experience for participants joining from all regions. Opting out of data centers may limit participants joining from those regions from using CRC, Dial-in, Call Me, and Invite by Phone." Id="220" /></Container><CheckBox Text="" Id="221" /></Container><Container><CheckBox Text="Australia" Id="241" /><CheckBox Text="Japan" Id="249" /><CheckBox Text="Brazil" Id="242" /><CheckBox Text="Mexico" Id="250" /><CheckBox Text="Canada" Id="243" /><CheckBox Text="Netherlands" Id="251" /><CheckBox Text="China" Id="244" /><CheckBox Text="Singapore" Id="252" /><CheckBox Text="Germany" Id="245" /><CheckBox Text="Switzerland" Id="253" /><CheckBox Text="Hong Kong SAR" Id="246" /><CheckBox Text="Taiwan" Id="254" /><CheckBox Text="India" Id="247" /><CheckBox Text="United States" Id="255" /><CheckBox Text="Ireland" Id="248" /></Container></Container></Container></Container></Container></Container></Container><Container><Container><Container><Text Text="About" Id="23" /><Container><Text Text="Zoom Blog" Id="46" /><Text Text="Customers" Id="47" /><Text Text="Our Team" Id="48" /><Text Text="Careers" Id="49" /><Text Text="Integrations" Id="50" /><Text Text="Partners" Id="51" /><Text Text="Investors" Id="52" /><Text Text="Press" Id="53" /><Text Text="ESG Responsibillity" Id="54" /><Text Text="Media Kit" Id="55" /><Text Text="How to Videos" Id="56" /><Text Text="Developer Platform" Id="57" /><Text Text="Zoom Ventures" Id="58" /></Container></Container><Container><Text Text="Download" Id="25" /><Container><Text Text="Meetings Client" Id="59" /><Text Text="Zoom Rooms Client" Id="60" /><Text Text="Browser Extension" Id="61" /><Text Text="Outlook Plug-in" Id="62" /><Text Text="Lync Plug-in" Id="63" /><Text Text="Android App" Id="64" /><Text Text="Zoom Virtual Backgrounds" Id="65" /></Container></Container><Container><Text Text="Sales" Id="27" /><Container><Text Text="**************" Id="66" /><Text Text="Contact Sales" Id="67" /><Text Text="Plans  Pricing" Id="68" /><Text Text="Request a Demo" Id="69" /><Text Text="Webinars and Events" Id="70" /></Container></Container><Container><Text Text="Support" Id="29" /><Container><Text Text="Test Zoom" Id="71" /><Text Text="Account" Id="72" /><Text Text="Support Center" Id="73" /><Text Text="Learning Center" Id="74" /><Text Text="Feedback" Id="75" /><Text Text="Contact Us" Id="76" /><Text Text="Accessibility" Id="77" /><Text Text="Developer Support" Id="78" /><Text Text="Privacy, Security, Legal Policies, and Modern Slavery Act Transparency Statement" Id="79" /></Container></Container><Container><Text Text="Language" Id="31" /><Text Text="English" Id="144" /></Container></Container><Text Text="Copyright 2023 Zoom Video Communications, Inc. All rights reserved. Terms Privacy Trust Center Acceptable Use Guidelines Legal  Compliance Do Not Sell/Share My Personal Information Cookie Preferences" Id="14" /></Container></Container><Button Id="256" /><Button Id="257" /></Window>"""  # noqa
        xml_dom = ET.fromstring(dom_str)
        task = "Export Last Zoom Recording"
        url = "https://uipath.zoom.us/profile"
        title = "My Profile - Zoom"

        myuuid = uuid.uuid4()

        request_context = get_testing_request_context()
        request_context.ui_task_session_id = str(myuuid)
        options = deepcopy(opt)
        options["caching"]["enabled"] = False
        options["prompt_options"].update(agent_prompt_options)
        request_utils.set_request_context(request_context)
        model = ActionPredictionModel(options)

        image_base64 = None
        uia_state = UIAState(
            task_description=task,
            variables={},
            input_variables={},
            output_variables={},
            title=title,
            url=url,
            xml_dom=xml_dom,
            is_object_dom=False,
            previous_steps=[],
            raw_dom=[],
            image_base64=image_base64,
            image_size=None,
            session_id=str(myuuid),
        )

        predicted_step, _ = await model.predict(uia_state)

        top_action = predicted_step.actions[0]
        assert top_action.name == "click"
        assert top_action.parameters["element_id"] == 89

        test_opt = deepcopy(opt)
        test_opt["caching"]["enabled"] = False
        test_opt["prompt_options"].update(agent_prompt_options)
        test_opt["engine_config"]["default_engine"] = "gpt-4o-2024-08-06"
        test_opt["engine_config"]["structured_output"] = False
        test_opt["engine_config"]["structured_output_method"] = "function_calling"
        model = ActionPredictionModel(test_opt)
        predicted_step, _ = await model.predict(uia_state)

        top_action = predicted_step.actions[0]
        assert top_action.name == "click"
        assert top_action.parameters["element_id"] == 89

    async def _test_openai_compute_use(self):
        # skip this test until we use LLMGW
        service = self._setup_ui_agent_automation_service(opt)

        first_step_image = os.path.join(data_path, "openai_computer_use", "1.png")
        second_step_image = os.path.join(data_path, "openai_computer_use", "2.png")

        with open(first_step_image, "rb") as img_file:
            image_cv = cv2.imread(first_step_image)
            img_width = image_cv.shape[1]
            img_height = image_cv.shape[0]

        with open(first_step_image, "rb") as img_file:
            first_step_image_64 = base64.b64encode(img_file.read()).decode("utf-8")

        with open(second_step_image, "rb") as img_file:
            second_step_image = base64.b64encode(img_file.read()).decode("utf-8")

        request = {
            "userTask": "What's the weather in Dublin?",
            "image": first_step_image_64,
            "image_width": img_width,
            "image_height": img_height,
            "env_name": "browser",
            "model_name": "openai-computer-use",
            "rawDOM": None,
            "title": None,
            "url": None,
            "previousSteps": None,
            "metadata": None,
        }

        response, _ = await service.predict(UiAgentRequest(**request))
        assert "step" in response
        assert response["step"]["actions"][0]["method_type"] == "click"

        request = {
            "userTask": "What's the weather in Dublin?",
            "image": second_step_image,
            "image_width": img_width,
            "image_height": img_height,
            "env_name": "browser",
            "model_name": "openai-computer-use",
            "rawDOM": None,
            "title": None,
            "url": None,
            "previousSteps": [response["step"]],
            "metadata": None,
        }

        response, _ = await service.predict(UiAgentRequest(**request))
        assert "step" in response
        assert response["step"]["actions"][0]["method_type"] == "type_into"

    def test_finish(self):
        service = self._setup_ui_automation_service(opt)
        predicted_answer = """<Step description="Extract info">
            <Action description="Extract the title" method="get_text(1, $title)"/>
            <Action description="Finish the task" method="finish()"/>
        </Step>"""
        step = Step.parse_xml(predicted_answer, is_ui_agent=False)
        res = service.request_processor.process_response(step, raw_dom={}, dom_type=DomSourceType.CV)
        assert res["step"]["actions"][-1]["method_type"] == "finish"

    def test_eval(self):
        test_eval1()

    def _get_request_data(self, task_name):
        task_folder = os.path.join(data_path, task_name)

        run_files = [f for f in os.listdir(task_folder) if os.path.isfile(os.path.join(task_folder, f))]
        run_files = [f for f in run_files if os.path.splitext(f)[1].lower() == ".json" and "cv_data.json" not in f]
        run_files.sort()

        all_input_data = []
        for run_file in run_files:
            run_file_path = os.path.join(task_folder, run_file)
            filename_without_extention = os.path.splitext(run_file)[0]
            image_file = os.path.join(task_folder, filename_without_extention + ".png")

            with open(run_file_path) as user_file:
                content = json.load(user_file)
                title = content["SiteTitle"]
                url = content["SiteURL"]
                image_base_64 = None
                if os.path.exists(image_file):
                    image = cv2.imread(image_file)
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                if os.path.exists(image_file):
                    with open(image_file, "rb") as img_file:
                        image_base_64 = base64.b64encode(img_file.read()).decode("utf-8")

                request_data = {
                    "userTask": task_name,
                    "variables": {},
                    "title": title,
                    "url": url,
                    "rawDOM": content["RawDOM"],
                    "image": image_base_64,
                }
                all_input_data.append(request_data)

        return all_input_data

    def test_incomplete_response_parse(self):
        response_message = '<Step description="Test description">'
        for _ in range(3):
            response_message += '<Action description="Test description" method="click(14)"/>'
        response_message += "<Action descript"
        prediction = Step.parse(response_message, is_ui_agent=False, format="xml")

        assert len(prediction.actions) == 3

    def test_parse_step_message(self):
        step_str = """<Step description="Set flight search parameters and search for flights">
  <Action description="Click on the One-way button" method="click(144)"/>
  <Action description="Type 'New York' in the origin input box" method="type_into(147, '', 'New York')"/>
  <Action description="Type 'Chicago' in the destination input box" method="type_into(151, '', 'Chicago')"/>
  <Action description="Click on the Search button" method="click(125)"/>
</Step>"""
        Step.parse_xml(step_str, is_ui_agent=True)

    def _check_action_dict(self, predicted_dict, expected_dict):
        for key in list(expected_dict.keys()):
            assert predicted_dict[key] == expected_dict[key]

    def _setup_ui_automation_service(self, opt) -> UiAutomationService:
        autopilot_opt = deepcopy(opt)
        autopilot_opt["prompt_options"].update(autopilot_prompt_options)
        service = UiAutomationService(options=autopilot_opt)
        request_context = get_testing_request_context()
        request_utils.set_request_context(request_context)
        return service

    def _setup_ui_agent_automation_service(self, opt) -> UIAgentAutomationService:
        agent_opt = deepcopy(opt)
        agent_opt["prompt_options"].update(agent_prompt_options)
        service = UIAgentAutomationService(options=agent_opt)
        myuuid = uuid.uuid4()
        request_context = get_testing_request_context()
        request_context.ui_task_session_id = str(myuuid)
        request_utils.set_request_context(request_context)
        return service

    def _load_ui_automation_example(self, sample_path: str) -> Tuple[List[dict], str]:
        with open(sample_path) as f:
            json_content = json.load(f)
            elements = json_content["ExtractedDOM"]["RawDOM"]["RawElements"]
            image_base_64 = json_content["ImageBase64"]

        return elements, image_base_64

    def _assert_iop_value(self, predicted_box: BoxCoordinates, expected_box: BoxCoordinates, iop_threshold=0.8):
        iop = intersection_over_predicted(
            [expected_box.x, expected_box.y, expected_box.x + expected_box.width, expected_box.y + expected_box.height],
            [predicted_box.x, predicted_box.y, predicted_box.x + predicted_box.width, predicted_box.y + predicted_box.height],
        )
        assert iop >= iop_threshold, f"IoP={iop} is less than {iop_threshold}"

    def test_actions_output(self):
        action = Action(description="description1", name="click", parameters={"element_id": 14})
        action_dict = action.to_response_dict(is_ui_agent=False)
        action_expected_dict = {
            "description": "description1",
            "method_type": "click",
            "element_id": "14",
        }
        self._check_action_dict(action_dict, action_expected_dict)

        action = Action(description="description2", name="get_text", parameters={"element_id": 223, "variable": "$address"})
        action_dict = action.to_response_dict(is_ui_agent=False)
        action_expected_dict = {
            "description": "description2",
            "method_type": "get_text",
            "element_id": "223",
            "variable": "$address",
        }
        self._check_action_dict(action_dict, action_expected_dict)

        action = Action(description="description3", name="type_into", parameters={"element_id": 20, "variable": "$firstName", "default_value": "John"})
        action_dict = action.to_response_dict(is_ui_agent=False)
        action_expected_dict = {
            "description": "description3",
            "method_type": "type_into",
            "element_id": "20",
            "variable": "$firstName",
            "default_value": "John",
        }
        self._check_action_dict(action_dict, action_expected_dict)

        # action = Action("description4", "select(40, $country, 'France')")
        action = Action(description="description4", name="select", parameters={"element_id": 40, "variable": "$country", "default_value": "France"})
        action_dict = action.to_response_dict(is_ui_agent=False)
        action_expected_dict = {
            "description": "description4",
            "method_type": "select",
            "element_id": "40",
            "variable": "$country",
            "default_value": "France",
        }
        self._check_action_dict(action_dict, action_expected_dict)

    async def test_service_or(self):
        json_path = os.path.join(data_path, "object_repository", "netsuite.json")
        with open(json_path) as f:
            json_content = json.load(f)

        service = self._setup_ui_automation_service(opt)
        request = {
            "userTask": "Fill new vendor screen and submit",
            "title": "NetSuite",
            "DOMType": "ObjectRepository",
            "rawDOM": json_content,
        }
        out, _predict_info = await service.predict(request)
        assert len(out["step"]["actions"]) == 5
        for action_item in out["step"]["actions"]:
            assert action_item["box_reference"] is not None

        assert out["step"]["actions"][-1]["box_reference"] == "aNzOp9-eak-7XcQhq0tkVw/ubI-6FqXyUSxFPNeRy55uQ"

    async def test_service_qa_with_image(self):
        img_path = os.path.join(
            data_path,
            "Export Last Zoom Recording",
            "e802ef8c-4f60-4a4f-8a28-2999e8979802_2023.06.09 at 16.28.38.png",
        )
        image_base_64 = None

        if os.path.exists(img_path):
            with open(img_path, "rb") as img_file:
                image_base_64 = base64.b64encode(img_file.read()).decode("utf-8")

        service = self._setup_ui_automation_service(opt)
        request = QaElementWithImageRequest(image=image_base_64, human_description="Recordings section", rawDOM=None)
        out = await service.qa_element(request)
        predicted_box = [int(x) for x in out["element"]["Area"].split(",")]

        expected_box = BoxCoordinates(**{"x": 33, "y": 334, "width": 81, "height": 17})
        predicted_box = BoxCoordinates(**{"x": predicted_box[0], "y": predicted_box[1], "width": predicted_box[2], "height": predicted_box[3]})

        self._assert_iop_value(predicted_box, expected_box)

    async def test_service_qa_with_image_and_dom_elements(self):
        dom_elements, imagebase64 = self._load_ui_automation_example(
            os.path.join(data_path, "test_qa_element", "d19ccf1d-23b7-48b7-ace8-087313f67884_step_1.json")
        )
        service = self._setup_ui_automation_service(opt)
        request = QaElementWithImageRequest(image=imagebase64, rawDOM=dom_elements, human_description="Create subtask button")
        response = await service.qa_element(request)

        predicted_box = [int(x) for x in response["element"]["Area"].split(",")]
        predicted_box = BoxCoordinates(**{"x": predicted_box[0], "y": predicted_box[1], "width": predicted_box[2], "height": predicted_box[3]})
        expected_box = BoxCoordinates(**{"x": 738, "y": 560, "width": 202, "height": 48})

        self._assert_iop_value(predicted_box, expected_box)

    async def test_service_qa_with_image_and_duplicates_header(self):
        img_path = os.path.join(
            data_path,
            "Export Last Zoom Recording",
            "e802ef8c-4f60-4a4f-8a28-2999e8979802_2023.06.09 at 16.28.38.png",
        )

        with open(img_path, "rb") as img_file:
            image_base_64 = base64.b64encode(img_file.read()).decode("utf-8")

        service = self._setup_ui_automation_service(opt)
        request = QaElementWithImageRequest(image=image_base_64, human_description="Edit personal information", rawDOM=None)
        out = await service.qa_element(request, request_headers={"x-uipath-semantic-duplicates-matching": True})

        assert len(out["element"]) > 1, "Expected multiple elements in the response"

        request = QaElementWithImageRequest(image=image_base_64, human_description="Black trolley over 200$", rawDOM=None)
        out = await service.qa_element(request, request_headers={"x-uipath-semantic-duplicates-matching": True})

        assert len(out) == 0, "Expected no elements in the response for this description"

    async def test_service_qa_with_image_and_dom_elements_duplicates_header(self):
        dom_elements, imagebase64 = self._load_ui_automation_example(
            os.path.join(data_path, "test_qa_element", "d19ccf1d-23b7-48b7-ace8-087313f67884_step_1.json")
        )
        service = self._setup_ui_automation_service(opt)
        request = QaElementWithImageRequest(image=imagebase64, rawDOM=dom_elements, human_description="Checklist")
        response = await service.qa_element(request, request_headers={"x-uipath-semantic-duplicates-matching": True})

        assert len(response["element"]) > 1, "Expected multiple elements in the response"

        request = QaElementWithImageRequest(image=imagebase64, human_description="Black trolley over 200$", rawDOM=None)
        out = await service.qa_element(request, request_headers={"x-uipath-semantic-duplicates-matching": True})

        assert len(out) == 0, "Expected no elements in the response for this description"

    async def test_service_qa_with_dom(self):
        dom_path = os.path.join(data_path, "expedia_step1.json")
        with open(dom_path) as f:
            json_content = json.load(f)
        dom = json_content["ParsedDOM"]

        service = self._setup_ui_automation_service(opt)

        request = QaElementWithDomRequest(dom_xml=dom, human_description="Go further with the Expedia app")
        out = await service.qa_element_with_dom(request)
        assert out["element_id"] == 72

    async def test_service_qa_with_dom_and_image(self):
        dom_path = os.path.join(data_path, "expedia_step1.json")
        with open(dom_path) as f:
            json_content = json.load(f)
        image_bytes = cv2.imencode(".jpg", np.zeros((128, 128, 3), dtype=np.uint8))[1].tobytes()
        image_base64 = base64.b64encode(image_bytes).decode("utf-8")
        dom = json_content["ParsedDOM"]

        service = self._setup_ui_automation_service(opt)

        request = QaElementWithDomRequest(
            dom_xml=dom,
            image=image_base64,
            human_description="Go further with the Expedia app",
        )
        out = await service.qa_element_with_dom(request)
        assert out["element_id"] == 72

    async def test_service_qa_with_image_no_text(self):
        img_path = os.path.join(data_path, "test_qa_element", "image_no_text.png")
        service = self._setup_ui_automation_service(opt)

        with open(img_path, "rb") as img_file:
            image_base_64 = base64.b64encode(img_file.read()).decode("utf-8")

        request = QaElementWithImageRequest(image=image_base_64, human_description="search icon", rawDOM=None)

        out = await service.qa_element(request)

        pred_x, pred_y, pred_width, pred_height = [int(x) for x in out["element"]["Area"].split(",")]

        predicted_box = BoxCoordinates(**{"x": pred_x, "y": pred_y, "width": pred_width, "height": pred_height})
        expected_box = BoxCoordinates(**{"x": 47, "y": 56, "width": 52, "height": 62})

        self._assert_iop_value(predicted_box, expected_box)

    async def test_service_qa_get_descriptions_text(self):
        img_path = os.path.join(data_path, "test_qa_element", "image_no_text.png")
        image_base_64 = None

        if os.path.exists(img_path):
            with open(img_path, "rb") as img_file:
                image_base_64 = base64.b64encode(img_file.read()).decode("utf-8")

        service = self._setup_ui_automation_service(opt)

        request = QaElementWithImageRequest(image=image_base_64, human_description="microfone icon", rawDOM=None)
        out = await service.qa_element(request)

        pred_x, pred_y, pred_width, pred_height = [int(x) for x in out["element"]["Area"].split(",")]

        predicted_box = BoxCoordinates(**{"x": pred_x, "y": pred_y, "width": pred_width, "height": pred_height})
        expected_box = BoxCoordinates(**{"x": 1024, "y": 53, "width": 53, "height": 50})

        self._assert_iop_value(predicted_box, expected_box)

    async def test_service_qa_description_target_outside_view(self):
        service = self._setup_ui_automation_service(opt)
        sample_path = os.path.join(data_path, "BestBuy Fitbit fitness tracker", "f8eea419-af7a-47e3-bb7b-cac9b0da2dd8_step_1.json")
        elements, image_base_64 = self._load_ui_automation_example(sample_path)

        target_box = BoxCoordinates(**{"x": 1694, "y": 6268, "width": 32, "height": 32})
        request = QaGetElementDescriptionRequest(target_box=target_box, image=image_base_64, rawDOM=elements)
        response = await service.get_element_description(request)
        generated_description = response.get("description", "")

        assert "share" in generated_description.lower(), f"Expected 'share' in description, got {generated_description}"

        request = QaElementWithImageRequest(image=image_base_64, human_description="Share on Pinterest", rawDOM=elements)
        raw_element = await service.qa_element(request)
        assert raw_element["element"]["Id"] == 85

    async def test_service_qa_description_target_outside_view_no_dom(self):
        service = self._setup_ui_automation_service(opt)
        sample_path = os.path.join(data_path, "BestBuy Fitbit fitness tracker", "f8eea419-af7a-47e3-bb7b-cac9b0da2dd8_step_1.json")
        _, image_base_64 = self._load_ui_automation_example(sample_path)

        target_box = BoxCoordinates(**{"x": 1694, "y": 6268, "width": 32, "height": 32})
        request = QaGetElementDescriptionRequest(target_box=target_box, image=image_base_64, rawDOM=None)

        with self.assertRaises(BadRequestError):
            await service.get_element_description(request)

    async def test_service_qa_description_target_in_view_with_anchors(self):
        service = self._setup_ui_automation_service(opt)
        _, image_base_64 = self._load_ui_automation_example(os.path.join(data_path, "test_qa_element", "d19ccf1d-23b7-48b7-ace8-087313f67884_step_1.json"))

        target_box = BoxCoordinates(**{"x": 707, "y": 241, "width": 200, "height": 52})  # subtask button
        anchor_boxes = [
            BoxCoordinates(**{"x": 569, "y": 249, "width": 130, "height": 53}),
            BoxCoordinates(**{"x": 697, "y": 504, "width": 120, "height": 32}),
        ]  # [attach_button, comments_button]

        request = QaGetElementDescriptionRequest(target_box=target_box, image=image_base_64, anchor_boxes=anchor_boxes, rawDOM=None)
        response = await service.get_element_description(request)

        assert "subtask" in response["description"].lower(), f"Expected 'create subtask' in description, got {response['description']}"

    async def test_service_qa_description_target_in_view_with_not_matching_anchors(self):
        service = self._setup_ui_automation_service(opt)
        _, image_base_64 = self._load_ui_automation_example(os.path.join(data_path, "test_qa_element", "d19ccf1d-23b7-48b7-ace8-087313f67884_step_1.json"))

        target_box = BoxCoordinates(**{"x": 707, "y": 241, "width": 200, "height": 52})  # subtask button
        not_matching_anchor_boxes = [
            BoxCoordinates(**{"x": 666, "y": 871, "width": 130, "height": 53}),
            BoxCoordinates(**{"x": 1819, "y": 1120, "width": 50, "height": 32}),
        ]  # whitespace

        request = QaGetElementDescriptionRequest(target_box=target_box, image=image_base_64, anchor_boxes=not_matching_anchor_boxes, rawDOM=None)
        response = await service.get_element_description(request)

        assert "subtask" in response["description"].lower(), f"Expected 'create subtask' in description, got {response['description']}"

    async def test_service_qa_description_target_outside_view_with_anchors(self):
        service = self._setup_ui_automation_service(opt)
        sample_path = os.path.join(data_path, "BestBuy Fitbit fitness tracker", "f8eea419-af7a-47e3-bb7b-cac9b0da2dd8_step_1.json")
        elements, image_base_64 = self._load_ui_automation_example(sample_path)

        target_box = BoxCoordinates(**{"x": 1694, "y": 6268, "width": 32, "height": 32})
        anchor_boxes = [
            BoxCoordinates(**{"x": 1378, "y": 6208, "width": 125, "height": 16}),
        ]
        request = QaGetElementDescriptionRequest(target_box=target_box, anchor_boxes=anchor_boxes, image=image_base_64, rawDOM=elements)
        response = await service.get_element_description(request)

        generated_description = response.get("description", "")

        assert "share" in generated_description.lower(), f"Expected 'share' in description, got {generated_description}"

        request = QaElementWithImageRequest(image=image_base_64, human_description="Share on Pinterest", rawDOM=elements)
        raw_element = await service.qa_element(request)
        assert raw_element["element"]["Id"] == 85

    async def test_service_qa_description_target_outside_view_with_not_matching_anchors(self):
        service = self._setup_ui_automation_service(opt)
        sample_path = os.path.join(data_path, "BestBuy Fitbit fitness tracker", "f8eea419-af7a-47e3-bb7b-cac9b0da2dd8_step_1.json")
        elements, image_base_64 = self._load_ui_automation_example(sample_path)

        target_box = BoxCoordinates(**{"x": 1694, "y": 6268, "width": 32, "height": 32})

        not_matching_anchor_boxes = [
            BoxCoordinates(**{"x": 500, "y": 6000, "width": 30, "height": 53}),
            BoxCoordinates(**{"x": 600, "y": 6000, "width": 50, "height": 32}),
        ]

        request = QaGetElementDescriptionRequest(target_box=target_box, anchor_boxes=not_matching_anchor_boxes, image=image_base_64, rawDOM=elements)

        response = await service.get_element_description(request)
        generated_description = response.get("description", "")

        assert "share" in generated_description.lower(), f"Expected 'share' in description, got {generated_description}"

    async def test_service_qa_description_request_validator(self):
        service = self._setup_ui_automation_service(opt)
        dom_elements, image_base_64 = self._load_ui_automation_example(
            os.path.join(data_path, "test_qa_element", "d19ccf1d-23b7-48b7-ace8-087313f67884_step_1.json")
        )

        with self.assertRaises(BadRequestError):
            await service.get_element_description(
                QaGetElementDescriptionRequest(image=image_base_64, target_box=BoxCoordinates(**{"x": 0, "y": 0, "width": 0, "height": 0}), rawDOM=dom_elements)
            )

        with self.assertRaises(BadRequestError):
            await service.get_element_description(
                QaGetElementDescriptionRequest(image=image_base_64, target_box=BoxCoordinates(**{"x": 0, "y": 0, "width": 0, "height": 0}), rawDOM=None)
            )

    async def test_service_qa_get_description_bbox_no_dom_no_elementid(self):
        service = self._setup_ui_automation_service(opt)

        img_path = os.path.join(data_path, "test_qa_element", "image_no_text.png")

        with open(img_path, "rb") as img_file:
            image_base_64 = base64.b64encode(img_file.read()).decode("utf-8")

        box = BoxCoordinates(**{"x": 1029, "y": 53, "width": 43, "height": 50})

        request = QaGetElementDescriptionRequest(target_box=box, image=image_base_64, rawDOM=None)
        out = await service.get_element_description(request)
        generated_description = out.get("description", "")

        qa_request = QaElementWithImageRequest(image=image_base_64, human_description=generated_description, rawDOM=None)
        raw_element = await service.qa_element(qa_request)
        pred_x, pred_y, pred_width, pred_height = [int(x) for x in raw_element["element"]["Area"].split(",")]
        predicted_box = BoxCoordinates(pred_x, pred_y, pred_width, pred_height)
        self._assert_iop_value(predicted_box, box)

    async def test_extract(self):
        datapoint_path = os.path.join(data_path, "BestBuy Fitbit fitness tracker")

        dom_path = os.path.join(datapoint_path, "f8eea419-af7a-47e3-bb7b-cac9b0da2dd8_step_1.json")
        with open(dom_path) as f:
            json_content = json.load(f)
        dom = json_content["ExtractedDOM"]["RawDOM"]["RawElements"]
        image_base_64 = json_content["ImageBase64"]

        service = self._setup_ui_agent_automation_service(opt)
        request = ExtractInformationRequest(
            raw_dom=dom,
            image=image_base_64,
            prompt_description="What are the specs of the Night Owl",
            title=json_content["ExtractedDOM"]["Title"],
            url=json_content["ExtractedDOM"]["Url"],
        )
        out, _predict_info = await service.extract(request)
        assert "description" in out["data"]
        assert "12 channel" in str(out["data"]).lower()

    async def test_popup_match(self):
        image_path = os.path.join(data_path, "test_popup_match", "In-page_pop-up_window_advertisement.jpg")
        with open(image_path, "rb") as img_file:
            image_base_64 = base64.b64encode(img_file.read()).decode("utf-8")

        service = self._setup_ui_automation_service(opt)
        request = PopUpMatchRequest(image=image_base_64, descriptions=["Real estate", "Discount", "Virus"])
        out = await service.close_popup(request)
        assert out["description_id"] == 1
        assert out["bounding_box"] is not None

        request = PopUpMatchRequest(image=image_base_64, descriptions=["Real estate", "Validation error", "Virus"])
        out = await service.close_popup(request)
        assert out["bounding_box"] is not None
        assert out["description_id"] is None

    async def test_close_popup_no_controls(self):
        image_path = os.path.join(data_path, "test_popup_match", "popup_no_controls.png")
        with open(image_path, "rb") as img_file:
            image_base_64 = base64.b64encode(img_file.read()).decode("utf-8")

        service = self._setup_ui_automation_service(opt)
        request = PopUpMatchRequest(image=image_base_64, descriptions=["Real estate", "Discount"])
        out = await service.close_popup(request)
        assert out["bounding_box"] is None
        assert out["description_id"] is None

    async def test_advice_model(self):
        """Test the AdviceModel with a fixed trace file."""
        # Load the trace data from the fixed location
        trace_file = os.path.join(data_path, "advice_trace", "advice.trace1.json")
        self.assertTrue(os.path.exists(trace_file), f"Trace file not found: {trace_file}")

        with open(trace_file, "r") as f:
            trace_data = json.load(f)

        # Verify trace data structure
        self.assertIn("Prompt", trace_data)
        self.assertIn("Prompt", trace_data["Prompt"])
        self.assertIn("Iterations", trace_data)
        self.assertIsInstance(trace_data["Iterations"], list)
        self.assertGreater(len(trace_data["Iterations"]), 0)

        # Test with images enabled
        model = AdviceModel()
        advice = await model.predict(trace_data=trace_data)

        # Basic validation of the advice output
        self.assertIsInstance(advice, str)
        self.assertGreater(len(advice), 500)

        # Check that the advice contains semantic placeholders
        self.assertIn("<", advice)  # Should contain semantic placeholders like <user_name>
        self.assertIn(">", advice)


if __name__ == "__main__":
    suite = unittest.defaultTestLoader.loadTestsFromTestCase(MainTests)
    success = unittest.TextTestRunner().run(suite).wasSuccessful()
    sys.exit(not success)
