import json
from collections import defaultdict

if __name__ == "__main__":
    with open("./web_voyager_all_tasks.json", "r", encoding="utf-8") as f:
        tasks = json.load(f)
    tasks_per_web = defaultdict(list)
    for task in tasks:
        tasks_per_web[task["web_name"]].append(task)

    # sample 11 task per webs
    sample_tasks = []
    for _web_name, tasks in tasks_per_web.items():
        # random.shuffle(tasks)
        sample_tasks.extend(tasks[:11])

    print("Sampled tasks: ", len(sample_tasks))
    with open("./web_voyager_top_10.json", "w", encoding="utf-8") as f:
        json.dump(sample_tasks, f, indent=2)
