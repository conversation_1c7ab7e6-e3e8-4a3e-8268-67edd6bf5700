[{"web_name": "Allrecipes", "id": "Allrecipes--0", "ques": "Provide a recipe for vegetarian lasagna with more than 100 reviews and a rating of at least 4.5 stars suitable for 6 people.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--1", "ques": "Find a recipe for a vegetarian lasagna that has at least a four-star rating and uses zucchini.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--2", "ques": "Find a recipe for a vegetarian lasagna under 600 calories per serving that has a prep time of less than 1 hour.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--3", "ques": "Locate a recipe for vegan chocolate chip cookies with over 60 reviews and a rating of at least 4.5 stars on Allrecipes.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--4", "ques": "Find a recipe for Baked Salmon that takes less than 30 minutes to prepare and has at least a 4 star rating based on user reviews.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--5", "ques": "Search for a popular Pasta Sauce with more than 1000 reviews and a rating above 4 stars. Create a shopping list of ingredients for this recipe.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--6", "ques": "Search for a vegetarian lasagna recipe that has at least a four-star rating and over 500 reviews.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--7", "ques": "Find a popular recipe for a chocolate chip cookie and list the ingredients and preparation steps.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--8", "ques": "Search for a recipe for <PERSON><PERSON> on Allrecipes that has at least 200 reviews and an average rating of 4.5 stars or higher. List the main ingredients required for the dish.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--9", "ques": "Find a high-rated recipe for vegetarian lasagna, list the key ingredients required, and include the total preparation and cook time stated on the recipe.", "web": "https://www.allrecipes.com/"}, {"web_name": "Allrecipes", "id": "Allrecipes--10", "ques": "Find The Most Popular Recipes of the 1960s, noting the recipe name, preparation time and total time of the second recipe in this collection.", "web": "https://www.allrecipes.com/"}, {"web_name": "Amazon", "id": "Amazon--0", "ques": "Search an Xbox Wireless controller with green color and rated above 4 stars.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--1", "ques": "Search for women's golf polos in m size, priced between 50 to 75 dollars, and save the lowest priced among results.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--2", "ques": "Find a gaming desktop with Windows 11 Home, and the disk size should be 1TB.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--3", "ques": "Find climbing gears and sort the results by price high to low. Answer the first 3 results after sorting.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--4", "ques": "Find the used Nintendo Switch Lite on Amazon then filter by 'Used - Good', tell me the cheapest one that is 'Used - Good'.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--5", "ques": "Find a Blue iPhone 12 Pro 128gb and add to cart.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--6", "ques": "Browse black strollers within 100$ to 200$ on Amazon. Then find one Among these black strollers with over 20,000 reviews and a rating greater than 4 star.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--7", "ques": "Browse the women's hiking boots on Amazon and filter the results to show only those that are waterproof and have a rating of at least 4 stars and size 6.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--8", "ques": "Find the cheapest Samsung-made Android tablet with screen between 10-10.9 inches on Amazon. Only answer the cheapest one.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--9", "ques": "Find a dog bed on Amazon that is washable and has a length of at least 30 inches.", "web": "https://www.amazon.com/"}, {"web_name": "Amazon", "id": "Amazon--10", "ques": "Find the cost of a 2-year protection for PS4 on Amazon.", "web": "https://www.amazon.com/"}, {"web_name": "Apple", "id": "Apple--0", "ques": "Compare the prices of the latest models of MacBook Air available on Apple's website.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--1", "ques": "Research the new features of the iOS 17 on Apple support and check its compatibility with the iPhone 12.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--2", "ques": "Compare the prices and chips for the iPhone 16 Pro and iPhone 16 Plus models directly from Apple's website.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--3", "ques": "Find the latest model of the iPhone and compare the price and screen size between the pro and pro max.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--4", "ques": "How much does it cost to buy a Macbook pro, 16-inch, Apple M4 Max chip with 16-core CPU, 40-core GPU, 64GB unified memory, 1TB SSD.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--5", "ques": "Check the release date and price for the latest version of the iPhone.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--6", "ques": "Find AirPods on Apple and how many types are currently available.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--8", "ques": "Identify and list the specifications of the latest iPad model released by Apple, including its storage options, processor type, and display features.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--10", "ques": "Find information on the latest (as of today's date) MacBook model, including its key features such as processor type, memory size, and storage capacity.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--11", "ques": "Get information about the latest iPad model released by Apple, including its release date if available, base storage capacity, and starting price available on Apple's official website.", "web": "https://www.apple.com/"}, {"web_name": "Apple", "id": "Apple--12", "ques": "What Apple Repair ways are mentioned on apple website, answer 2 of them.", "web": "https://www.apple.com/"}, {"web_name": "ArXiv", "id": "ArXiv--0", "ques": "Search for the latest preprints about 'quantum computing'.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--1", "ques": "Search for the latest research papers on quantum computing submitted to ArXiv within the last two days.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--2", "ques": "Look up the most recent papers related to 'cs.CL', select one and show its abstract.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--3", "ques": "Locate the most recent research paper about 'Algebraic Topology' under Mathematics published on ArXiv. Provide the title of the paper, the name of the authors, and the abstract.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--4", "ques": "Find the most recent research papers in Astrophysics of Galaxies. How many papers have been announced in the last day?", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--5", "ques": "Search papers about \"quantum computing\" which has been submitted to the Quantum Physics category on ArXiv. How many results in total. What if search in all archives?", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--6", "ques": "How many figures and tables are in the paper \"On the Sentence Embeddings from Pre-trained Language Models\"?", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--7", "ques": "Find the most recent paper submitted on machine learning in the Computer Science category posted on ArXiv.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--8", "ques": "What is the latest news on ArXiv?", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--9", "ques": "Find the latest research paper about neural networks published on ArXiv which has been submitted within the last week.", "web": "https://arxiv.org/"}, {"web_name": "ArXiv", "id": "ArXiv--10", "ques": "Visit ArXiv Help on how to withdraw an article if the submission is not yet announced.", "web": "https://arxiv.org/"}, {"web_name": "BBC News", "id": "BBC News--0", "ques": "Find a report on the BBC News website about recent developments in renewable energy technologies in the UK.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--1", "ques": "Read the latest health-related news article published on BBC News and summarize the key points discussed.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--2", "ques": "Read the latest article regarding the environmental impacts of deforestation published within the last month.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--3", "ques": "Check the leaderboard for Golf's DP World Tour in the SPORT section, what was the name of the most recent tournament, and how many teams have a Total of -10 strokes.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--4", "ques": "Find the latest article regarding the economic implications of climate change in Europe as reported by BBC News and summarize the central points.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--5", "ques": "Find the article \"What is climate change? A really simple guide\" and use it to answer what human activities are causing climate change.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--6", "ques": "Find the top story from BBC News in the technology section for today.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--7", "ques": "Find a AI-related story under Technology of Business. What is in the first picture in the story?", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--8", "ques": "Get a brief overview of the economic implications of the UK's latest trade deal posted on BBC News and the date when the article was published.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--9", "ques": "Find out which musician made the headlines in Music News.", "web": "https://www.bbc.com/news/"}, {"web_name": "BBC News", "id": "BBC News--10", "ques": "Identify the main headlines covering the UK's plan to tackle climate change on BBC News.", "web": "https://www.bbc.com/news/"}, {"web_name": "Booking", "id": "Booking--0", "ques": "Find a Mexico hotel with deals for July 25-26.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--1", "ques": "Find the cheapest available hotel room for a three night stay from 1st August in Jakarta. The room is for 2 adults, just answer the cheapest hotel room and the price.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--2", "ques": "Find a hotel in Ohio From August 20th to August 23th for 3 adults and 2 rooms.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--3", "ques": "Find a hotel with 4 star and above rating in Los Angeles for 3 days from August 18th.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--4", "ques": "Search for the cheapest Hotel near Kashi Vishwanath Temple that offer breakfast from August 25th - August 26th.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--5", "ques": "Search a hotel with free WiFi and air conditioning in Bali from August 1 to August 4, 2025.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--6", "ques": "Book one room which provides breakfast, and airport shuttle from August 22 to 25 in Los Angeles.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--7", "ques": "Find a hotel room on Aug 3-6 that is closest to National University of Singapore and costs less than $500", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--8", "ques": "Get the hotel with highest review score and free cancelation in Chennai for 20/08/2025 - 21/08/2025.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--9", "ques": "Find hotels for 2 adults in London with a price less than 250 dollars for four days starting from Aug 25. You must browse the page and offer at least 3 options.", "web": "https://www.booking.com/"}, {"web_name": "Booking", "id": "Booking--10", "ques": "Find a well-reviewed hotel in Paris with available bookings suitable for a couple (2 adults) on August 14-21, 2025, that offers free cancellation options.", "web": "https://www.booking.com/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--0", "ques": "Look up the pronunciation and definition of the word \"sustainability\" on the Cambridge Dictionary.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--1", "ques": "Find the pronunciation, definition, and a sample sentence for the word 'serendipity'.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--2", "ques": "Look up the pronunciation, definition, and example sentence for the word \"ubiquitous\" in UK and US English.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--3", "ques": "Look up the definition, pronunciation, and examples of the word \"zeitgeist.\"", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--4", "ques": "Look for the British English pronunciation of the word \"innovate\" and write down the International Phonetic Alphabet (IPA) notation, then find one example sentence provided in the Cambridge Dictionary that uses this word.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--5", "ques": "Learn the UK and US pronunciation of the word \"procrastination\", and find one example sentence that reflects its use in context.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--6", "ques": "Search for the word \"sustainability\" on the Cambridge Dictionary, what is the translation of sustainability into Chinese and French in the dictionary.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--7", "ques": "Look up the meaning, pronunciation, and an example sentence of the word \"gestalt\" using the Cambridge Dictionary.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--8", "ques": "Find three different meanings of \"dog\" in Cambridge Dictionary.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--9", "ques": "Look up the British pronunciation of the word \"euphoria\" and find an example sentence using that word on the Cambridge Dictionary.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Cambridge Dictionary", "id": "Cambridge Dictionary--10", "ques": "Look up the definition and pronunciation of the word \"impeccable\" and also find an example sentence using that word.", "web": "https://dictionary.cambridge.org/"}, {"web_name": "Coursera", "id": "Coursera--0", "ques": "Find a beginner-level online course about '3d printing' which lasts 1-3 months, and is provided by a renowned university.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--1", "ques": "Search for a beginner-level online course about Python programming, suitable for someone who has no programming experience on Coursera.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--2", "ques": "Find a Be<PERSON>ner's Spanish Specialization on Coursera and show all the courses in this Specialization.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--3", "ques": "Identify a new course or Specialization on Coursera related to Python Data Science, sort the courses by newest, what the first course is and which institution offers it.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--4", "ques": "Identify a course or Specialization on Coursera that helps business process management with with a rating 4.7.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--5", "ques": "Identify a Specialization on Coursera that teaches C++ programming for beginners, provide the name and what the learning outcomes are.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--6", "ques": "Identify a course on Coursera related to 'Artificial Intelligence for Healthcare' and note the course duration along with the number of quizzes in Assessments.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--7", "ques": "Find a course on Coursera that teaches Reinforcement Learning for Intermediate with a rating of at least 4.5. Provide the name of the course, the institution offering it, and the number of reviews it has received.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--8", "ques": "Find a free course related to 'R for Data Science' available on Coursera. Scroll to find a course with the Free tag. What language the course is taught in?", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--9", "ques": "Identify a Coursera course on artificial intelligence ethics that has a duration of less than 20 hours to complete and has been rated 4+ stars by participants.", "web": "https://www.coursera.org/"}, {"web_name": "Coursera", "id": "Coursera--10", "ques": "Locate an introductory course related to artificial intelligence on Coursera, ensuring it's suitable for beginners and contains at least one module discussing Ethical Considerations.", "web": "https://www.coursera.org/"}, {"web_name": "ESPN", "id": "ESPN--0", "ques": "Look up the current standings for the NBA Eastern Conference on ESPN.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--1", "ques": "Check the latest articles on ESPN for updates on any trades that occurred in the NBA within the past 2 days.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--3", "ques": "Retrieve the final score from the most recent NBA game broadcast on ESPN, including the playing teams' names and the date of the match.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--4", "ques": "Check ESPN for the final scores of NBA games that were played yesterday.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--5", "ques": "Identify the top scorer in the NBA from the latest completed game and note down the points scored, the team they play for, and their position on the team.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--6", "ques": "Find the result of the latest basketball game between the Los Angeles Lakers and the Boston Celtics, including the final score and top scorer from the match.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--7", "ques": "Retrieve the final score and a brief summary of the latest NBA game played by the Los Angeles Lakers as reported on ESPN.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--8", "ques": "Find information on ESPN about the top three scoring leaders in the NBA as of the last day of the regular season, and note which teams they play for.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--9", "ques": "Search on ESPN for how many teams have Los Angeles in their name and how many of them are NBA.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--10", "ques": "Check ESPN for the score and a brief recap of the latest college football championship game.", "web": "https://www.espn.com/"}, {"web_name": "ESPN", "id": "ESPN--11", "ques": "How many NBA teams are there and list all the teams with 'New' in their name.", "web": "https://www.espn.com/"}, {"web_name": "GitHub", "id": "GitHub--0", "ques": "Search for an open-source project related to 'climate change data visualization' on GitHub and report the project with the most stars.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--1", "ques": "Search for an open-source repository for machine learning in Python, specifically focused on decision trees, updated within the last 2 days.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--2", "ques": "Look for the trending Python repositories on GitHub with most stars.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--3", "ques": "Find out how much more package storage the Enterprise version has over Team in GitHub Pricing.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--4", "ques": "Find a popular JavaScript repository created in the last 30 days on GitHub with a Readme file.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--5", "ques": "Find a Python repository on GitHub that has been updated in the past 2 days and has at least 500 stars.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--6", "ques": "Search for an open-source project related to 'cryptocurrency wallet' updated in the past 30 days and provide the top three contributors.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--7", "ques": "Find the official GitHub repository for ALBERT and show me what files the repo changed in the most recent commit.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--8", "ques": "Look up the latest stable release version of Vuex and find out when it was published.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--9", "ques": "Locate a repository on GitHub that was created in the last week and has 50 or more stars. Provide brief details about the project's purpose and its programming language.", "web": "https://github.com/"}, {"web_name": "GitHub", "id": "GitHub--10", "ques": "If I start using Copilot Individual, how much US dollars will it cost per year and what features does it have?", "web": "https://github.com/"}, {"web_name": "Google Flights", "id": "Google Flights--0", "ques": "Book a journey with return option on same day from Edinburg to Manchester on September 28th and show me the lowest price option available.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--1", "ques": "Show me the list of one-way flights on September 17, 2025 from Chicago to Paris.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--2", "ques": "Find the lowest fare from all eligible one-way flights for 1 adult from JFK to Heathrow on September. 22.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--3", "ques": "Search for the one-way flight available from Calgary to New York on September. 1st with the lowest carbon dioxide emissions.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--4", "ques": "Search for one-way flights from New York to London on September. 26th and filter the results to show only non-stop flights.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--5", "ques": "Find flights from Chicago to London on 20 September and return on 23 September.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--6", "ques": "Search for a flight on September 19 and return on September 26 from Tel Aviv to Venice and Select First Class.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--7", "ques": "Find a round trip from Phoenix to Miami (September. 25th - September. 28th), show the First Class plane tickets for me that do not exceed $1320..", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--8", "ques": "Search a one-way filght from Dublin To Athens Greece for 1 Adult that leaves on June 30 and analyse the price graph for the next 2 months.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--9", "ques": "Find a one way economy flight from Pune to New York in September. 15th and show me how long it will take for flight transfer.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Flights", "id": "Google Flights--10", "ques": "Locate the cheapest round-trip flights from New York to Tokyo leaving on Sep 25, 2025, and returning on Oct 15, 2025.", "web": "https://www.google.com/travel/flights/"}, {"web_name": "Google Map", "id": "Google Map--0", "ques": "Find 5 beauty salons with ratings greater than 4.8 in Seattle, WA.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--1", "ques": "Tell me one bus stop that is nearest to the intersection of main street and Amherst street in Altavista.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--2", "ques": "Find Apple Stores close to zip code 90028", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--3", "ques": "The least amount of walking from Central Park Zoo to the Broadway Theater in New York.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--4", "ques": "Plan a trip from Boston Logan Airport to North Station.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--5", "ques": "Search for a parking garage near Thalia Hall in Chicago that isn't open 24 hours.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--6", "ques": "Find all Uniqlo locations in Chicago, IL.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--7", "ques": "Find bus stops in Alanson, MI", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--8", "ques": "Find a place to climb within 2 miles of zip code 90028.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--9", "ques": "Find the art gallery that is nearest to Los Angeles Hindu Temple.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Map", "id": "Google Map--10", "ques": "Search for a park in the state of California called Castle Mountains National Monument and find out it's Basic Information.", "web": "https://www.google.com/maps/"}, {"web_name": "Google Search", "id": "Google Search--0", "ques": "Find the initial release date for Guardians of the Galaxy Vol. 3 the movie.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--1", "ques": "<PERSON>'s bio", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--2", "ques": "Search for the latest news title about the NBA team the Los Angeles Lakers.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--3", "ques": "Show me a list of comedy movies, sorted by user ratings. Show me the Top 5 movies.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--4", "ques": "Show most played games in Steam. And tell me the number of players in In game at this time", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--5", "ques": "find the score of the latest nba game played by the phoenix suns.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--6", "ques": "Browse the monthly trending searches in Columbus.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--7", "ques": "Find the software requirements for iPhones that support AirDrop's ability to continue transmitting over the web when out of range.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--8", "ques": "Find the video on YouTube: 'Oscars 2023: Must-See Moments!'. Tell me who the first comment displayed under that video belongs to, and how many thumbs up and replies it has.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--9", "ques": "Show the rating of Prometheus movie on IMDb and Rotten Tomatoes.", "web": "https://www.google.com/"}, {"web_name": "Google Search", "id": "Google Search--10", "ques": "Find the no. 1 weekly charts ranked artist based on Billboard and tell me 10 most played song by this artist until now.", "web": "https://www.google.com/"}, {"web_name": "Huggingface", "id": "Huggingface--0", "ques": "Find a pre-trained natural language processing model on Hugging Face that can perform sentiment analysis, and make sure the model's last update is within March 2023.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--2", "ques": "Discover three new and popular open-source NLP models for language translation released in the past month on Huggingface.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--3", "ques": "Look up a model with a license of cc-by-sa-4.0 with the most likes on Hugging face.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--4", "ques": "Locate an open-source conversational AI model on Hugging Face, trained in English and list its main features and applications.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--5", "ques": "Find a model released on Hugging Face for recipe generation. Retrieve the information of the model, including its name, model size and tensor type.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--7", "ques": "Which is the most downloaded audio related dataset on Hugging face currently.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--8", "ques": "Retrieve an example of a pre-trained language model in natural language processing and identify the tasks it is specifically designed for, like translation or text summarization.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--9", "ques": "Find the most download machine translation model on Huggingface which focuses on English and Japanese (en-ja) and report the evaluation metrics stated for it.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--11", "ques": "Identify the latest updated image to video model available on Huggingface and summarize its main features.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--12", "ques": "Find the most recently updated machine learning model on Huggingface which focuses on Error Correction.", "web": "https://huggingface.co/"}, {"web_name": "Huggingface", "id": "Huggingface--13", "ques": "Search for LLaMA in the huggingface doc, what type is the spaces_between_special_tokens parameter in LlamaTokenizer and what is its default value.", "web": "https://huggingface.co/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--0", "ques": "derivative of x^2 when x=5.6", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--1", "ques": "Give a constraint on the set of inequalities for the inner region of the pentagram.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--2", "ques": "Calculate 3^71 and retain 5 significant figures in scientific notation.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--3", "ques": "Let g(x) be the integral of x^2 cos(2x). Write the expression of g(x).", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--4", "ques": "Pack 24 circles in a circle radius r. <PERSON><PERSON><PERSON> Densest known packing and Square packing. Then tell me the radius of the inner circles.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--5", "ques": "Show the solution of y\"(z) + sin(y(z)) = 0 from wolframalpha.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram <PERSON>--6", "ques": "Simplify x^5-20x^4+163x^3-676x^2+1424x-1209 so that it has fewer items.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--7", "ques": "Give the final angle and final length after 6s of a Spring pendulum with spring equilibrium length=0.12m, initial length=0.24m, initial angle=80deg, mass=1kg, spring constant=120 N/m .", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--8", "ques": "Give 12 lbs of 4-cyanoindole, converted to molar and indicate the percentage of C, H, N.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--9", "ques": "Annual energy production of Diablo Canyon 2 in 2010.", "web": "https://www.wolframalpha.com/"}, {"web_name": "<PERSON><PERSON>", "id": "Wolfram Alpha--10", "ques": "Give the geomagnetic field on June 20, 2023 in Oslo.", "web": "https://www.wolframalpha.com/"}]