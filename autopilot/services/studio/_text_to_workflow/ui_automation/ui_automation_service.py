import os
import xml.etree.ElementTree as ET
from copy import deepcopy
from dataclasses import dataclass
from typing import Any, Dict, List

import cv2
import numpy as np
import pybase64
from pydantic import BaseModel
from uipath_cv_client.client import CvClient
from uipath_cv_client.cv_dom_extractor import CVDomExtractor
from uipath_cv_client.dom_processing import (
    Box<PERSON>oordina<PERSON>,
    DomSourceType,
    find_raw_dom_element,
    get_dom_tree_from_elements,
    match_bbox_to_xml_element,
    parse_raw_dom,
    remove_attribute_for_all_elements,
)

from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.ui_automation.action import ActionType, Step
from services.studio._text_to_workflow.ui_automation.computer_use.models.cu_model import ComputerUseModel
from services.studio._text_to_workflow.ui_automation.llm_tasks.close_popup import close_popup
from services.studio._text_to_workflow.ui_automation.llm_tasks.find_duplicates import find_duplicates
from services.studio._text_to_workflow.ui_automation.llm_tasks.get_description import get_description
from services.studio._text_to_workflow.ui_automation.models.autopilot.summarize_model import SummarizeModel
from services.studio._text_to_workflow.ui_automation.models.autopilot.ui_automation_activities_model import UiAutomationActivitiesModel
from services.studio._text_to_workflow.ui_automation.models.prompt_helper import UIAState
from services.studio._text_to_workflow.ui_automation.models.ui_agent.actions_model import ActionPredictionModel
from services.studio._text_to_workflow.ui_automation.models.ui_agent.extract_info_model import ExtractInfoModel
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.v1.agent import UiPathComputerUseV1
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.v2.agent import UiPathComputerUseV2
from services.studio._text_to_workflow.ui_automation.object_repository_parser import ObjectRepositoryParser
from services.studio._text_to_workflow.ui_automation.qa_semantic.qa_model import QAModel
from services.studio._text_to_workflow.ui_automation.schemas.agent_act_on_screen import UiAgentRequest, UiAgentResponse
from services.studio._text_to_workflow.utils import errors, request_utils, telemetry_utils, uipath_cloud_platform
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType

LOGGER = telemetry_utils.AppInsightsLogger()


class QaElementWithDomRequest(BaseModel):
    human_description: str
    dom_xml: str = ""
    image: str | None = None
    add_label_matching_examples: bool = False


class QaElementWithImageRequest(BaseModel):
    human_description: str
    image: str
    rawDOM: List[dict] | None


class PopUpMatchRequest(BaseModel):
    descriptions: List[str]
    image: str


class QaGetElementDescriptionRequest(BaseModel):
    image: str
    target_box: BoxCoordinates
    rawDOM: List[dict] | None
    anchor_boxes: List[BoxCoordinates] | None = None


class ExtractInformationRequest(BaseModel):
    model_config = {"protected_namespaces": ()}

    raw_dom: List[dict] | None = None
    image: str | None = None
    prompt_description: str
    title: str | None = None
    url: str | None = None
    model_name: str | None = None


@dataclass
class DomInfo:
    raw_dom: dict | None
    image_array: np.ndarray | None = None
    dom_type: DomSourceType = DomSourceType.Driver


class RequestProcessor:
    def __init__(self, options):
        self.options = options
        self.or_parser = ObjectRepositoryParser()
        self.is_ui_agent = options.get("prompt_options", {}).get("is_ui_agent", False)

    def _get_auth_token(self, request_context: RequestContext):
        auth_token = uipath_cloud_platform.get_token_from_context(request_context)

        # some of the requests to the service are made using S2S authentication
        # right now CV does not support S2S authentication, so we will also receive a user token
        # in this case we will use the user token instead of the service token for the CV request
        if request_context and request_context.computer_vision_user_token and request_context.computer_vision_user_token != "":
            auth_token = request_context.computer_vision_user_token
        if auth_token is not None:
            auth_token = auth_token.split(" ")[1]
        return auth_token

    def _decode_and_process_image(self, image_base64: str) -> np.ndarray:
        image_blob = pybase64.b64decode(str(image_base64), validate=True)
        image_np = np.frombuffer(image_blob, np.uint8)
        image_array = cv2.imdecode(image_np, 1)
        image_array = cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB)
        return image_array

    async def get_request_data(self, request):
        request_data = await self.preprocess_request(request)
        image_size = request_data["image_array"].shape[:2] if request_data["image_array"] is not None else None
        request_context = request_utils.get_request_context()
        uia_state = UIAState(
            task_description=request_data["task"],
            variables=request_data["variables"],
            input_variables=request_data["input_variables"],
            output_variables=request_data["output_variables"],
            title=request_data["title"],
            url=request_data["url"],
            xml_dom=request_data["xml_dom"],
            is_object_dom=request_data["dom_type"] == DomSourceType.ObjectRepository,
            previous_steps=request_data["previous_steps"],
            image_base64=request_data.get("image_base64", None),
            image_size=image_size,
            raw_dom=request_data["raw_dom"],
            session_id=request_context.ui_task_session_id if request_context else None,
        )
        return uia_state, request_data

    async def extract_dom_data_from_request(
        self, request_raw_dom: List[dict] | None, raw_dom_type_value: str = "Driver", request_image: str | None = None
    ) -> DomInfo:
        dom_type = DomSourceType.Driver

        if raw_dom_type_value == "ObjectRepository":
            dom_type = DomSourceType.ObjectRepository
            raw_dom = self.or_parser.parse(request_raw_dom)
        else:  # raw_dom_type_value == "Driver"
            if request_raw_dom is not None:
                raw_dom = get_dom_tree_from_elements(request_raw_dom)
                if raw_dom is None and request_image is None:
                    raise errors.BadRequestError("Invalid request. rawDOM or image is required in the request.")
                dom_type = DomSourceType.Driver
            else:
                raw_dom = None

        image_array = None

        if request_image is not None:
            image_array = self._decode_and_process_image(request_image)
            if raw_dom is None:
                request_context = request_utils.get_request_context()
                auth_token = self._get_auth_token(request_context)

                cv_dom_extractor = CVDomExtractor(cv_client=CvClient.from_legacy_opt(self.options, user_token=auth_token))
                raw_dom = await cv_dom_extractor.extract_visual_dom_from_image_async(image_array)
                dom_type = DomSourceType.CV

        return DomInfo(raw_dom=raw_dom, image_array=image_array, dom_type=dom_type)

    async def preprocess_request(self, request, summary=False):
        if summary:
            user_task = ""
        else:
            user_task = request["userTask"]
        input_variables = request.get("input_variables", {})
        output_variables = request.get("output_variables", {})
        variables = request.get("variables", {})
        raw_dom_type_value = request.get("DOMType", "Driver")
        request_raw_dom = request["rawDOM"]
        title = request.get("title", "Unknown-title")
        url = request.get("url", "Unknown-url")
        request_image = request.get("image", None)

        dom_info = await self.extract_dom_data_from_request(request_raw_dom=request_raw_dom, raw_dom_type_value=raw_dom_type_value, request_image=request_image)

        if dom_info.raw_dom is None:
            raise errors.ServiceError(500, "Exception extracting the DOM")

        xml_dom = parse_raw_dom(
            dom_info.raw_dom,
            dom_type=dom_info.dom_type,
            options=self.options,
            image=dom_info.image_array,
            file_path=None,
            use_cv_cache=False,
        )

        request_data = {
            "xml_dom": xml_dom,
            "variables": variables,
            "input_variables": input_variables,
            "output_variables": output_variables,
            "title": title,
            "url": url,
            "image_base64": request_image,
            "image_array": dom_info.image_array,
            "task": user_task,
            "raw_dom": dom_info.raw_dom,
            "dom_type": dom_info.dom_type,
        }

        if not summary:
            previous_steps_dict = request.get("previousSteps", [])
            previous_steps: List[Step] = []
            if len(previous_steps_dict) > 0:
                for step_dict in previous_steps_dict:
                    if len(step_dict["actions"]) == 0:
                        console_logger.error("Empty action list in previous step")
                        continue
                    step = Step.from_request_dict(step_dict, is_ui_agent=self.is_ui_agent)
                    if len(step.actions) > 0:
                        previous_steps.append(step)
            request_data["previous_steps"] = previous_steps

        return request_data

    def process_response(self, step: Step, raw_dom: Dict | None, dom_type: DomSourceType | None, predict_info=None) -> dict:
        """Assign element box reference (dom or bounding box) to the action"""
        step_out: dict = {"description": step.description}
        actions_dict_lst: List[dict[str, Any]] = []

        for action in step.actions:
            action_dict = action.to_response_dict(self.is_ui_agent)

            if action_dict["method_type"] and ("element_id" in action_dict and action_dict["element_id"] != "" and action_dict["element_id"] is not None):
                element_id = int(action_dict["element_id"])
                raw_element = find_raw_dom_element(raw_dom, element_id)
                if raw_element is not None:
                    if dom_type == DomSourceType.CV:
                        action_dict["box_reference"] = raw_element["AbsoluteRegion"]
                        try:
                            xy_wh_coordinates = [float(val) for val in raw_element["AbsoluteRegion"].split(",")]
                            x_center = xy_wh_coordinates[0] + (xy_wh_coordinates[2] / 2)  # noqa
                            y_center = xy_wh_coordinates[1] + (xy_wh_coordinates[3] / 2)  # noqa

                            action_dict["parameters"]["position"] = [int(x_center), int(y_center)]
                            del action_dict["element_id"]
                            del action_dict["parameters"]["element_id"]
                        except Exception as ex:  # noqa
                            LOGGER.error(str(ex), {"raw_element": str(raw_element)})
                    elif dom_type == DomSourceType.ObjectRepository:
                        action_dict["box_reference"] = raw_element["reference"]
                    else:
                        action_dict["box_reference"] = element_id
            actions_dict_lst.append(action_dict)

        step_out["actions"] = actions_dict_lst
        response = {"step": step_out}
        return response


class UiAgentRequestProcessor(RequestProcessor):
    def __init__(self, options):
        super().__init__(options)
        self.is_ui_agent = True

    def process_response(self, step: Step, raw_dom: Dict | None, dom_type: DomSourceType | None, predict_info=None) -> dict:
        step_out: dict = {"description": step.description}

        step_out["additional_parameters"] = step.additional_parameters
        step_out["thought"] = step.additional_parameters.get("reasoning", "")

        respone_actions: List[dict[str, Any]] = []

        for action in step.actions:
            action_dict = action.to_response_dict(self.is_ui_agent)

            element_id = action_dict["parameters"].get("element_id", "")
            if element_id != "":
                raw_element = find_raw_dom_element(raw_dom, int(element_id))
                if raw_element is not None and dom_type == DomSourceType.CV:
                    xy_wh_coordinates = [float(val) for val in raw_element["AbsoluteRegion"].split(",")]
                    x_center = xy_wh_coordinates[0] + (xy_wh_coordinates[2] / 2)  # noqa
                    y_center = xy_wh_coordinates[1] + (xy_wh_coordinates[3] / 2)  # noqa

                    action_dict["parameters"]["position"] = [int(x_center), int(y_center)]
                    del action_dict["parameters"]["element_id"]

            respone_actions.append(action_dict)

        step_out["actions"] = respone_actions
        response = {"step": step_out}

        if predict_info is not None:
            token_usage = {
                "model": predict_info["requests"][0]["model"],
                "requests": len(predict_info["requests"]),
                "input_tokens": sum([r["token_usage"]["input_tokens"] for r in predict_info["requests"]]),
                "output_tokens": sum([r["token_usage"]["output_tokens"] for r in predict_info["requests"]]),
                "total_tokens": sum([r["token_usage"]["total_tokens"] for r in predict_info["requests"]]),
                "total_time": sum([r["time"] for r in predict_info["requests"]]),
            }
            response["token_usage"] = token_usage

        return response


class UiAutomationService(object):
    def __init__(self, options):
        self.uia_model = UiAutomationActivitiesModel(options)
        self.request_processor = RequestProcessor(options)
        self.extract_info_model = ExtractInfoModel(options)
        self.summarize_model = SummarizeModel(options)
        self.computer_use_model = ComputerUseModel(options)
        self.qa_model = QAModel(options)
        self.options = options
        self.is_ui_agent = False
        self.options["dom"]["display_containers_id"] = False
        self.options["dom"]["output_select_tag"] = False
        assert self.options["prompt_options"]["is_ui_agent"] is False, "Wrong prompt options for UiAutomationService"

    def _is_box_inside_image(self, box: BoxCoordinates, image: np.ndarray) -> bool:
        img_H, img_W, _ = image.shape
        if box.width <= 0 or box.height <= 0:
            return False
        return box.x >= 0 and box.y >= 0 and box.x + box.width <= img_W and box.y + box.height <= img_H

    def _to_element_dict(self, dom_element: dict, bounding_box_key: str, dom_type: DomSourceType) -> dict:
        return {
            "DomType": str(dom_type),
            "Area": dom_element[bounding_box_key],
            "Id": dom_element["Id"] if dom_type == DomSourceType.Driver else None,
        }

    async def qa_element_with_dom(self, request: QaElementWithDomRequest):
        element_id = await self.qa_model.qa_element(
            request.dom_xml,
            request.human_description,
            request.image,
            request.add_label_matching_examples,
            consuming_feature_type=ConsumingFeatureType.QA_ELEMENT,
        )
        return {"element_id": element_id}

    async def qa_element(self, request: QaElementWithImageRequest, request_headers: Dict[str, str | bool] | None = None) -> dict[str, dict | List[dict]]:
        dom_info: DomInfo = await self.request_processor.extract_dom_data_from_request(request_raw_dom=request.rawDOM, request_image=request.image)

        if dom_info.raw_dom is None:
            raise errors.ServiceError(500, "Exception extracting the DOM")

        options = deepcopy(self.options)
        bounding_box_key = "AbsoluteRegion"

        if dom_info.dom_type == DomSourceType.Driver:
            options["dom"]["display_containers_id"] = True
            bounding_box_key = "Area"

        xml_dom = parse_raw_dom(
            dom_info.raw_dom,
            dom_type=dom_info.dom_type,
            options=options,
            image=None,
        )

        dom_str = ET.tostring(xml_dom, encoding="unicode")
        element_id = await self.qa_model.qa_element(
            dom_str,
            request.human_description,
            request.image,
            consuming_feature_type=ConsumingFeatureType.QA_SCREEN,
            use_container_id=options["dom"].get("display_containers_id", False),
        )

        if element_id and element_id >= 0:
            target_element = find_raw_dom_element(dom_info.raw_dom, element_id)

            if target_element is not None:
                if not request_headers or not request_headers.get("x-uipath-semantic-duplicates-matching", False):
                    return {
                        "element": self._to_element_dict(target_element, bounding_box_key, dom_info.dom_type),
                    }

                duplicate_elements = []

                duplicates_response = await find_duplicates(dom_str, request.human_description, target_element, request.image, options)
                duplicate_elements += [find_raw_dom_element(dom_info.raw_dom, dup_id) for dup_id in set(duplicates_response.duplicate_ids)]

                qa_response: dict[str, List[dict]] = {"element": []}

                for duplicate_element in [target_element] + duplicate_elements:
                    if duplicate_element is not None:
                        box = [int(coord) for coord in duplicate_element[bounding_box_key].split(",")]
                        box = BoxCoordinates(box[0], box[1], box[2], box[3])
                        if self._is_box_inside_image(box, dom_info.image_array):
                            qa_response["element"].append(self._to_element_dict(duplicate_element, bounding_box_key, dom_info.dom_type))
                return qa_response  # type: ignore

        # a more descriptive error should be raised here
        return {}

    async def get_element_description(self, request: QaGetElementDescriptionRequest) -> Dict[str, str | None]:
        """
        Get the description of an element in the DOM using its bounding box.

        - If the element is not found in the driver DOM, it will fallback to the CV DOM.
        - The matched element (plus possibly the anchors) will be cropped from the image and sent along with the XML DOM to the LLM for semantic description generation.
        - The element can be outside of the viewport, in which case only the XML DOM constructed from the driver DOM will be sent to the LLM.

        """
        dom_info = await self.request_processor.extract_dom_data_from_request(request_raw_dom=request.rawDOM, request_image=request.image)

        if dom_info.raw_dom is None:
            raise errors.ServiceError(500, "Exception extracting the DOM")

        options = deepcopy(self.options)
        options["dom"]["add_element_coordinates"] = True

        if dom_info.dom_type == DomSourceType.Driver:
            options["dom"]["display_containers_id"] = True

        xml_dom_tree = parse_raw_dom(
            dom_info.raw_dom,
            dom_type=dom_info.dom_type,
            options=options,
            image=None,
        )

        element_id = match_bbox_to_xml_element(xml_dom_tree, request.target_box, iou_threshold=0.6)
        if dom_info.dom_type == DomSourceType.Driver and element_id == -1:
            # fallback to CV DOM if the element is not found in the driver DOM
            dom_info = await self.request_processor.extract_dom_data_from_request(request_raw_dom=None, request_image=request.image)
            xml_dom_tree = parse_raw_dom(
                dom_info.raw_dom,
                dom_type=dom_info.dom_type,
                options=options,
                image=None,
            )
            element_id = match_bbox_to_xml_element(xml_dom_tree, request.target_box, iou_threshold=0.6)

        anchors_ids = [match_bbox_to_xml_element(xml_dom_tree, box, iou_threshold=0.4) for box in request.anchor_boxes or []]
        anchors_ids = [anchor_id for anchor_id in anchors_ids if anchor_id != -1]

        # remove the coordinates attribute from the XML tree to reduce the size of the prompt
        remove_attribute_for_all_elements(xml_dom_tree, attribute="Coordinates")

        if dom_info.image_array is not None and self._is_box_inside_image(request.target_box, dom_info.image_array):
            anchors_inside_image = [anchor for anchor in request.anchor_boxes or [] if self._is_box_inside_image(anchor, dom_info.image_array)]

            boxes_inside_image = [request.target_box] + anchors_inside_image

            x_min = min([box.x for box in boxes_inside_image])
            y_min = min([box.y for box in boxes_inside_image])
            x_max = max([box.x + box.width for box in boxes_inside_image])
            y_max = max([box.y + box.height for box in boxes_inside_image])

            min_dim = min(x_max - x_min, y_max - y_min)
            border_percentage = self.options.get("prompt_options", {}).get("crop_border_percentage", 0.1)
            x_min = max(0, x_min - int(border_percentage * min_dim))
            y_min = max(0, y_min - int(border_percentage * min_dim))
            x_max = min(dom_info.image_array.shape[1], x_max + int(border_percentage * min_dim))
            y_max = min(dom_info.image_array.shape[0], y_max + int(border_percentage * min_dim))

            image_crop = dom_info.image_array[y_min:y_max, x_min:x_max, :]

            _, image_crop = cv2.imencode(".png", cv2.cvtColor(image_crop, cv2.COLOR_RGB2BGR))
            base64crop = pybase64.b64encode(image_crop.tobytes()).decode("utf-8")

        else:
            if element_id == -1:
                raise errors.BadRequestError("The provided target box is not found neither in the DOM nor in inside the image")
            base64crop = None

        description = await get_description(
            ET.tostring(xml_dom_tree, encoding="unicode"), element_id, anchors_ids=anchors_ids, image_base64=base64crop, options=options
        )
        return {"description": description}

    async def close_popup(self, request: PopUpMatchRequest) -> Dict:
        dom_info = await self.request_processor.extract_dom_data_from_request(request_raw_dom=None, request_image=request.image)
        if dom_info.raw_dom is None:
            raise errors.ServiceError(500, "Exception extracting the DOM")
        parsed_dom = parse_raw_dom(
            dom_info.raw_dom,
            dom_type=dom_info.dom_type,
            options=self.options,
            image=None,
        )
        dom_str = ET.tostring(parsed_dom, encoding="unicode")
        response = await close_popup(dom_str, request.descriptions, request.image, self.options)
        raw_element = find_raw_dom_element(dom_info.raw_dom, response.closing_element_id)

        if raw_element is not None:
            bounding_box = [int(coord) for coord in raw_element["AbsoluteRegion"].split(",")]
            bounding_box = {"x": bounding_box[0], "y": bounding_box[1], "width": bounding_box[2], "height": bounding_box[3]}
        else:
            bounding_box = None

        response = {"description_id": response.description_id, "bounding_box": bounding_box}

        return response

    async def predict(self, request) -> tuple[dict, dict]:  # TODO: we should add a schema for request - response
        uia_state, request_data = await self.request_processor.get_request_data(request)
        req_options = {"model_name": request.get("model_name", None)}
        predicted_answer, predict_info = await self.uia_model.predict(uia_state, req_options=req_options)
        out = self.request_processor.process_response(predicted_answer, request_data["raw_dom"], request_data["dom_type"])

        return out, predict_info

    async def summarize(self, request):
        request_data = await self.request_processor.preprocess_request(request)
        dom = request_data["dom"]
        title = request_data["title"]
        url = request_data["url"]
        # image = request_data["image"]
        out = await self.summarize_model.predict(dom, title, url)
        return {"summary": out}


class UIAgentAutomationService:
    def __init__(self, options):
        self.options = options
        self.options["engine_config"]["default_engine"] = self.options["engine_config"]["default_agent_use_engine"]
        self.request_processor = UiAgentRequestProcessor(self.options)
        self.uia_agent_model = ActionPredictionModel(self.options)
        self.extract_info_model = ExtractInfoModel(self.options)
        self.computer_use_model = ComputerUseModel(self.options)
        self.uipath_computer_use_model = UiPathComputerUseV1(self.options)
        self.uipath_computer_use_model_cv_grounding = UiPathComputerUseV2(self.options)
        self.is_ui_agent = True
        self.options["dom"]["display_containers_id"] = True
        self.options["dom"]["output_select_tag"] = True
        assert self.options["prompt_options"]["is_ui_agent"] is True, "Wrong prompt options for UIAgentAutomationService"

    async def predict(self, request: UiAgentRequest) -> tuple[dict, dict]:  # TODO: we should add a schema for request - response
        request: dict[str, Any] = request.model_dump()
        model_name = request.get("model_name", self.options["engine_config"]["default_agent_use_engine"])
        is_computer_use = model_name in ("openai-computer-use", "claude-computer-use")
        is_uipath_computer_use = request.get("metadata") and request["metadata"].get("uipath_computer_use", False)

        assert not (is_computer_use and is_uipath_computer_use), "Cannot use both computer use and UiPath computer use models at the same time"

        console_logger.debug("Model name: %s", model_name)
        if is_computer_use:
            assert len(request.get("rawDOM", [])) == 0, "rawDOM should be empty for computer use model"
            request_state = await self.computer_use_model.get_request_state(request)
            response, predict_info = await self.computer_use_model.predict(request_state)
            UiAgentResponse.model_validate(response)
            # get the OpenAI/Claude llm model name
            predict_info["llm_model_name"] = self.computer_use_model.get_llm_model_name(request_state)
            return response, predict_info
        elif is_uipath_computer_use:
            use_cv_grounding = request.get("metadata") and request["metadata"].get("use_cv_grounding", False)
            assert len(request.get("rawDOM", [])) == 0, "rawDOM should be empty for UiPath computer use model"
            if use_cv_grounding:
                # use the v2 model for UiPath computer use with CV grounding
                response, predict_info = await self.uipath_computer_use_model_cv_grounding.predict_request(request, model_name)
            else:
                # use the v1 model for UiPath computer use
                response, predict_info = await self.uipath_computer_use_model.predict_request(request, model_name)
            UiAgentResponse.model_validate(response)
            return response, predict_info
        else:
            uia_state, request_data = await self.request_processor.get_request_data(request)
            req_options = {"model_name": request.get("model_name", None)}
            predicted_answer, predict_info = await self.uia_agent_model.predict(uia_state, req_options=req_options)
            response = self.request_processor.process_response(predicted_answer, request_data["raw_dom"], request_data["dom_type"], predict_info=predict_info)

            if os.getenv("AGENT_ACT_WAIT", "false").lower() in ("true", "1"):
                if response["step"]["actions"][-1]["method_type"] not in (ActionType.Finish, ActionType.Wait, ActionType.ExtractInfo):
                    response["step"]["actions"].append({"description": "wait", "method_type": ActionType.Wait, "default_value": 1500, "parameters": {}})

            # look up optional trace flag in metadata
            if request.get("metadata") and request["metadata"].get("include_additional_trace_info", False):
                response["step"]["trace_data"] = ET.tostring(uia_state.xml_dom, encoding="unicode")

            if request.get("enable_prompt_debug_logging", False):
                # Add prompt data in the response to the client.
                # This is currently used only for debug logging.
                prompt_data: dict[str, str] = {
                    "user_message": uia_state.user_message,
                    "reflection_output": uia_state.reflection_output,
                }
                response["step"]["prompt_data"] = prompt_data

            UiAgentResponse.model_validate(response)

            predict_info["llm_model_name"] = model_name
            return response, predict_info

    async def extract(self, request: ExtractInformationRequest):
        model_name = request.model_name
        if model_name is None:
            model_name = self.options["engine_config"]["default_agent_use_engine"]
        dom_info = await self.request_processor.extract_dom_data_from_request(request_raw_dom=request.raw_dom, request_image=request.image)
        if dom_info.raw_dom is None:
            raise errors.ServiceError(500, "Exception extracting the DOM")
        req_options = {"model_name": request.model_name}
        dom = parse_raw_dom(
            dom_info.raw_dom,
            dom_type=dom_info.dom_type,
            options=self.options,
            image=dom_info.image_array,
            file_path=None,
            use_cv_cache=False,
        )

        out, predict_info = await self.extract_info_model.predict(
            prompt=request.prompt_description, dom=dom, title=request.title, url=request.url, image_base64=request.image, req_options=req_options
        )

        extract_response = {
            "data": out,
        }
        if predict_info is not None:
            token_usage = {
                "model": predict_info["requests"][0]["model"],
                "requests": len(predict_info["requests"]),
                "input_tokens": sum([r["token_usage"]["input_tokens"] for r in predict_info["requests"]]),
                "output_tokens": sum([r["token_usage"]["output_tokens"] for r in predict_info["requests"]]),
                "total_tokens": sum([r["token_usage"]["total_tokens"] for r in predict_info["requests"]]),
                "total_time": sum([r["time"] for r in predict_info["requests"]]),
            }
            extract_response["token_usage"] = token_usage

        predict_info["llm_model_name"] = model_name
        return extract_response, predict_info
