import json
import re
import xml.etree.ElementTree as ET
from collections import OrderedDict
from typing import Any, List, Literal, Optional, Union, get_args, get_origin

from services.studio._text_to_workflow.ui_automation.action_definition import (
    ActionType,
    FinishStatus,
    actions_client_server_mapping,
    agent_action_definitions,
    autopilot_action_definitions,
)
from services.studio._text_to_workflow.ui_automation.computer_use.utils.keyboard_utils import KeyMapper
from services.studio._text_to_workflow.ui_automation.models.autopilot.prompting.autopilot_steps_example import autopilot_step_examples
from services.studio._text_to_workflow.ui_automation.models.ui_agent.prompting.agent_steps_example import agent_step_examples
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import ValidationException, parse_message_json

actions_server_client_mapping = {v: k for k, v in actions_client_server_mapping.items()}


def serialize_type(annotation: Any):
    """Convert any type annotation into a JSON-compatible string representation."""

    # Handle Literal types (e.g., Literal["up", "down"])
    if get_origin(annotation) is Literal:
        return f"Literal{repr(get_args(annotation))}"

    # Handle Union types (e.g., Union[int, str])
    if get_origin(annotation) is Union:
        return f"Union[{', '.join(serialize_type(arg) for arg in get_args(annotation))}]"

    # Handle basic types (int, str, float, bool, NoneType)
    if isinstance(annotation, type):
        return annotation.__name__  # Converts <class 'int'> -> "int"

    # Handle generic types (like List[int], Dict[str, int], etc.)
    if get_origin(annotation) is not None:
        args = ", ".join(serialize_type(arg) for arg in get_args(annotation))
        return f"{get_origin(annotation).__name__}[{args}]"

    # Fallback: Convert unknown types to string
    return str(annotation)


def can_convert_to_type(value, target_type):
    if isinstance(target_type, str):
        return False
    try:
        target_type(value)
        return True
    except (ValueError, TypeError):
        return False


class ActionDefinition:
    def __init__(self, action_definition: dict):
        self.name = action_definition["name"]
        self.parameters: OrderedDict[str, type] = OrderedDict(action_definition["parameters"])
        self.description = action_definition["description"]

    def serialize_method(self, format) -> str:
        """Returns a string representation of the action definition.
        Example: "click(element_id:int), Click on an element with the corresponding element id."
        To be used inside the prompt message.
        """
        if format == "xml":
            parameter_list = [f"{name}:{serialize_type(value)}" for name, value in self.parameters.items()]
            parameters_val = ", ".join(parameter_list)
            return f"{self.name}({parameters_val})"
        elif format == "json":
            return json.dumps(
                {
                    "name": self.name,
                    "description": "...",
                    "parameters": {name: serialize_type(value) for name, value in self.parameters.items()},
                }
            )
        else:
            raise NotImplementedError()


AUTOPILOT_ACTIONS = {action_definition["name"]: ActionDefinition(action_definition) for action_definition in autopilot_action_definitions}

AGENT_ACTIONS = {action_definition["name"]: ActionDefinition(action_definition) for action_definition in agent_action_definitions}


def get_action_definition(is_desktop: bool, is_ui_agent: bool) -> List[ActionDefinition]:
    if is_ui_agent:
        if is_desktop:
            desktop_actions = [ActionDefinition(ad) for ad in agent_action_definitions if ad["name"] != ActionType.NavigateBack]
            return desktop_actions
        else:
            web_actions = [ActionDefinition(ad) for ad in agent_action_definitions]
            return web_actions
    else:
        return [ActionDefinition(ad) for ad in autopilot_action_definitions]


def build_actions_definition_str(is_ui_agent: bool, is_desktop: bool, format: str) -> str:
    """
    Returns a string representation of the action definitions.
    To be used inside the SYSTEM prompt message.
    """

    action_definitions = get_action_definition(is_ui_agent=is_ui_agent, is_desktop=is_desktop)

    return "\n\n".join([f"{ad.serialize_method(format=format)}  # {ad.description}" for ad in action_definitions])


class MethodParser:
    @staticmethod
    def parse_action(description: str, method: str, is_ui_agent: bool):
        supported_actions = AGENT_ACTIONS if is_ui_agent else AUTOPILOT_ACTIONS
        # # DAN: We could use abstract syntax trees for parsing the whole method expression. Example:
        # > import ast
        # > ast_tree = ast.parse(method)
        # > method_name = ast_tree.body[0].value.func.id
        # > parameters = [arg.value for arg in ast_tree.body[0].value.args]

        method = method.strip()
        start_paranthesis = method.index("(")
        end_paranthesis = method.rindex(")")
        method_name = method[: method.index("(")].strip()
        parameters = method[start_paranthesis + 1 : end_paranthesis]
        if method_name not in supported_actions:
            supported_methods_text = ",".join(list(supported_actions.keys()))
            raise ValidationException(f"Method {method_name} is not supported! Supported methods are: {supported_methods_text}.")

        action_def = supported_actions[method_name]

        if parameters is not None:
            # this matches strings '' and non commas, essentially splitting by commas that are exclusively outside strings
            # does not work for strings containing ' e.g.: "type_into(223, 'O'Hare')"
            parameters = re.findall(r"(?:\s*'[^']*'\s*)|(?:[^,]+)", parameters.strip())
            parameters = [x.strip() for x in parameters if x.strip() != ""]
            parameters = [x.strip("'") for x in parameters]

            # this does not work for "(223, 'hello, world')" - it separates the string as well
            # parameters = parameters.strip().split(",")
            # parameters = [x.strip() for x in parameters if x.strip() != ""]
        if parameters is None:
            parameters = []

        if len(parameters) != len(action_def.parameters):
            print("method:", method)
            raise ValidationException(f"Arguments for {method_name} are invalid!")

        parameter_dict = {}
        for param_value, (param_name, param_type) in zip(parameters, action_def.parameters.items(), strict=False):
            if not can_convert_to_type(param_value, param_type):
                error_message = f"Parameter {param_name}: {param_value} of {method_name} has invalid value type. Type {param_type} expected."
                raise ValidationException(error_message)

            parameter_dict[param_name] = param_type(param_value)

        action = Action(
            name=method_name,
            description=description,
            parameters=parameter_dict,
            is_ui_agent=is_ui_agent,
        )
        return action


class Action(object):
    def __init__(
        self, name: str, description: str, parameters: dict, action_id: str | None = None, result: Optional[dict | str] = None, is_ui_agent: bool = False
    ):
        self.id = action_id
        self.name = name
        self.parameters = parameters
        self.description = description
        self.result = result
        self.execution_error_message = ""
        self.is_ui_agent = is_ui_agent
        self.action_definition = AUTOPILOT_ACTIONS[name] if not is_ui_agent else AGENT_ACTIONS.get(name, None)
        # for eval only, to matc the id of the labeled/recorded action and the DOM element
        # in order to get a description of that action, since we don't label or record the action description
        self.target_element: Optional[ET.Element] = None

    @staticmethod
    def from_dict(action_dict: dict, is_ui_agent: bool):
        if "name" not in action_dict or "description" not in action_dict or "parameters" not in action_dict:
            raise ValidationException(f"Invalid action: {str(action_dict)}. Action must contain name, description and parameters keys!")

        action_name = action_dict["name"].lower()
        action_defs = AGENT_ACTIONS if is_ui_agent else AUTOPILOT_ACTIONS
        if action_name not in action_defs:
            raise ValidationException(f"Action {action_dict['name']} is not supported! Supported actions are: {', '.join(list(action_defs.keys()))}.")
        action_def = action_defs[action_name]
        for param in list(action_def.parameters.keys()):
            if param not in action_dict["parameters"]:
                params_str = ", ".join([f"{name}: {serialize_type(value)}" for name, value in action_def.parameters.items()])
                raise ValidationException(f"Action {action_name} is missing parameter {param}. It must have the following parameters: {params_str}.")
            param_value = action_dict["parameters"][param]
            param_type = action_def.parameters[param]

            # if param_type is a actual type (not typing.Literal for example)
            if isinstance(param_type, type) and not can_convert_to_type(param_value, param_type):
                raise ValidationException(f"The parameter {param}  with value {str(param_value)} does not have expected type: {serialize_type(param_type)}")

        if action_def == ActionType.SendKeys:
            # for backward compatibility
            if "keys" in action_dict["parameters"]:
                supported_keys = ",".join([item.lower() for item in KeyMapper.special_keys.values()])
                keys = action_dict["parameters"]["keys"]
                if not isinstance(keys, list):
                    raise ValidationException(f"Action {action_name} parameter keys should be list")
                else:
                    for key in keys:
                        if key.lower() not in supported_keys:
                            raise ValidationException(f"Action {action_name} unsupported key {key}. Supported keys: [{supported_keys}]")
                    mapped_keys = [KeyMapper.map_openai_key(key) for key in keys]
                    action_dict["parameters"]["keys"] = mapped_keys

        return Action(
            name=action_dict["name"].lower(),
            description=action_dict["description"],
            parameters=action_dict["parameters"],
            is_ui_agent=is_ui_agent,
        )

    def assign_action_target(self, dom):
        if len(self.parameters) > 0 and "element_id" in self.parameters:
            element_id = self.parameters["element_id"]
            self.target_element = dom.find(f'.//*[@Id="{element_id}"]')
        else:
            return None

    def serialize_method(self, format: str = "xml") -> str:
        """
        Returns a string representation of the method, which is action without the description.
        Example: "click(14)" for click action.
        """
        if format == "xml":
            parameter_strs = []
            # in correct order
            for param_name, param_type in self.action_definition.parameters.items():
                param_value = self.parameters[param_name]
                if param_type is str:
                    o_param_value = str(param_value)
                    o_param_value = o_param_value.replace('"', "'")  # translation done for xml syntax adherence
                    parameter_strs.append(f"'{o_param_value}'")
                else:
                    parameter_strs.append(str(param_value))
            method_str = f"{self.name}({', '.join(parameter_strs)})"
            return method_str
        else:
            raise NotImplementedError()

    def serialize(self, format: str = "xml") -> str:
        if format == "xml":
            method_str = self.serialize_method()
            return f"""<Action description="{self.description}" method="{method_str}"/>"""

        elif format == "json":
            return f"""{{"description": "{self.description}", "name": "{self.name}", "parameters": {json.dumps(self.parameters)}}}"""
        else:
            raise NotImplementedError()

    def prompt_action_repr(self, format: str) -> str:
        """
        Return a string description of the action to use in the prompt message
        corresponding to the previous steps.
        For example: "Click on the Flights tab to book flight click(56)"
        """
        if self.target_element is not None:
            # for evaluation only where we don't have action description in recorded actions
            # TODO: improve using estimaton from the action and the target element info
            target_str = ET.tostring(self.target_element, encoding="utf-8").decode("utf-8")
            if format == "xml":
                action_str = f"{self.serialize_method()} on dom element {target_str}"
            elif format == "json":
                action_str = f"{self.serialize()} on dom element {target_str}"
            else:
                raise NotImplementedError()
        elif len(self.description.strip()) == 0:
            # this should not happen but for backward compatibility
            if format == "xml":
                action_str = self.serialize_method()
            elif format == "json":
                action_str = self.serialize()
            else:
                raise NotImplementedError()
        else:
            action_str = self.description

        if self.result is not None:
            action_str += f". Action result: {json.dumps(self.result)}"

        return action_str

    # FOR UIA SERVICE
    @staticmethod
    def from_request_dict(action_dict: dict, is_ui_agent: bool):
        """Parse the action dictionary as in the request"""
        name = action_dict["method_type"]

        if name in actions_client_server_mapping:
            name = actions_client_server_mapping[name]

        description = action_dict["description"]
        # if client send back parameters then use it (ui task)
        parameters = action_dict.get("parameters")
        result = action_dict.get("result")

        # client backward compat: for parsing result from the extraction in case client return the full response
        # client should return the "data" key only in future client release
        if result is not None and isinstance(result, dict) and "token_usage" in result and "data" in result:
            result = result["data"]
        if is_ui_agent:
            assert parameters is not None, "UI Agent client should send back parameters"
        # if not (autopilot) then read from the flatten keys
        else:
            parameters = {}
            for key in list(AUTOPILOT_ACTIONS[name].parameters.keys()):
                if key in action_dict:
                    parameters[key] = action_dict[key]
        return Action(
            action_id=action_dict.get("id"),
            name=name,
            description=description,
            parameters=parameters,
            result=result,
            is_ui_agent=is_ui_agent,
        )

    def to_response_dict(self, is_ui_agent: bool):
        """for UIA service response"""

        if self.name in actions_server_client_mapping:
            name = actions_server_client_mapping[self.name]
        else:
            name = self.name

        action_dict = {
            "description": self.description,
            "method_type": name,
            "parameters": self.parameters,
            "id": self.id,
        }

        if not is_ui_agent:
            action_dict["default_value"] = self.parameters.get("value", "")

        if self.result is not None:
            action_dict["result"] = self.result

        if not is_ui_agent:
            # flatten parameters out for backward compability
            for key, value in self.parameters.items():
                if key == "element_id":
                    # TODO: fix this, can we return int instead of str?
                    action_dict[key] = str(value)
                else:
                    action_dict[key] = value

        return action_dict


class Step(object):
    def __init__(
        self,
        description: str,
        actions: List[Action],
        screen_info: dict | None = None,
        image: str | None = None,
        additional_parameters: dict | None = None,
        matching_advice_step: str | None = None,
    ):
        self.description = description
        self.actions: List[Action] = actions
        self.screen_info = screen_info
        self.additional_parameters = additional_parameters
        self.image = image
        self.matching_advice_step = matching_advice_step

    @staticmethod
    def parse(message: str | dict, is_ui_agent: bool, format: str):
        """
        Parse the response message from LLM and return the Step object.
        """
        if format == "xml" and isinstance(message, str):
            return Step.parse_xml(message, is_ui_agent)
        elif format == "json" and isinstance(message, str):
            return Step.parse_json(message, is_ui_agent)
        elif format == "structured_output" and isinstance(message, dict):
            return Step.from_structured_output(message, is_ui_agent)
        else:
            raise NotImplementedError()

    @staticmethod
    def from_structured_output(message: dict, is_ui_agent: bool):
        message["actions"] = [
            {
                "name": action["name"],
                "description": action["description"],
                "parameters": {param: action[param] for param in action.keys() if param not in ["name", "description"]},
            }
            for action in message["actions"]
        ]
        step = Step.from_dict(message, is_ui_agent)
        return step

    @staticmethod
    def parse_xml(message: str, is_ui_agent: bool):
        """
        Parse the XML response message from LLM and return the Step object.
        """
        actions: List[Action] = []
        try:
            message = message.replace("&", "&amp;")
            tree = ET.fromstring(f"<root>{message}</root>")
        except ET.ParseError:
            print("Invalid Step XML syntax")
            message = Step.repair_xml(message)
            tree = ET.fromstring(f"<root>{message}</root>")

        if len(tree) != 1 or tree[0].tag != "Step":
            raise ValidationException("Response should contains only one step")

        step_dom = tree[0]
        if step_dom.get("description") is None:
            raise ValidationException("Step does not have description attribute")
        step_description = step_dom.get("description", "")
        for action_dom in step_dom:
            if action_dom.tag != "Action":
                raise ValidationException("Action does not have Action tag")
            if action_dom.get("description") is None:
                raise ValidationException("Action does not have description attribute")
            if action_dom.get("method") is None:
                raise ValidationException("Action does not have method attribute")
            action = MethodParser.parse_action(
                action_dom.get("description", ""),
                action_dom.get("method", ""),
                is_ui_agent,
            )
            actions.append(action)

        step = Step(step_description, actions)
        return step

    @staticmethod
    def parse_json(message: str, is_ui_agent: bool):
        """
        Parse the XML response message from LLM and return the Step object.
        """
        step_dict = parse_message_json(message)
        step = Step.from_dict(step_dict, is_ui_agent)
        return step

    @staticmethod
    def repair_xml(xml_string):
        # get the step tag
        step_tag = ""
        pattern = r'<Step description="[^"]*"(?=>)'
        match = re.search(pattern, xml_string)
        if match:
            step_tag = match.group(0)
        else:
            return ""

        # extract all correctly formated action tags
        action_tags = re.findall(r"<Action[^>]*>", xml_string)
        # reconstruct the XML string with the valid Action tags
        repaired_xml = f"{step_tag}>" + "".join(action_tags) + "</Step>"
        return repaired_xml

    @staticmethod
    def from_dict(step_dict, is_ui_agent: bool):
        return Step(
            description=step_dict["description"],
            actions=[Action.from_dict(action_dict, is_ui_agent) for action_dict in step_dict["actions"]],
            matching_advice_step=step_dict.get("matching_advice_step", None),
        )

    @staticmethod
    def from_request_dict(step_dict, is_ui_agent: bool = False):
        """Parse the step dictionary as in the request
        This has different keys for backward compatibility
        """
        step_description = step_dict["description"]
        additional_parameters = step_dict.get("additional_parameters", {})
        screen_info = {}
        if "title" in step_dict:
            screen_info["title"] = step_dict["title"]
        if "url" in step_dict:
            screen_info["url"] = step_dict["url"]

        steps_actions = []
        for action_dict in step_dict.get("actions", []):
            action = Action.from_request_dict(action_dict, is_ui_agent)
            error_message = action_dict.get("execution_error_message", "")
            action.execution_error_message = error_message
            # for select action
            detected_items = action_dict.get("detected_items")
            if detected_items is not None and len(detected_items) > 0:
                action.execution_error_message = f"The value to select '{action.parameters['value']}' is not a valid option of the Select element. The available options are: {', '.join(detected_items)}"
            steps_actions.append(action)
        step = Step(step_description, steps_actions, screen_info, additional_parameters=additional_parameters)
        return step

    def to_response_dict(self, flatten_parameters: bool):
        response_step = {"description": self.description, "additional_parameters": self.additional_parameters}
        response_actions = []

        for action in self.actions:
            action_dict = action.to_response_dict(is_ui_agent=True, flatten_parameters=flatten_parameters)
            response_actions.append(action_dict)

        response_step["actions"] = response_actions

        return response_step

    def serialize(self, format="xml") -> str:
        if format == "xml":
            actions_str = "\n  ".join([action.serialize(format="xml") for action in self.actions])
            return f"""<Step description="{self.description}">\n  {actions_str}\n</Step>"""
        elif format == "json":
            actions_str = ",\n    ".join([action.serialize(format="json") for action in self.actions])
            return f"""{{
  "description": "{self.description}",
  "actions": [
    {actions_str}
  ],
  "matching_advice_step": "{self.matching_advice_step}"
}}"""
        else:
            raise NotImplementedError()

    @staticmethod
    def create_finish_failure_step(description: str, is_ui_agent: bool):
        return Step(
            description=description,
            actions=[Action(name=ActionType.Finish, description=description, parameters={"status": FinishStatus.Failure}, is_ui_agent=is_ui_agent)],
            additional_parameters={},
        )

    @staticmethod
    def create_text_output_step(description: str, text: str, status: str, is_ui_agent: bool):
        return Step(
            description=description,
            actions=[Action(name=ActionType.Finish, description=description, parameters={"result": text, "status": status}, is_ui_agent=is_ui_agent)],
        )


def build_step_examples(is_ui_agent: bool, is_desktop: bool, format) -> str:
    step_examples = agent_step_examples if is_ui_agent else autopilot_step_examples
    steps = [Step.from_dict(example, is_ui_agent=is_ui_agent) for example in step_examples]
    valid_steps = []

    supported_actions = get_action_definition(is_ui_agent=is_ui_agent, is_desktop=is_desktop)
    supported_actions_names = [action.name for action in supported_actions]

    for step in steps:
        invalid_example = False
        for action in step.actions:
            if action.name not in supported_actions_names:
                invalid_example = True
                break
        if not invalid_example:
            valid_steps.append(step)

    return "\n\n".join([step.serialize(format=format) for step in valid_steps])


if __name__ == "__main__":
    print(build_actions_definition_str(is_ui_agent=True, is_desktop=False, format="json"))
    print(build_actions_definition_str(is_ui_agent=False, is_desktop=False, format="xml"))

    print("\n\nSTEP EXAMPLES AUTOPILOT")
    print(build_step_examples(is_ui_agent=False, is_desktop=False, format="xml"))
    print("\n\nSTEP EXAMPLES UI AGENT")
    print(build_step_examples(is_ui_agent=True, is_desktop=False, format="json"))

    step_str = """<Step description="Set flight search parameters and search for flights">
  <Action description="Click on the One-way button" method="click(144)"/>
  <Action description="Type 'New York' in the origin input box" method="type_into(147, '', 'New York')"/>
  <Action description="Type 'Chicago' in the destination input box" method="type_into(151, '', 'Chicago')"/>
  <Action description="Type 'Feb 8 2025' in the departure date input box" method="type_into(104, '', 'Feb 8 2025')"/>
  <Action description="Click on the Search button" method="click(125)"/>
</Step>"""
    step = Step.parse_xml(step_str, is_ui_agent=True)

    print("\n\nRESPONSE EXAPMLE")
    action = Action(description="description3", name="type_into", parameters={"element_id": 20, "variable": "$firstName", "default_value": "John"})
    print(action.to_response_dict(is_ui_agent=False))

    step_str = """json
{
  "description": "Fill in birthday and birth country in the application form using input variables",
  "actions": [
    {"description": "Type into the birthday input box using $birthDay variable", "name": "type_into", "parameters": {"element_id": 123, "variable": "$birthDay", "value": ""}},
    {"description": "Type into the birth country input box (JJ/MM/YYYY) using $birthCountry", "name": "type_into", "parameters": {"element_id": 132, "variable": "$birthCountry", "value": ""}}
  ]
}
"""
    print(Step.parse_json(step_str, is_ui_agent=True))
