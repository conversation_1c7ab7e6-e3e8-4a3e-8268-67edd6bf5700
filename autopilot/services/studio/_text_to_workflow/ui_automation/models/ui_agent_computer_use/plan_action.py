from typing import Union

from services.studio._text_to_workflow.ui_automation.computer_use.common.step import ComputerUseStep
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import ValidationException

key_maps = {
    "Backspace": "Back",
    "Ctrl": "Ctrl",
    "Shift": "Shift",
    "Tab": "Tab",
    "Enter": "Enter",
    "Escape": "Esc",
    "Arrowleft": "Left",
    "Arrowup": "Up",
    "Arrowright": "Right",
    "Arrowdown": "Down",
    "Delete": "Del",
    "Pageup": "PgUp",
    "Pagedown": "PgDn",
}


class PlanActionType:
    Click = "click"
    Type = "type"
    Scroll = "scroll"
    Drag = "drag"
    Wait = "wait"
    KeyPress = "key_press"
    MouseMove = "move_mouse"
    ExtractData = "extract_data"
    Finish = "finish"


VALID_PLAN_ACTIONS = [
    PlanActionType.Click,
    PlanActionType.Type,
    PlanActionType.Scroll,
    PlanActionType.Drag,
    PlanActionType.Wait,
    PlanActionType.KeyPress,
    PlanActionType.MouseMove,
    PlanActionType.ExtractData,
    PlanActionType.Finish,
]


class PlanAction:
    def __init__(self, action_type: str, description: str, parameters: dict | None = None):
        self.action_type = action_type
        self.description = description
        self.parameters = parameters if parameters is not None else {}

    def to_dict(self):
        return {
            "type": self.action_type,
            "description": self.description,
            "parameters": self.parameters,
        }

    @classmethod
    def from_dict(cls, data: dict | None) -> Union["PlanAction", None]:
        if data is None:
            return None

        action_type = data.get("type", "").lower()

        if action_type not in VALID_PLAN_ACTIONS:
            raise ValidationException(f"Invalid action type: {action_type}")

        target_element = data.get("target_element", None)

        action = PlanAction(
            action_type=action_type,
            description=data.get("description", ""),
            parameters=data.get("parameters", {}),
        )

        if target_element is not None:
            action.parameters["target_element"] = target_element

        return action


class TaskPlanAction:
    def __init__(self, action_type: str, description: str, parameters: dict | None = None, id: int | None = None):
        self.action_type = action_type
        self.description = description
        self.parameters = parameters if parameters is not None else {}
        self.previous_steps: list[ComputerUseStep] = []
        self.id = id

    def to_dict(self):
        return {
            "id": self.id,
            "type": self.action_type,
            "description": self.description,
            "parameters": self.parameters,
        }

    @classmethod
    def from_dict(cls, data: dict | None) -> Union["TaskPlanAction", None]:
        if data is None:
            return None

        action_type = data.get("type", "").lower()

        if action_type not in ["set_value", "act", "wait_load_completed", "extract_info_from_screen", "finish"]:
            raise ValueError(f"Invalid action type: {action_type}")

        action = TaskPlanAction(
            action_type=action_type, description=data.get("description", ""), parameters=data.get("parameters", {}), id=data.get("id", None)
        )

        return action

    def previous_steps_str(self) -> str:
        previous_steps_str = ""
        for step in self.previous_steps:
            previous_steps_str += f"\t - {step.description}\n"
            if step.additional_parameters is not None:
                if "extracted_data" in step.additional_parameters:
                    previous_steps_str += f"\t\t   Extracted data: {step.additional_parameters['extracted_data']}\n"
        return previous_steps_str.strip()
