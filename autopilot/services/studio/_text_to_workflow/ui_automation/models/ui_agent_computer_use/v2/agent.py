import json

from services.studio._text_to_workflow.ui_automation.computer_use.common.computer_action import ComputerUseAction
from services.studio._text_to_workflow.ui_automation.computer_use.common.step import ComputerUseStep
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import llm_client, utils
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.output_compiler import OutputComplier
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.plan_action import PlanActionType, key_maps
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.state import ExecutionState, State
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.v2.action_planner_cv import ActionPlanner, PlannerOutput
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.v2.grounder import Grounder
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.v2.history import build_history_str
from services.studio._text_to_workflow.ui_automation.schemas.agent_act_on_screen import SupportedActions
from services.studio._text_to_workflow.utils import request_utils


class UiPathComputerUseV2(object):
    def __init__(self, options: dict):
        self.options = options
        self.planner = ActionPlanner(options)
        self.executor = Grounder(options, use_cv_grounder=True)
        # Initialize the output compiler
        self.output_compiler = OutputComplier(options)
        self.use_chat_history = self.planner.use_chat_history
        self.use_last_image = self.planner.use_last_image

    async def predict_request(self, request_body: dict, model_name: str) -> dict:
        # Extract the state from the request body
        request_context = request_utils.get_request_context()
        session_id = request_context.ui_task_session_id if request_context else None
        request_id = f"{session_id}_{len(request_body.get('previousSteps', []))}"

        state = State(
            task=request_body["userTask"],
            image_base64=request_body["image"],
            previous_steps=request_body.get("previousSteps", []),
        )

        execution_state = ExecutionState(
            model_name=model_name,
            session_id=str(session_id),
            request_id=request_id,
            predict_info={},
            execution_info={},
        )

        return await self.predict(state, execution_state)

    def get_annotated_image(self, state: State, action: ComputerUseAction) -> str:
        """
        Annotate the image with the action position if applicable.
        """
        position = None
        if action.name in [SupportedActions.Click, SupportedActions.MouseMove, SupportedActions.Scroll]:
            position = action.parameters["position"]
        elif action.name == SupportedActions.Drag:
            start_pos = action.parameters["path"][0]
            position = (start_pos["x"], start_pos["y"])
        if position is not None:
            annotated_image = utils.annotate_image(state.image_base64, position)
        else:
            annotated_image = state.image_base64
        return annotated_image

    async def predict(self, state: State, execution_state: ExecutionState, retry_number: int = 0) -> tuple[dict, dict]:
        # Get the execution info

        planer_output: PlannerOutput = await self.planner.predict(state, execution_state)
        plan_action = planer_output.plan_action

        # TODO: validate plan_output
        action: ComputerUseAction | None = None
        step: ComputerUseStep | None = None

        match plan_action.action_type:
            case PlanActionType.KeyPress:
                keys = plan_action.parameters["key"].split(" ")
                keys = [key.strip() for key in keys]
                keys = [key_maps.get(key, key) for key in keys]
                action = ComputerUseAction(
                    name=SupportedActions.KeyPress,
                    description=plan_action.description,
                    parameters={"keys": keys},
                )
            case PlanActionType.Wait:
                action = ComputerUseAction(
                    name=SupportedActions.Wait,
                    description=plan_action.description,
                    parameters={},
                )
            case PlanActionType.ExtractData:
                # return a step with no action, just to store the extracted data
                step = ComputerUseStep(
                    description=plan_action.description,
                    actions=[],
                    additional_parameters={
                        "extracted_data": plan_action.parameters,
                    },
                    thought=planer_output.thought,
                )
            case PlanActionType.Finish:
                if plan_action.parameters.get("status", "").lower() == "success":
                    history_str = build_history_str(state)
                    task_output = await self.output_compiler.predict(state, history=history_str, execution_state=execution_state)
                else:
                    task_output = ""
                action = ComputerUseAction(
                    name=SupportedActions.Finish, description=plan_action.description, parameters=plan_action.parameters, result=task_output
                )
            case PlanActionType.Click | PlanActionType.MouseMove | PlanActionType.Scroll | PlanActionType.Drag:
                grounding_result = await self.executor.predict(state, plan_action.description)

                if grounding_result.position is None:
                    if grounding_result.bbox is None:
                        raise ValueError("Both position and bbox are None in grounding result")
                    x1, y1, x2, y2 = grounding_result.bbox
                    x, y = (x1 + x2) // 2, (y1 + y2) // 2
                else:
                    x, y = grounding_result.position

                if plan_action.action_type == PlanActionType.Scroll:
                    # guess the scroll direction if missing in the plan output
                    if "direction" not in plan_action.parameters:
                        console_logger.error("Scroll direction is missing in the plan action: %s", plan_action)
                        if "scroll up" in plan_action.description.lower():
                            scroll_direction = "up"
                        else:
                            scroll_direction = "down"
                    else:
                        scroll_direction = plan_action.parameters["direction"]

                    action = ComputerUseAction(
                        name=SupportedActions.Scroll, description=plan_action.description, parameters={"position": [x, y], "direction": scroll_direction}
                    )
                elif plan_action.action_type == PlanActionType.Drag:
                    assert grounding_result.end_position is not None, "End position must be provided for drag action"
                    x_end, y_end = grounding_result.end_position
                    action = ComputerUseAction(
                        name=SupportedActions.Drag,
                        description=plan_action.description,
                        parameters={"path": [{"x": x + 4, "y": y + 4}, {"x": x_end + 4, "y": y_end + 4}]},
                    )
                else:
                    assert plan_action.action_type in [SupportedActions.Click, SupportedActions.MouseMove]
                    action = ComputerUseAction(
                        name=plan_action.action_type,
                        description=plan_action.description,
                        parameters={"position": [x, y]},
                    )
            case PlanActionType.Type:
                action = ComputerUseAction(
                    name=SupportedActions.TypeInto,
                    description=plan_action.description,
                    parameters={"value": plan_action.parameters["text"]},
                )

        if step is None:
            assert action is not None
            step = ComputerUseStep(
                description=plan_action.description,
                actions=[action],
                additional_parameters={},
                thought=planer_output.thought,
            )

        # save additional data for history
        assert step.additional_parameters is not None
        step.additional_parameters["thought"] = planer_output.thought
        step.additional_parameters["review"] = planer_output.review
        step.additional_parameters.update(planer_output.additional_sections)
        step.additional_parameters["plan_action"] = json.dumps(plan_action.to_dict())

        if self.use_chat_history | self.use_last_image:
            if self.use_chat_history:
                history_image = state.image_base64
            else:
                history_image = self.get_annotated_image(state, action)

            previous_steps_parameters = {
                "max_chat_history_messages": 1000,
                "max_chat_history_images": self.planner.number_history_steps_with_images,
                "image": history_image,
            }
            agent_response = {"step": step.to_response_dict(), "previous_steps_parameters": previous_steps_parameters}
        else:
            agent_response = {"step": step.to_response_dict()}

        if llm_client.TRACE_FLAG:
            langfuse = llm_client.langfuse
            session_id = execution_state.session_id
            request_id = execution_state.request_id
            trace = langfuse.trace(id=f"{session_id}_{request_id}", session_id=session_id, name=request_id)
            trace.update(output=agent_response)
            # langfuse.flush()
        predict_info = {}
        return agent_response, predict_info
