import datetime
import json
from collections import OrderedDict

from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import llm_client
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.plan_action import PlanAction, key_maps
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.state import ExecutionState, State
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.v1.history import build_history_str
from services.studio._text_to_workflow.ui_automation.utils.messages_utils import ValidationException, parse_message_json
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()

system_template = """You are a computer use agent that perform computer-related tasks.
You will be given a task, a current screenshot, and a list of previous actions. You need to predict the next action.

## Action definitions:
Possible computer actions are: "click", "type", "scroll", "drag", "wait", "key_press", "move_mouse"

In addition, there are 2 special actions:
- "extract_data":  to extract some data from the screen for the task. This data will be stored in memory and used in the next actions or returned in the final result.
For extract_data action, you must give a short description of the data to be extracted as well as the actual data content. The data must be seen in the screenshot (do not guess).
- "finish": to finish the task with success or failure status. The failure status is used when the task is impossible to complete like being stuck in a loop.

Each action has a description and parameters. The action description is a single sentence which mentions the action as well as the control element to interact with.
This description will be used by the executor agent to locate the action's target element coordinates in the screen, so describe the element targeted by the action as detailed as possible.
Particularly for icons, you can describe their position, text on it, color, nearby elements etc...
Example of some action descriptions with more detailed information to help the executor agent locate the element:
- "Click on the Calendar icon with the text 'Thu 28' to open the calendar app"
- "Click the 'Search' button on the top right corner next to the login button."


Your action response must be a valid JSON with the following format:
{{
    "type": str  # one of the valid action types
    "description": # action description
    "target_element":  {{
                "type": str,  # the type of the target element, like "button", "input", "dropdown", "checkbox","radio button", "slider" etc...
                "label": optional[str],  # the label of the target element, like "Next", "Search", "Username", "Password" etc... Not available for all element types.                                          
                }}
    "parameters": # optional, action parameters dictionary 
}}

## Action parameters: parameters are required for the actions below
- "type": 
    - text: str  # the text to be typed

- "scroll":
    - direction: str  # the direction to scroll: "up", "down", "left" or "right"

- "extract_data": 
    - description: str  # short description of the data to be extracted
    - data: str | json  # the actual extracted data content

- "finish":
    - status: str  # the status of the task, must be "success" or "failure"

- "key_press":
    - key: str  # the key or key combination (separated by space) to be pressed. Example of key combination "Ctrl A", "Shift Tab", "Ctrl C" etc... Beside normal keys like letters, numerics, puntuations etc.. here are special key list: {key_list}

## Action examples: example of valid actions:
{{
    "type": "click",
    "description": "Click the 'Next' button to see the next page.",
    "target_element": 
    {{
        "type": "button",   
        "label": "Next",          
    }}
}}

{{
    "type": "click",
    "description": "Click the 'X' icon to delete the current input box text",
    "target_element": 
    {{
        "type": "icon",
        "label": "X",        
    }}
}}

{{
    "type": "type",
    "description": "Type 'John' in the first name input box.",
    "target_element": 
        {{
            "type": "input",
            "label": "First Name",            
        }}
    "parameters": {{ "text": "John" }}
}}

{{
    "type": "scroll",
    "description": "Scroll down to see more options in the dropdown menu.",
    "target_element": 
    {{
        "type": "dropdown",
        "label": "Country", 
    }},
    "parameters": {{ "direction": "down" }}
}}

{{
    "type": "key_press",
    "description": "Press Pagedown key to extract more data below.",
    "parameters": {{ "key": "Pagedown" }}
}}

{{
    "type": "mouse_move",
    "description": "Move the mouse over the 'Language' dropdown menu at the top menu bar."
    "target_element": 
    {{
        "type": "dropdown",
        "label": "Language",        
    }}
}}

{{
    "type": "drag",
    "description": "Drag the item into the folder"
    "target_element": 
    {{
        "type": "text",
        "label": "image.png",        
    }},
}}

{{
    "type": "extract_data",
    "description": "Extract the product name and price from the screen.",
    "parameters": {{
        "description": "product name and price",
        "data": "Product Name: iPhone 14, Price: $999"
    }}
}}

{{
    "type": "key_press",
    "description": "Press the 'Ctrl A' to select the current field input.",
    "parameters": {{ "key": "Ctrl A" }}
}}

{{
    "type": "finish",
    "description": "Finish the task with success",
    "parameters": {{ "status": "success" }}
}}
"""

user_message_template = """Here are the current information:
The current date is (YYYY-MM-DD): {current_date}
Task: {task}

Previous actions:
{history}
"""

user_command_template = """Recall Task Again: {task}
Check if the task is finished. If not provide the next action to perform.
Remember:
- Perform the task on provided application(s) or website(s). You are not allowed to use the browser "address bar".
- Close any cookies, ads, login or registration etc pop-ups if not needed.
- Only one action at a time (never "click and type", "click and drag", "type and press" etc..).
- Never type or click on invisible element.
- For any openning input combobox, dropdown menu options, you must select an option or press Enter key to select default one.
- Click on input box before typing.
- Once focusing on an input box, if it has a default pre-typed value (not placeholder which is usually grayed-out), remove the exising value first by clicking on "X" icon or using "Ctrl A" + "Backspace" or "Backspace" if the value is already selected.
- For search input, if no search button or suggestions popup after typing, press 'Enter' to trigger search.
- Retry the drag action on slider control if needed to refine the slider values closer to expected values.
- Scroll / Pageup / Pagedown to explore or extract more content/data if needed (prefer 'key_press' action with key 'Pageup', 'Pagedown' for faster scrolling). Particularly when extraction data from table with hidden rows or columns.
- Scroll action must have a 'direction' parameter. Finish action must have a 'status' parameter.

{execution_info_message}
Answer in json format:
{json_output_format}
"""

PlanerCoTSections = OrderedDict(
    {
        "review": {
            "display": "preview_action_result",
            "description": "Briefly describe the previous action result and UI change on the screenshot to see if is correctly performed.",
        },
        "thought": {"display": "thought", "description": "Reason briefly about the next action to perform if the task is not finished."},
        "is_element_visible": {
            "display": "is_element_visible",
            "description": "If the element you want to interact with is visible on the current screenshot answer 'yes' otherwise answer 'no' or 'N/A' (not applicable).",
        },
        # "scroll": {
        #     "display": "Scroll",
        #     "description": "If the element to interact with is not visible, you must scroll or page up/down to make it visible. If scrolling is needed, answer 'yes' or 'no' or 'NA'(not applicable). If 'yes', you must provide the scroll direction in the action parameters.",
        # },
    }
)


### for chat conversation
user_task_info_template = """## Task Information:
The current date is (YYYY-MM-DD): {current_date}
Task: {task}
"""


class PlannerOutput(object):
    def __init__(self, plan_action: PlanAction, additional_sections: dict[str, str]):
        self.plan_action = plan_action
        self.thought = additional_sections["thought"]
        self.review = additional_sections["review"]
        self.additional_sections = {key: value for key, value in additional_sections.items() if key not in ["review", "thought"]}
        if self.plan_action.parameters.get("target_element") is not None:
            target_element = self.plan_action.parameters["target_element"]
            type = target_element.get("type", "").lower()
            similar_input_types = ["input", "inputbox", "combobox", "dropdown"]
            if type in similar_input_types:
                target_element["type"] = "inputbox"
            self.target_element = target_element
        else:
            self.target_element = None


class ActionPlanner(object):
    def __init__(self, options: dict):
        self.planner_options = options["uipath-computer-use"]["planner"]
        self.use_chat_history = self.planner_options.get("use_chat_history", True)
        self.use_last_image = self.planner_options.get("use_last_image", False) and not self.use_chat_history
        self.number_history_steps_with_images = self.planner_options.get("number_history_steps_with_images", 4)

    def build_message_output_format_info(self) -> str:
        """Build the output format information for the user command."""
        output_dict = OrderedDict({})
        for _, value in PlanerCoTSections.items():
            display = value["display"]
            description = value["description"]
            output_dict[display] = description

        output_dict["action"] = "<The action to perform in JSON format as specified in the system message>"

        return json.dumps(output_dict, indent=4, ensure_ascii=False)

    def get_step_content(self, step: dict) -> str:
        content_dict = OrderedDict({})

        for key, value in PlanerCoTSections.items():
            param_value = step["additional_parameters"].get(key, None)
            display_name = value["display"]
            content_dict[display_name] = param_value
            content_dict["action"] = json.loads(step["additional_parameters"]["plan_action"])

        return json.dumps(content_dict, indent=4, ensure_ascii=False)

    def build_messages_chat(self, state: State, execution_info: dict) -> list[dict]:
        """Build messages for the chat model."""
        messages = []
        system_message = {
            "role": "system",
            "content": system_template.format(key_list=", ".join(key_maps.keys())),
        }

        messages.append(system_message)

        user_task_info_message = {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": user_task_info_template.format(
                        task=state.task,
                        current_date=datetime.datetime.now().strftime("%Y-%m-%d"),
                    ),
                }
            ],
        }

        messages.append(user_task_info_message)

        start_index = max(0, len(state.previous_steps) - self.number_history_steps_with_images)
        end_index = len(state.previous_steps)

        for index in range(0, end_index):
            step = state.previous_steps[index]

            if index >= start_index:
                assert step["image"] is not None and len(step["image"]) > 0, "Step image is empty"
                user_image_message = {
                    "role": "user",
                    "content": [
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{step['image']}"}},
                    ],
                }
                messages.append(user_image_message)

            assistant_message_text = self.get_step_content(step)

            assistant_message = {
                "role": "assistant",
                "content": [
                    {
                        "type": "text",
                        "text": assistant_message_text,
                    },
                ],
            }

            messages.append(assistant_message)

        # last user message with current screenshot
        last_user_message = {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "Current screenshot:",
                },
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{state.image_base64}"}},
                {
                    "type": "text",
                    "text": user_command_template.format(
                        task=state.task,
                        execution_info_message=self.build_execution_info_message(state, execution_info),
                        json_output_format=self.build_message_output_format_info(),
                    ),
                },
            ],
        }

        messages.append(last_user_message)
        return messages

    def build_messages(self, state: State, execution_info: dict) -> list[dict]:
        system_message = {
            "role": "system",
            "content": system_template.format(key_list=", ".join(key_maps.keys())),
        }
        contents = [
            {
                "type": "text",
                "text": user_message_template.format(
                    task=state.task,
                    history=build_history_str(state),
                    current_date=datetime.datetime.now().strftime("%Y-%m-%d"),
                ),
            }
        ]

        if self.use_last_image:
            if len(state.previous_steps) > 0:
                if state.previous_steps[-1].get("image") is None:
                    last_image = state.previous_steps[-1].get("additional_parameters", {}).get("image", None)
                else:
                    last_image = state.previous_steps[-1]["image"]
                assert len(last_image) > 0, "Last image is empty"
                contents.extend(
                    [
                        {
                            "type": "text",
                            "text": "Screenshot of the last step with potentially a red mark on the last action element:",
                        },
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{last_image}"}},
                    ]
                )

        contents.extend(
            [
                {
                    "type": "text",
                    "text": "Current screenshot:",
                },
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{state.image_base64}"},
                },
                {
                    "type": "text",
                    "text": user_command_template.format(
                        task=state.task,
                        execution_info_message=self.build_execution_info_message(state, execution_info),
                        json_output_format=self.build_message_output_format_info(),
                    ),
                },
            ]
        )

        user_message = {
            "role": "user",
            "content": contents,
        }
        messages = [system_message, user_message]
        return messages

    def extract_response(self, response_content: str) -> tuple[PlanAction, dict[str, str]]:
        cot_sections_lst = list(PlanerCoTSections.keys())

        additional_sections = OrderedDict({})
        response_json = parse_message_json(response_content)

        for section in cot_sections_lst:
            section_display = PlanerCoTSections[section]["display"]
            if section_display not in response_json:
                raise ValidationException(f"Invalid response format, '{section}' key not found: {response_content}")
            additional_sections[section] = response_json.get(PlanerCoTSections[section]["display"])

        if "action" not in response_json:
            raise ValidationException(f"Invalid response format, 'action' key not found: {response_content}")

        action_dict = response_json["action"]

        plan_action = PlanAction.from_dict(self.correct_action_type(action_dict))

        return plan_action, additional_sections

    def build_execution_info_message(self, state, execution_info: dict) -> str:
        execution_info_message = ""
        # add additional information to the execution info message
        return execution_info_message

    def correct_action_type(self, response_json: dict) -> dict:
        """Correct the action type in the response JSON if needed."""
        action_type = response_json.get("type", "").lower()
        if action_type in ("press", "key_press", "press_key"):
            response_json["type"] = "key_press"
        elif action_type in ("mouse_move", "move_mouse"):
            response_json["type"] = "move_mouse"
        elif action_type in ("type_text", "type_into", "type"):
            response_json["type"] = "type"
        elif "scroll" in action_type:
            response_json["type"] = "scroll"
        elif "wait" in action_type:
            response_json["type"] = "wait"
        return response_json

    async def predict(self, state: State, execution_state: ExecutionState) -> PlannerOutput:
        if self.use_chat_history:
            messages = self.build_messages_chat(state, execution_state.execution_info)
        else:
            messages = self.build_messages(state, execution_state.execution_info)

        llm_messages = [message for message in messages]
        repeat_count = 2
        while repeat_count > 0:
            try:
                response_content = await llm_client.send_messages(
                    llm_messages,
                    options={"model_name": execution_state.model_name},
                    metadata={"session_id": execution_state.session_id, "request_id": execution_state.request_id, "generation_name": "planner"},
                )
                console_logger.debug("Planner response: %s", response_content)
                if response_content is None:
                    raise ValueError("Planner response is None")

                # Try to extract the action and additional sections from the response content
                plan_action, additional_sections = self.extract_response(str(response_content))

                break
            except ValidationException as e:
                console_logger.error("Validation error: %s", e)
                repeat_count -= 1
                ai_message = {
                    "role": "assistant",
                    "content": [
                        {
                            "type": "text",
                            "text": response_content,
                        },
                    ],
                }
                error_message = {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": e.message,
                        },
                    ],
                }

                llm_messages = messages + [ai_message, error_message]

                if repeat_count == 0:
                    raise ValueError(f"Invalid planner response format: {response_content}")
        if plan_action is None:
            raise ValueError("Planner response is not valid")
        planner_output = PlannerOutput(
            plan_action=plan_action,
            additional_sections=additional_sections,
        )
        return planner_output
