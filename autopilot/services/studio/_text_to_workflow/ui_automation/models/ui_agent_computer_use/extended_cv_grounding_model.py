import asyncio
from copy import deepcopy

import cv2
import <PERSON><PERSON><PERSON><PERSON>
import numpy as np
import pybase64
from PIL import Image
from uipath_cv_client.client import CvClient
from uipath_cv_client.cv_dom_extractor import CVDomExtractor

from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use import utils
from services.studio._text_to_workflow.ui_automation.models.ui_agent_computer_use.plan_action import PlanActionType
from services.studio._text_to_workflow.utils import request_utils, uipath_cloud_platform


class ExtendedCVGroundingModel(utils.GroundingBaseModel):
    """
    Enhance grounding model with cv capabilities.
    """

    def __init__(self, options: dict, base_model: utils.GroundingBaseModel = None):
        self.options = options
        self.base_model = base_model
        options = deepcopy(options)
        self.options = options
        self.options["cv"]["tasks"]["detect_tables"] = False
        self.options["cv"]["tasks"]["detect_cells"] = False
        self.options["cv"]["tasks"]["get_controls_descriptions"] = False

    def _decode_and_process_image(self, image_base64: str) -> np.ndarray:
        image_blob = pybase64.b64decode(str(image_base64), validate=True)
        image_np = np.frombuffer(image_blob, np.uint8)
        image_array = cv2.imdecode(image_np, 1)
        image_array = cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB)
        return image_array

    def _get_auth_token(self, request_context: RequestContext):
        auth_token = uipath_cloud_platform.get_token_from_context(request_context)

        # some of the requests to the service are made using S2S authentication
        # right now CV does not support S2S authentication, so we will also receive a user token
        # in this case we will use the user token instead of the service token for the CV request
        if request_context and request_context.computer_vision_user_token and request_context.computer_vision_user_token != "":
            auth_token = request_context.computer_vision_user_token
        if auth_token is not None:
            auth_token = auth_token.split(" ")[1]
        return auth_token

    async def get_cv_dom(self, image_array: np.ndarray) -> dict:
        # request_context = request_utils.get_request_context()
        # auth_token = self._get_auth_token(request_context)
        request_context = request_utils.get_request_context()
        auth_token = self._get_auth_token(request_context)

        cv_dom_extractor = CVDomExtractor(cv_client=CvClient.from_legacy_opt(self.options, user_token=auth_token))
        raw_dom = await cv_dom_extractor.extract_visual_dom_from_image_async(image_array)
        return raw_dom

    def get_all_nodes_with_boxes(self, cv_dom: dict) -> tuple[list[dict], list[tuple[int, int, int, int]]]:
        """
        Get all nodes from the visual DOM.
        """
        nodes = []
        bboxes = []
        for node in cv_dom.get("Children", []):
            nodes.append(node)
            bboxes.append(self.get_node_bounding_box(node))
            child_nodes, child_bboxes = self.get_all_nodes_with_boxes(node)  # recursive call to get all children nodes
            nodes.extend(child_nodes)
            bboxes.extend(child_bboxes)
        return nodes, bboxes

    def get_nodes_by_text(self, cv_dom: dict, text: str, edit_distance: int | None = None) -> list[dict]:
        """
        Get nodes from the visual DOM that match the given text.
        """
        text_stripped = text.strip(":,*;.!?\"'").lower()
        nodes = []
        for node in cv_dom.get("Children", []):
            node_text = node.get("Text", "")
            score = 0
            if edit_distance is not None:
                score = Levenshtein.distance(node_text, text)
                if score <= edit_distance:
                    nodes.append(node)
            else:
                if node_text is not None and node_text != "":
                    node_text_stripped = node_text.strip(":,*;.!?\"'").lower()
                    if node_text_stripped != "":
                        if node_text_stripped == text_stripped:
                            nodes.append(node)
            nodes.extend(self.get_nodes_by_text(node, text, edit_distance))

        return nodes

    def get_nodes_by_type(self, nodes: list[dict], element_type: str) -> list[dict]:
        """
        Get nodes from the visual DOM that match the given text.
        """
        out_nodes = []
        for node in nodes:
            node_type = node.get("ElementType", "").lower()
            if node_type == element_type:
                out_nodes.append(node)
            children_nodes = node.get("Children", [])
            out_nodes.extend(self.get_nodes_by_type(children_nodes, element_type))

        return out_nodes

    def get_node_bounding_box(self, node: dict, scale_factor=1.0) -> tuple[int, int, int, int]:
        """
        Get the bounding box of a node.
        """
        absolute_region = node.get("AbsoluteRegion", "")
        if not absolute_region:
            return None
        x0, y0, w, h = map(int, absolute_region.split(","))
        x1 = x0 + w
        y1 = y0 + h
        return (x0 * scale_factor, y0 * scale_factor, x1 * scale_factor, y1 * scale_factor)

    def get_closest_boxes(self, boxes: np.ndarray, ref_point: np.ndarray, top_k=1) -> list[int]:
        """
        Find the closest boxes to a reference point.

        Args:
            boxes: (N, 4) array with boxes [x_min, y_min, x_max, y_max]
            ref_point: (2,) array [x_ref, y_ref]
            top_k: number of closest boxes to return

        Returns:
            indices of the closest boxes in sorted order of distance
        """
        # Clip the reference point to be inside each box (or on the border)
        clipped_points = np.stack(
            [
                np.clip(ref_point[0], boxes[:, 0], boxes[:, 2]),  # x
                np.clip(ref_point[1], boxes[:, 1], boxes[:, 3]),  # y
            ],
            axis=1,
        )

        # Compute Euclidean distance from clipped point to the reference point
        distances = np.linalg.norm(clipped_points - ref_point, axis=1)

        # Get the indices of the closest boxes
        closest_indices = np.argsort(distances)[:top_k]

        return closest_indices

    def pre_process_image(self, image_base64: str) -> tuple[np.ndarray, float]:
        """
        Pre-process the image for CV grounding.
        """
        image_array = self._decode_and_process_image(image_base64)
        # downscale the image by a factor of 2 to speed up the processing
        scale_factor = 1.0
        if image_array.shape[0] * image_array.shape[1] > 2000 * 2000:
            scale_factor = 0.5

        return image_array, scale_factor

    async def predict(self, request: utils.GroundingRequest) -> utils.GroundingOutput:
        """
        Predict the grounding output using both the base model and CV grounding.
        """

        base_image_array, scale_factor = self.pre_process_image(request.image_base64)
        scale_boxes_factor = 1.0 / scale_factor  # factor to scale the boxes back to the original image size
        if scale_factor != 1.0:
            console_logger.debug("Scaling image by factor: %s", scale_factor)
            image_array_resized = cv2.resize(base_image_array, (0, 0), fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_LINEAR)
        else:
            image_array_resized = base_image_array

        grounding_result, cv_dom = await asyncio.gather(self.base_model.predict(request), self.get_cv_dom(image_array_resized))
        console_logger.debug("Base model response: %s", grounding_result.__dict__)
        console_logger.debug("CV model response: %s", cv_dom)

        if grounding_result.position is None:
            if grounding_result.bbox is None:
                raise ValueError("Both position and bbox are None in grounding result")
            x1, y1, x2, y2 = grounding_result.bbox
            x, y = (x1 + x2) // 2, (y1 + y2) // 2
        else:
            x, y = grounding_result.position

        _, all_bboxes = self.get_all_nodes_with_boxes(cv_dom)  # get all nodes and bboxes from the visual DOM
        all_bboxes_scaled = np.asarray(all_bboxes, dtype=np.float32) * scale_boxes_factor

        indices_containing_point = np.where(
            (all_bboxes_scaled[:, 0] <= x) & (all_bboxes_scaled[:, 1] <= y) & (all_bboxes_scaled[:, 2] >= x) & (all_bboxes_scaled[:, 3] >= y)
        )[0]  # get indices of boxes that contain the point (x, y)

        current_action = request.action_type

        if (len(indices_containing_point) == 0) and (
            current_action != PlanActionType.Scroll
        ):  # if there are nodes containing the point we will keep the predicted point so we will return none
            # crop the image around the point (x,y) with 500 px to refine the grounding

            closest_indices = self.get_closest_boxes(all_bboxes_scaled, np.array([x, y]), top_k=2)
            padding = 10  # padding around the bounding boxes to include more context

            # make sure the region to include closest boxes is within the image bounds
            region_to_include_x1 = min(all_bboxes_scaled[closest_indices, 0] - padding)
            region_to_include_y1 = min(all_bboxes_scaled[closest_indices, 1] - padding)
            region_to_include_x2 = max(all_bboxes_scaled[closest_indices, 2] + padding)
            region_to_include_y2 = max(all_bboxes_scaled[closest_indices, 3] + padding)

            min_x = int(max(0, min(region_to_include_x1, x - 500)))
            min_y = int(max(0, min(region_to_include_y1, y - 500)))
            max_x = int(min(base_image_array.shape[1], max(x + 500, region_to_include_x2)))
            max_y = int(min(base_image_array.shape[0], max(y + 500, region_to_include_y2)))

            cropped_image = base_image_array[min_y:max_y, min_x:max_x]
            pil_image = Image.fromarray(cropped_image)
            cropped_image_base64 = utils.pil_image_to_base64(pil_image)
            request_refined = utils.GroundingRequest(
                description=request.description, image_base64=cropped_image_base64, target_element=request.target_element, label=request.label
            )

            grounding_result = await self.base_model.predict(request_refined)
            if grounding_result.position is None:
                if grounding_result.bbox is None:
                    raise ValueError("Both position and bbox are None in grounding result")
                x1, y1, x2, y2 = grounding_result.bbox
                grounding_result.bbox = x1 + min_x, y1 + min_y, x2 + min_x, y2 + min_y
            else:
                x, y = grounding_result.position
                grounding_result.position = x + min_x, y + min_y

        return grounding_result
