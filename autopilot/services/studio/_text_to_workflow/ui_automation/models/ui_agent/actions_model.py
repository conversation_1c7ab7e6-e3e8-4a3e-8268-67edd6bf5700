import copy
import json
import re
import time
import xml.etree.ElementTree as ET
from typing import List

import langchain.schema

from services.studio._text_to_workflow.ui_automation.action import Step, ValidationException
from services.studio._text_to_workflow.ui_automation.action_definition import ActionType, FinishStatus
from services.studio._text_to_workflow.ui_automation.cache import (
    CacheableUIAgentStep,
    add_step_data_to_cache,
    get_review_observation_from_cache,
)
from services.studio._text_to_workflow.ui_automation.llm_chat_providers import (
    get_chat_provider,
    get_model_name,
)
from services.studio._text_to_workflow.ui_automation.models.prompt_helper import (
    UIAState,
    build_ui_agent_system_prompt_data,
    build_user_message,
    build_user_prompt_data,
)
from services.studio._text_to_workflow.ui_automation.models.ui_agent.history_processor import HistoryProcessorModel
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent.objects import ModelResponse
from services.studio._text_to_workflow.ui_automation.models.ui_agent.output_compiler_model import (
    OutputCompilerModel,
)
from services.studio._text_to_workflow.ui_automation.models.ui_agent.prompting.text_to_action_message import (
    chat_message_invalid_reply_message,
    chat_system_message,
    chat_user_message,
    response_default_instruction,
    response_reasoning_instruction,
)
from services.studio._text_to_workflow.ui_automation.models.ui_agent.reflection_model import (
    ReflectionModel,
)
from services.studio._text_to_workflow.ui_automation.models.ui_agent.summary_model import (
    SummaryModel,
)
from services.studio._text_to_workflow.ui_automation.utils.ui_agent_types import (
    StepResponse,
)
from services.studio._text_to_workflow.utils.inference.llm_schema import (
    ConsumingFeatureType,
)


class ActionPredictionModel:
    def __init__(self, options):
        self.options = copy.deepcopy(options)
        self.prompt_config = options.get("prompt_options", {})
        self.action_format = self.prompt_config["action_format"]
        consuming_feature_type = ConsumingFeatureType.SCREEN_AGENT
        self.consuming_feature_type = consuming_feature_type
        self.use_structured_output = options.get("engine_config", {}).get("structured_output", False)
        self.summary_model = SummaryModel(options)
        self.reflection_model = ReflectionModel(options)
        self.output_compiler_model = OutputCompilerModel(options)

        self.use_cache = options["caching"]["enabled"]
        self.rebuild_history = self.prompt_config.get("review_observation", False) and self.use_cache

        self.history_processor_model = HistoryProcessorModel(options)
        assert self.prompt_config["is_ui_agent"] is True, "Wrong prompt options for ActionPredictionModel"

    def validate_step(self, step: Step, dom: ET.Element):
        """
        Validates the step actions against the dom
        - Return False if FIRST action is invalid (unexist element_id)
        - Otherwise stop at the first invalid action
        """
        last_valid_idx = 0
        for idx, action in enumerate(step.actions, 0):
            parameters = action.parameters
            if "element_id" in parameters:
                # auto convert element_id to int
                parameters["element_id"] = int(parameters["element_id"])
                element_id = parameters["element_id"]
                should_check_element_id = True
                if action.name == ActionType.Scroll and element_id <= 0:
                    # skip validation for scroll action
                    should_check_element_id = False
                if should_check_element_id:
                    element = dom.find(f".//*[@Id='{str(element_id)}']")
                    if element is None:
                        if idx == 0:
                            # raise only first action element id is incorrect
                            error_message = (
                                f"Action '{action.description}' is invalid. Element Id '{element_id}' for method {action.name} does not exist in the dom!"  # noqa
                            )
                            raise ValidationException(message=error_message)
                        else:
                            # stop here
                            break
            last_valid_idx = idx
        # remove the actions starting from the first invalid action
        step.actions = step.actions[: last_valid_idx + 1]

    def post_process_actions(self, step: Step):
        """
        Make sure that if finish, navigate back, extract info present, they are unique action
        No actions after click (we enforce that in the prompt but it can still happen)
        """
        for idx, action in enumerate(step.actions):
            unique_actions = [
                ActionType.Click,
                ActionType.Finish,
                ActionType.NavigateBack,
                ActionType.ExtractInfo,
                ActionType.Scroll,
            ]
            if idx == 0 and action.name in unique_actions:
                # no more actions after unique actions
                step.actions = step.actions[:1]
                break
            if idx > 0 and action.name in unique_actions:
                # remove unique actions if they follow other actions
                step.actions = step.actions[:idx]
                break
            if action.name == ActionType.Click:
                # remove all the actions after click
                step.actions = step.actions[: idx + 1]
                break

    def build_response_instruction(self):
        """Create instruction for giving response with format and optional reasoning"""

        if self.use_structured_output:
            return response_default_instruction.format(action_format=self.action_format)

        response_instruction = response_reasoning_instruction.format(action_format=self.action_format)
        return response_instruction

    def _get_model_response(self, prediction_out: langchain.schema.AIMessage | StepResponse) -> ModelResponse:
        if self.use_structured_output:
            if not isinstance(prediction_out, StepResponse):
                raise ValueError("Structured output is enabled but the prediction output is not of type StepResponse")
            predicted_step = Step.parse(
                prediction_out.model_dump(),
                is_ui_agent=True,
                format="structured_output",
            )
        else:
            if not isinstance(prediction_out, langchain.schema.AIMessage):
                raise ValueError("Structured output is disabled but the prediction output is not of type AIMessage")

            prediction_content = prediction_out.content
            # extract the reasoning part
            # Define the regular expression pattern
            pattern = (
                r"Observation:(?P<observation>.*?)"
                r"Reasoning:(?P<reasoning>.*?)"
                r"Next step:(?P<next_step>.*)"
            )

            # Search for the pattern in the input string
            match = re.search(pattern, prediction_content, re.DOTALL)

            if match:
                observation_string = match.group("observation").strip()
                reasoning_string = match.group("reasoning").strip()
                step_message = match.group("next_step").strip()

                predicted_step = Step.parse(
                    step_message,
                    is_ui_agent=True,
                    format=self.action_format,
                )
                predicted_step.additional_parameters = {"observation": observation_string, "reasoning": reasoning_string}
                return ModelResponse(observation_string, reasoning_string, predicted_step)
            else:
                raise ValidationException("The step does not have a correct format!")
        return ModelResponse(observation="", reasoning="", step=predicted_step)

    async def build_messages(self, uia_state: UIAState, predict_info: dict, req_options: dict | None = None) -> List[langchain.schema.BaseMessage]:
        screen_properties = {
            "object_dom": uia_state.is_object_dom,
            "desktop": uia_state.is_desktop,
        }
        system_prompt_elements = build_ui_agent_system_prompt_data(uia_state.image_base64, screen_properties, self.prompt_config)

        system_message_text = chat_system_message.format(**system_prompt_elements)
        messages: List[langchain.schema.BaseMessage] = [langchain.schema.SystemMessage(role="system", content=system_message_text)]
        model_name = get_model_name(self.options, req_options)

        if self.use_cache and self.rebuild_history:
            reviewed_observation_infos: dict[int, str] = {}
            reviewed_observations_info_list = get_review_observation_from_cache(uia_state.session_id)
            reviewed_observation_infos = {item[1]: item[0] for item in reviewed_observations_info_list}
            for step_index in reviewed_observation_infos.keys():
                if step_index >= len(uia_state.previous_steps):
                    break
                step = uia_state.previous_steps[step_index]
                reviewed_observation_info = reviewed_observation_infos[step_index]
                step.additional_parameters["reviewed_observation"] = reviewed_observation_info["reviewed_observation"]
                # append the token usage corresponding to the last reviewed observation
                if step_index == len(uia_state.previous_steps) - 1:
                    predict_info["requests"] = predict_info.get("requests", []) + [
                        {"model": reviewed_observation_info["model"], "token_usage": reviewed_observation_info["token_usage"], "time": 0.0}
                    ]

        use_history_messages = False
        if use_history_messages:
            history_messages = await self.history_processor_model.build_history_messages(uia_state, req_options)
            messages.extend(history_messages)
            history_str = ""
        else:
            history_str = self.history_processor_model.build_history_str(uia_state)

        user_prompt_elements = build_user_prompt_data(uia_state, self.prompt_config)
        user_prompt_elements["response_instruction"] = self.build_response_instruction()
        user_prompt_elements["history_str"] = history_str
        console_logger.debug(f"\nHistory:\n {history_str}")

        user_message = build_user_message(chat_user_message, user_prompt_elements, llm_model_name=model_name)
        messages.append(user_message)
        uia_state.user_message = user_message.text()
        return messages

    async def predict(
        self,
        uia_state: UIAState,
        req_options: dict | None = None,
    ) -> tuple[Step, dict]:
        predict_info = {}

        summary_interval = self.history_processor_model.summary_interval
        do_summary = len(uia_state.previous_steps) >= 2 * summary_interval and len(uia_state.previous_steps) % summary_interval == 0
        if do_summary:
            summary = await self.summary_model.predict(
                uia_state,
                predict_info=predict_info,
                req_options=req_options,
            )
            uia_state.summary = summary
        else:
            # get the previous saved summary
            if len(uia_state.previous_steps) > 0:
                uia_state.summary = uia_state.previous_steps[-1].additional_parameters["summary"]

        do_reflection = len(uia_state.previous_steps) % 5 == 0
        if do_reflection:
            reflection = await self.reflection_model.predict(uia_state=uia_state, predict_info=predict_info, req_options=req_options)
            uia_state.reflection = reflection

        messages = await self.build_messages(uia_state, predict_info=predict_info, req_options=req_options)

        response = await self.predict_with_retries(uia_state, messages, predict_info, req_options=req_options)
        step = response.step
        self.post_process_actions(step)

        # cache the step
        if self.use_cache:
            # State, Action
            if uia_state.session_id is None:
                raise ValueError("Session ID is required for action prediction")

            uia_state_dict = uia_state.to_dict_for_db()
            cacheable_step = CacheableUIAgentStep(model_prediction=response.to_dict(), uia_state=uia_state_dict)
            add_step_data_to_cache(uia_state.session_id, len(uia_state.previous_steps), cacheable_step)

        task_finished = False

        if step is not None and len(step.actions) == 1 and step.actions[0].name == ActionType.Finish:
            task_finished = True
            finishAction = step.actions[-1]
            if finishAction.parameters.get("status") != FinishStatus.Failure:
                finish_result = await self.output_compiler_model.predict(uia_state, step, predict_info=predict_info, req_options=req_options)
                console_logger.debug("\nFinish Action Result: %s", finish_result)
            else:
                finish_result = {}
            finishAction.result = str(json.dumps(finish_result, ensure_ascii=False))

        if step is not None:
            step.screen_info = {"Title": uia_state.title, "Url": uia_state.url}

            if self.rebuild_history and not task_finished:
                await self.history_processor_model.review_last_step(uia_state, response, predict_info, req_options, fire_and_forget=True)

            step.additional_parameters["summary"] = uia_state.summary
        return step, predict_info

    async def predict_with_retries(
        self,
        ui_state: UIAState,
        messages: List[langchain.schema.BaseMessage],
        predict_info: dict,
        retry_number: int = 0,
        req_options: dict | None = None,
    ) -> ModelResponse:
        """
        Predicts the next step, retry if step validation failed is raised
        If step validation failed twice, return a finish failure step
        """
        try:
            console_logger.debug("[actions_model] started")
            start_time_seconds = time.time()
            chat_provider = get_chat_provider(
                self.options,
                req_options,
                self.consuming_feature_type,
                response_format=StepResponse if self.use_structured_output else None,
            )

            gpt_response = await chat_provider.send_message(messages, predict_info)
            max_vadidation_retries = 2
            if self.use_structured_output:
                console_logger.debug("\n[actions_model] Action model Response: %s", gpt_response.model_dump())
            else:
                console_logger.debug("\n[actions_model] Action model Response: %s", gpt_response.content)

            # both parsing and actions validation can raise ValidationException
            response = self._get_model_response(gpt_response)
            # this removes the actions which have invalid target elements
            self.validate_step(response.step, ui_state.xml_dom)

            duration_seconds = time.time() - start_time_seconds
            console_logger.debug("[actions_model] duration_seconds: %s", duration_seconds)
            return response

        except ValidationException as ve:
            error_message = ve.message
            console_logger.error("Step validation error: %s", error_message)
            predict_info["ValidationException"] = predict_info.get("ValidationException", 0) + 1
            if retry_number < max_vadidation_retries:
                current_response = gpt_response.content if not self.use_structured_output else str(gpt_response.model_dump())
                console_logger.error(current_response)
                console_logger.error("Retry since validation error...")
                reply_message_content = chat_message_invalid_reply_message.format(
                    error_message=error_message,
                    action_format=self.action_format,
                    current_response=current_response,
                    response_instruction=self.build_response_instruction(),
                )

                reply_message = {
                    "type": "text",
                    "text": reply_message_content,
                }
                messages[-1].content.append(reply_message)
                return await self.predict_with_retries(
                    ui_state,
                    messages,
                    predict_info,
                    retry_number=retry_number + 1,
                    req_options=req_options,
                )
            else:
                console_logger.warn("Can not create valid step after multiple retries, return finish with failure!")
                return ModelResponse(
                    observation="", reasoning="", step=Step.create_finish_failure_step("Finish since can not create valid actions", is_ui_agent=True)
                )
