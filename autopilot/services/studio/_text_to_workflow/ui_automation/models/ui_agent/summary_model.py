import copy
import time
from typing import List, Optional

import langchain.chains
import langchain.prompts
import langchain.schema

from services.studio._text_to_workflow.ui_automation.llm_chat_providers import get_chat_provider, get_model_name
from services.studio._text_to_workflow.ui_automation.models.prompt_helper import (
    UIAState,
    build_ui_agent_system_prompt_data,
    build_user_message,
    build_user_prompt_data,
    default_prompt_config,
)
from services.studio._text_to_workflow.ui_automation.models.ui_agent.history_processor import HistoryProcessorModel
from services.studio._text_to_workflow.ui_automation.models.ui_agent.logger import console_logger
from services.studio._text_to_workflow.ui_automation.models.ui_agent.prompting.summary_prompt import (
    summary_system_message,
    summary_user_message,
)
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType


class SummaryModel:
    def __init__(self, options):
        self.options = copy.deepcopy(options)
        self.prompt_config = copy.deepcopy(default_prompt_config)
        self.prompt_config.update(options.get("prompt_options", {}))
        self.use_structured_output = options.get("engine_config", {}).get("structured_output", False)
        self.history_processor_model = HistoryProcessorModel(options)
        self.summary_interval = self.prompt_config["summary_interval"]

    def build_messages(
        self,
        uia_state: UIAState,
        req_options: dict | None = None,
    ) -> List[langchain.schema.BaseMessage]:
        user_prompt_data = build_user_prompt_data(uia_state, self.prompt_config)
        previous_step_str = self.history_processor_model.build_previous_steps_str(
            uia_state.previous_steps, start_k=0, end_k=len(uia_state.previous_steps) - self.summary_interval
        )
        user_prompt_data = {
            "previous_step_str": previous_step_str,
            "date": user_prompt_data["date"],
            "task_description": user_prompt_data["task_description"],
        }
        screen_properties = {"object_dom": uia_state.is_object_dom, "desktop": uia_state.is_desktop}
        system_prompt_data = build_ui_agent_system_prompt_data(uia_state.image_base64, screen_properties, self.prompt_config)
        system_message_text = summary_system_message.format(**system_prompt_data)
        messages: List[langchain.schema.BaseMessage] = [langchain.schema.SystemMessage(role="system", content=system_message_text)]
        model_name = get_model_name(self.options, req_options=req_options)
        messages.append(build_user_message(summary_user_message, user_prompt_data, llm_model_name=model_name))
        return messages

    async def predict(
        self,
        uia_state: UIAState,
        predict_info: dict,
        req_options: dict | None = None,
    ) -> Optional[str]:
        console_logger.debug("[summary_model] started")
        start_time_seconds = time.time()
        messages = self.build_messages(uia_state, req_options)
        chat_provider = get_chat_provider(
            self.options,
            req_options=req_options,
            consuming_feature_type=ConsumingFeatureType.SCREEN_AGENT,
        )
        output = await chat_provider.send_message(messages=messages, predict_info=predict_info)
        summary = str(output.content)
        console_logger.debug("\n[summary_model] Summary: %s", summary)
        duration_seconds = time.time() - start_time_seconds
        console_logger.debug("[summary_model] duration_seconds: %s", duration_seconds)
        return summary
