agent_step_examples = [
    {
        "description": "Go to employees page",
        "actions": [{"name": "click", "description": "Click on the employees tab", "parameters": {"element_id": 14}}],
        "matching_advice_step": "X.Y",
    },
    {
        "description": "Scroll to see more items",
        "actions": [
            {
                "name": "scroll",
                "description": "Scroll to the bottom of the list",
                "parameters": {"element_id": 10, "direction": "down", "number_of_scrolls": 6},
            },
        ],
        "matching_advice_step": "X.Y",
    },
    {
        "description": "Remove last recording",
        "actions": [
            {"name": "click", "description": "Press the remove button", "parameters": {"element_id": 18}},
        ],
        "matching_advice_step": "X.Y",
    },
    {
        "description": "Fill in user inscription form",
        "actions": [
            {"name": "type_into", "description": "Type 'John' in the first name input box", "parameters": {"element_id": 34, "variable": "", "value": "<PERSON>"}},
            {"name": "type_into", "description": "Type 'Doe' in the lastname name input box", "parameters": {"element_id": 36, "variable": "", "value": "Doe"}},
        ],
        "matching_advice_step": "X.Y",
    },
    {
        "description": "Set flight departure input combo box",
        "actions": [
            {
                "name": "type_into",
                "description": "Type 'Bucharest' in the departure input combo box",
                "parameters": {"element_id": 31, "variable": "", "value": "Bucharest"},
            }
        ],
        "matching_advice_step": "X.Y",
    },
    {
        "description": "Set the date using date picker",
        "actions": [{"name": "click", "description": "Click on the date picker to start setting the date to 'Jul 7 2023", "parameters": {"element_id": 47}}],
        "matching_advice_step": "X.Y",
    },
    {
        "description": "Here there is no way to find information about last world cup winner. Go back to the google search page.",
        "actions": [{"name": "browser_navigate_back", "description": "Go back to the search page to start another search", "parameters": {}}],
        "matching_advice_step": "X.Y",
    },
    {
        "description": "Get patient information",
        "actions": [
            {
                "name": "extract_info_from_screen",
                "description": "Extract patient information from the patient section",
                "parameters": {"prompt": "Extract all the important patient information from the patient section", "label": "Patient Information"},
            }
        ],
        "matching_advice_step": "X.Y",
    },
    {
        "description": "Press enter on the search input box to trigger the search",
        "actions": [
            {
                "name": "send_keys",
                "description": "Press enter on the search input box to trigger the search",
                "parameters": {"element_id": 17, "keys": ["Enter"]},
            }
        ],
        "matching_advice_step": "X.Y",
    },
    {
        "description": "Try to close selection popup, couldn't find an alternative way to close it.",
        "actions": [
            {
                "name": "send_keys",
                "description": "Press Escape key to close the selection popup",
                "parameters": {"element_id": 14, "keys": ["Esc"]},
            }
        ],
        "matching_advice_step": "X.Y",
    },
    {
        "description": "Wait for screen to load",
        "actions": [{"name": "wait_load_completed", "description": "Wait for table items to load. Currently a spinner is present.", "parameters": {}}],
        "matching_advice_step": "X.Y",
    },
    {
        "description": "Wait for the form to load",
        "actions": [{"name": "wait_load_completed", "description": "Wait for the form update from the prious step to completely loaded.", "parameters": {}}],
        "matching_advice_step": "X.Y",
    },
    {
        "description": "Finish with extracted data",
        "actions": [
            {
                "name": "finish",
                "description": "Task completed, the information required was extracted.",
                "parameters": {"status": "success"},
            }
        ],
        "matching_advice_step": "X.Y",
    },
]
