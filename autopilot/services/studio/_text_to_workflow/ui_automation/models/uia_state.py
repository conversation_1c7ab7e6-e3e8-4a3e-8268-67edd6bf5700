import copy
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Tuple

import cv2
import numpy as np
import pybase64

from services.studio._text_to_workflow.ui_automation.action import Step
from services.studio._text_to_workflow.ui_automation.models.ui_agent import image_utils


class UIAState:
    """Contains the state of the UIA model"""

    def __init__(
        self,
        task_description: str,
        variables: Dict[str, str],
        input_variables: Dict[str, str],
        output_variables: Dict[str, str],
        title: str,
        url: str,
        xml_dom: ET.Element,
        is_object_dom: bool,
        previous_steps: List[Step],
        raw_dom: List[Dict],
        image_base64: Optional[str],
        image_size: Optional[Tuple[int, int]] = None,
        summary: str = "",
        reflection: str = "",
        session_id: Optional[str] = None,
        user_message: str = "",
        reflection_output: str = "",
    ):
        self.task_description = task_description
        self.variables = variables
        self.input_variables = input_variables
        self.output_variables = output_variables
        self.title = title
        self.url = url
        self.xml_dom = xml_dom
        self.is_object_dom = is_object_dom
        self.is_desktop = self.url is None and not self.is_object_dom
        self.image_base64 = image_base64
        self.summary = summary
        self.reflection = reflection
        self.previous_steps = previous_steps
        self.raw_dom = raw_dom
        self.image_size = image_size
        self.session_id = session_id
        self.user_message = user_message
        self.reflection_output = reflection_output

    def to_dict(self):
        return {
            "task_description": self.task_description,
            "variables": self.variables,
            "input_variables": self.input_variables,
            "output_variables": self.output_variables,
            "title": self.title,
            "url": self.url,
            "xml_dom": ET.tostring(self.xml_dom, encoding="utf-8").decode("utf-8"),
            "is_object_dom": self.is_object_dom,
            "is_desktop": self.is_desktop,
            "image_base64": self.image_base64,
            "summary": self.summary,
            "reflection": self.reflection,
            "previous_steps": self.previous_steps,
            "raw_dom": self.raw_dom,
            "image_size": self.image_size,
            "session_id": self.session_id,
            "user_message": self.user_message,
            "reflection_output": self.reflection_output,
        }

    def to_dict_for_db(self):
        return {
            "task_description": self.task_description,
            "variables": self.variables,
            "input_variables": self.input_variables,
            "output_variables": self.output_variables,
            "title": self.title,
            "url": self.url,
            "is_desktop": self.is_desktop,
            "image_base64": self.image_base64,
            "summary": self.summary,
            "raw_dom": self.raw_dom,
            "image_size": self.image_size,
        }

    def from_dict(self, value: dict):
        self.task_description = value["task_description"]
        self.variables = value["variables"]
        self.input_variables = value["input_variables"]
        self.output_variables = value["output_variables"]
        self.title = value["title"]
        self.url = value["url"]
        self.xml_dom = ET.fromstring(value["xml_dom"])
        self.is_object_dom = value["is_object_dom"]
        self.is_desktop = value["is_desktop"]
        self.image_base64 = value["image_base64"]
        self.summary = value["summary"]
        self.previous_steps = value["previous_steps"]
        self.raw_dom = value["raw_dom"]
        self.image_size = value["image_size"]
        self.session_id = value["session_id"]
        self.user_message = value["user_message"]
        self.reflection_output = value["reflection_output"]


class StateProcessor:
    def __init__(self, state: UIAState):
        self.state = state

    def extract_visible_nodes(self, view_port_size: tuple[int, int] | None = None) -> list[dict]:
        if view_port_size is None and self.state.image_size is not None:
            view_port_height, view_port_width = self.state.image_size
        elif view_port_size is not None:
            view_port_height, view_port_width = view_port_size
        else:
            raise ValueError("view_port_size is required")

        visible_nodes = []

        def process_node(node):
            is_node_visible = True
            if "Area" in node:
                x0, y0, width, height = [int(item) for item in node["Area"].split(",")]
                x1 = x0 + width
                y1 = y0 + height
                display_x0 = np.clip(x0, 0, view_port_width)
                display_y0 = np.clip(y0, 0, view_port_height)
                display_x1 = np.clip(x1, 0, view_port_width)
                display_y1 = np.clip(y1, 0, view_port_height)
                display_width = display_x1 - display_x0
                display_height = display_y1 - display_y0

                is_node_visible = display_width > 0 and display_height > 0

            if "Children" in node:
                for i in range(len(node["Children"]) - 1, -1, -1):
                    child = node["Children"][i]
                    is_child_visible = process_node(child)
                    is_node_visible = is_node_visible or is_child_visible
            if is_node_visible:
                visible_nodes.append(node)
            return is_node_visible

        process_node(self.state.raw_dom)
        return visible_nodes

    def get_xml_dom_for_visible_nodes(
        self,
    ) -> ET.Element:
        visible_nodes = self.extract_visible_nodes()
        visible_nodes_ids = set([node.get("Id", -1) for node in visible_nodes])
        xml_dom = copy.deepcopy(self.state.xml_dom)

        queue = [xml_dom]
        while queue:
            node: ET.Element = queue.pop(0)
            node_children = list(node)
            for child in node_children:
                child_id = child.get("Id", None)
                if child_id is not None and int(child_id) not in visible_nodes_ids:
                    node.remove(child)
                else:
                    queue.append(child)
        self.annotate_image_visible_nodes()
        return xml_dom

    def annotate_image_visible_nodes(self):
        image_blob = pybase64.b64decode(str(self.state.image_base64), validate=True)
        image_np = np.frombuffer(image_blob, np.uint8)
        image_array = cv2.imdecode(image_np, 1)
        image_array = cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB)
        # view_port_size = (image_array.shape[0], image_array.shape[1])
        visible_nodes = self.extract_visible_nodes()
        bboxes = []
        for node in visible_nodes:
            if "Area" not in node:
                continue
            x0, y0, width, height = [int(item) for item in node["Area"].split(",")]
            x1 = x0 + width
            y1 = y0 + height
            bboxes.append([x0, y0, x1, y1])
        bboxes_array = np.array(bboxes)
        box_annotator = image_utils.BoxAnnotator()
        out_image = box_annotator.annotate(image_array, bboxes_array)
        return out_image
