import asyncio
import json
import pathlib

from aiocache import cached
from fastapi.encoders import jsonable_encoder
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.runnables import RunnableConfig

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import ExtensionTypes
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnExtensionPlan,
    BpmnExtensionPlanResult,
    BpmnExtensionToolResult,
    BpmnRequestContext,
    Connector,
    ElementExtension,
    ExtensionType,
    ModelType,
    Process,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import BusinessProcessAgentState
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_action_task import BpmnExtensionActionTask
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_agent_task import BpmnExtensionAgentTask
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_agentic_process_task import BpmnExtensionAgenticProcessTask
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_api_workflow_task import BpmnExtensionApiWorkflowTask
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_base_task import BpmnExtensionBaseTask
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_business_rule_task import BpmnExtensionBusinessRuleTask
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_connector_task import BpmnExtensionConnectorTask
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_queue_task import BpmnExtensionQueueTask
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_rpa_workflow_task import BpmnExtensionRpaWorkflowTask
from services.studio._text_to_workflow.bpmn_generation.fps_client import (
    get_connector_elements,
)
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils.sse_helper import MessageEmitter
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time

LOGGER = AppInsightsLogger()

TOOL_TASK_MAPPINGS: dict[ExtensionType, BpmnExtensionBaseTask] = {
    ExtensionType.RPA_WORKFLOW: BpmnExtensionRpaWorkflowTask(),
    ExtensionType.ACTION: BpmnExtensionActionTask(),
    ExtensionType.AGENT: BpmnExtensionAgentTask(),
    ExtensionType.CONNECTOR: BpmnExtensionConnectorTask(),
    ExtensionType.QUEUE: BpmnExtensionQueueTask(),
    ExtensionType.BUSINESS_RULE: BpmnExtensionBusinessRuleTask(),
    ExtensionType.AGENTIC_PROCESS: BpmnExtensionAgenticProcessTask(),
    ExtensionType.API_WORKFLOW: BpmnExtensionApiWorkflowTask(),
}


class BpmnExtensionFlowTask(BpmnBaseTask):
    def __init__(self):
        super().__init__("extension/plan_prompt.yaml")
        self.examples = BpmnBaseTask.read_content((pathlib.Path(__file__).parent.parent / "config/extension/plan_examples.md").absolute())
        self.history_dependencies = [Tool.EXTENSION, Tool.EDIT_BPMN, Tool.QA]

    @log_execution_time("bpmn_extension_node.run")
    async def run(self, state: BusinessProcessAgentState, config: RunnableConfig):
        message_emitter: MessageEmitter = config.get("configurable", {})["message_emitter"]
        user_request = await self.custom_stream(state, message_emitter)
        context = state["context"]
        tool_results = state.get("tool_results") or []
        if not context.solution_id or not context.project_key:
            await message_emitter.emit_message("Solution ID and project key are required for extension.", "message")
            tool_results.append(None)
            return {"tool_results": tool_results, "current_tool_index": state["current_tool_index"] + 1}

        connectors, external_agents = await self._gather_resources(context)
        bpmn_patch = self._extract_bpmn_patch(state)
        if not user_request:
            user_request = context.user_request

        model_name = self._get_model_name(context.override_model_type or ModelType.Anthropic)
        chat_history = await self._get_related_chat_history(context.chat_history, self.history_dependencies, model_name)
        plan: BpmnExtensionPlanResult = await self._generate_plan(context, connectors, model_name, chat_history, bpmn_patch)
        if plan.error:
            LOGGER.error(f"Error in generating plan: {plan.error}")
            tool_result = BpmnExtensionToolResult(tool=Tool.EXTENSION, explanation=plan.error, title=None, update=None)
            await message_emitter.emit_message(tool_result["explanation"], "message")
            tool_results.append(tool_result)
            return {"tool_results": tool_results, "current_tool_index": state["current_tool_index"] + 1}

        if not plan.relevant_elements:
            LOGGER.info("No elements found in the plan.")
            tool_result = BpmnExtensionToolResult(tool=Tool.EXTENSION, explanation="No elements found in the plan.", title=None, update=None)
            await message_emitter.emit_message(tool_result["explanation"], "message")
            tool_results.append(tool_result)
            return {"tool_results": tool_results, "current_tool_index": state["current_tool_index"] + 1}

        groups: dict[ExtensionType, list[BpmnExtensionPlan]] = {}
        for element in plan.relevant_elements:
            if element.extension_type not in groups:
                groups[element.extension_type] = []
            groups[element.extension_type].append(element)

        update: list[ElementExtension] = []
        for extension_type in groups:
            plans: list[BpmnExtensionPlan] = groups[extension_type]
            result = await TOOL_TASK_MAPPINGS[extension_type].generate(
                context=context,
                plans=plans,
                connectors=connectors,
                external_agents=external_agents,
                model_name=model_name,
                chat_history=chat_history,
            )
            if len(result) > 0:
                update.extend(result)
                await message_emitter.emit_message(jsonable_encoder({"tool": Tool.EXTENSION, "update": result}), "result")

        tool_results.append(
            BpmnExtensionToolResult(
                tool=Tool.EXTENSION,
                explanation=plan.explanation,
                title=plan.title,
                update=update,
            )
        )
        return {
            "tool_results": tool_results,
            "current_tool_index": state["current_tool_index"] + 1,
        }

    async def generate(self, context: BpmnRequestContext) -> ToolResult:
        if not context.solution_id or not context.project_key:
            LOGGER.error("Solution ID and project key are required for extension.")
            return BpmnExtensionToolResult(tool=Tool.EXTENSION, explanation="Solution id and project key are required.", title=None, update=None)

        connectors, external_agents = await self._gather_resources(context)
        model_name = self._get_model_name(context.override_model_type or ModelType.Anthropic)
        chat_history = await self._get_related_chat_history(context.chat_history, self.history_dependencies, model_name)
        plan: BpmnExtensionPlanResult = await self._generate_plan(context, connectors, model_name, chat_history)
        if plan.error:
            LOGGER.error(f"Error in generating plan: {plan.error}")
            return BpmnExtensionToolResult(tool=Tool.EXTENSION, explanation=plan.error, title=None, update=[])

        if not plan.relevant_elements:
            LOGGER.info("No elements found in the plan.")
            return BpmnExtensionToolResult(tool=Tool.EXTENSION, explanation="No elements found in the plan.", title=None, update=[])

        groups: dict[ExtensionType, list[BpmnExtensionPlan]] = {}
        for element in plan.relevant_elements:
            if element.extension_type not in groups:
                groups[element.extension_type] = []
            groups[element.extension_type].append(element)

        api_calls = []
        for extension_type in groups:
            plans: list[BpmnExtensionPlan] = groups[extension_type]
            api_calls.append(
                TOOL_TASK_MAPPINGS[extension_type].generate(
                    context=context,
                    plans=plans,
                    connectors=connectors,
                    external_agents=external_agents,
                    model_name=model_name,
                    chat_history=chat_history,
                )
            )

        results: list[list[ElementExtension]] = await asyncio.gather(*api_calls)
        update: list[ElementExtension] = []
        for result in results:
            if len(result) > 0:
                update.extend(result)

        return BpmnExtensionToolResult(
            tool=Tool.EXTENSION,
            explanation=plan.explanation,
            title=plan.title,
            update=update,
        )

    @log_execution_time("bpmn_extension_flow_task.generate_plan")
    async def _generate_plan(
        self,
        context: BpmnRequestContext,
        connectors: list[Connector],
        model_name: str,
        chat_history: str,
        bpmn_patch: dict | None = None,
    ) -> BpmnExtensionPlanResult:
        connector_elements = []
        for connector in connectors:
            connector_elements.append({"key": connector.key, "name": connector.name, "description": connector.description})

        prompts = self.config["prompt"]
        parser = PydanticOutputParser(pydantic_object=BpmnExtensionPlanResult)
        system_message = self._system_template(prompts["system"], partial_variables={"schema": parser.get_format_instructions()}).format(
            examples=self.examples,
            connectors=json.dumps(connector_elements),
        )
        bpmn_patch_str = json.dumps(bpmn_patch or {})
        user_message = self._human_template(prompts["user"]).format(
            bpmn_xml=context.current_bpmn,
            user_query=context.user_request,
            chat_history=chat_history,
            bpmn_patch=bpmn_patch_str,
        )
        result, _ = await self._call_llm(system_message, user_message, use_case=model_name)
        # LOGGER.info(f"Result of extension plan is : [{result}]")
        output: BpmnExtensionPlanResult = BpmnExtensionPlanResult.model_validate_json(result.strip("```json\n").strip("\n```"))
        return output

    def _extract_bpmn_patch(self, state: BusinessProcessAgentState) -> dict | None:
        tool_results = state.get("tool_results") or []
        if len(tool_results) > 0 and tool_results[-1] and tool_results[-1]["tool"] == Tool.EDIT_BPMN:
            return {
                "add": tool_results[-1].get("add", []),
                "delete": tool_results[-1].get("delete", []),
                "update": tool_results[-1].get("update", []),
            }
        return None

    def connector_elements_cache_key(*args, **kwargs):
        request_context = args[2]
        tenant_id = request_context.tenant_id if isinstance(request_context, RequestContext) else ""
        return f"{tenant_id}_connector_elements"

    @cached(ttl=43200, key_builder=connector_elements_cache_key)
    async def get_connectors(self, request_context: RequestContext):
        return await get_connector_elements(request_context)

    @log_execution_time("bpmn_extension_flow_task.gather_resources")
    async def _gather_resources(self, context: BpmnRequestContext) -> tuple[list[Connector], list[Process]]:
        extension_data = context.extension_data_override
        if extension_data:
            connections = extension_data.connections or []
            external_agents: list[Process] = []
            connectors: list[Connector] = []
            for connector in connections:
                if connector.isExternalAgent:
                    external_agents.append(
                        Process(
                            id=connector.key,
                            name=connector.name,
                            type=ExtensionTypes.EXTERNAL_AGENT,
                            description=connector.description,
                            connection=None,
                            activity=None,
                            fakeId=None,
                            score=0,
                        )
                    )
                else:
                    connectors.append(connector)
            return connectors, external_agents

        request_context = context.request_context
        connectors, agents = await self.get_connectors(request_context)
        cached_connectors = [
            Connector(
                name=connector.name,
                key=connector.key,
                description=connector.description,
                connection=None,
                activity=None,
                trigger=None,
                fakeId=None,
                score=0,
                type="",
                isExternalAgent=False,
                inputJson=None,
            )
            for connector in connectors
        ]
        cached_agents = [
            Process(
                id=agent.key,
                name=agent.name,
                type=ExtensionTypes.EXTERNAL_AGENT,
                description=agent.description,
                connection=None,
                activity=None,
                fakeId=None,
                score=0,
            )
            for agent in agents
        ]
        return cached_connectors, cached_agents
