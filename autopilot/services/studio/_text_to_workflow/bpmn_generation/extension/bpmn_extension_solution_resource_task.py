from abc import abstractmethod

from pydantic import <PERSON><PERSON>dapter
from pydantic_core import ValidationError
from typing_extensions import override

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import ImplementationTypes, ResourceKinds, ResourceTypes
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    ActionApp,
    BpmnElement,
    BpmnExtensionPlan,
    BpmnRequestContext,
    Connector,
    ElementExtension,
    ElementImplementationResult,
    ExtensionData,
    ExtensionDataOverride,
    ExtensionInfo,
    Process,
    SolutionResource,
)
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_base_task import BpmnExtensionBaseTask
from services.studio._text_to_workflow.bpmn_generation.fps_client import get_solution_resources
from services.studio._text_to_workflow.utils.errors import ServiceError
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time

LOGGER = AppInsightsLogger()


class BpmnExtensionSolutionResourceTask(BpmnExtensionBaseTask):
    def __init__(self, implementation_type: ImplementationTypes, solution_kind: ResourceKinds | None, solution_types: list[ResourceTypes] | None = None):
        super().__init__()
        self.implementation_type = implementation_type
        self.solution_kind = solution_kind
        self.solution_types = solution_types

    @abstractmethod
    def _get_task_type(self, plan: BpmnExtensionPlan) -> str:
        pass

    @abstractmethod
    def _get_extension_type(self, plan: BpmnExtensionPlan) -> str:
        pass

    @abstractmethod
    def _get_eval_dataset(self, extension_data_override: ExtensionDataOverride) -> list[SolutionResource]:
        pass

    @override
    @log_execution_time("bpmn_extension_solution_task.generate")
    async def generate(
        self,
        context: BpmnRequestContext,
        plans: list[BpmnExtensionPlan],
        connectors: list[Connector],
        external_agents: list[Process],
        model_name: str,
        chat_history: str,
    ) -> list[ElementExtension]:
        candidates, solution_resources = await self._gather_resources(context)
        elements: list[BpmnElement] = [element.element for element in plans]

        prompts = self.config["prompt"]
        system_message = self._system_template(prompts["system"])
        user_message = self._human_template(prompts["user"]).format(
            elements=TypeAdapter(list[BpmnElement]).dump_json(elements).decode("utf-8"),
            implementation_type=self.implementation_type.value,
            implementations=TypeAdapter(list[ExtensionInfo]).dump_json(candidates, exclude_none=True).decode("utf-8"),
            user_query=context.user_request or "",
            chat_history=chat_history,
        )

        result, _ = await self._call_llm(system_message, user_message, use_case=model_name)
        # LOGGER.info(f"Result of extension suggestion is : [{result}]")
        try:
            results: dict[str, ElementImplementationResult] = TypeAdapter(dict[str, ElementImplementationResult]).validate_json(
                result.strip("```json\n").strip("\n```")
            )
            return await self._process_extensions(results, plans, solution_resources)
        except ValidationError as e:
            LOGGER.error(f"Validation error {e} in processing LLM response: [{result}]")
            raise ServiceError(500, message="I couldn’t process your request, please try it later.")

    @log_execution_time("bpmn_extension_solution_task.gather_resources")
    async def _gather_resources(self, context: BpmnRequestContext) -> tuple[list[ExtensionInfo], dict[int, SolutionResource]]:
        request_context = context.request_context
        extension_data = context.extension_data_override
        if extension_data:
            solution_resources = self._get_eval_dataset(extension_data)
        else:
            solution_resources = await get_solution_resources(
                request_context,
                context.solution_id,
                context.project_key,
                self.solution_kind,
                self.solution_types,
            )

        # Build simpler data structure for processes and agents and fake integer id for reducing token size
        fake_id = 1
        solution_resources_by_fake_id: dict[int, SolutionResource] = {}
        candidates: list[ExtensionInfo] = []
        for solution_resource in solution_resources:
            solution_resource.fakeId = fake_id
            candidates.append(ExtensionInfo(id=fake_id, name=solution_resource.name, description=solution_resource.description, score=None))
            solution_resources_by_fake_id[solution_resource.fakeId] = solution_resource
            fake_id += 1

        return candidates, solution_resources_by_fake_id

    async def _process_extensions(
        self, results: dict[str, ElementImplementationResult], plans: list[BpmnExtensionPlan], solution_resources: dict[int, SolutionResource]
    ) -> list[ElementExtension]:
        extension_results: list[ElementExtension] = []
        for plan in plans:
            element = plan.element
            if element.id not in results:
                LOGGER.error(f"LLM generated hallucinated element id for element [{element.id}]")
                continue

            element_extension = results[element.id]
            if element_extension.error:
                LOGGER.error(f"Error in generating extension for element [{element.id}]: {element_extension.error}")
                continue

            extensions: list[Process | ActionApp | Connector | SolutionResource] = []
            for suggestion in element_extension.suggestions or []:
                if suggestion.id not in solution_resources:
                    LOGGER.error(f"LLM generated hallucinated suggestion id for element [{element.id}]")
                    continue

                solution_resource = solution_resources[suggestion.id]
                solution_resource.score = suggestion.score or 0
                solution_resource.type = self._get_extension_type(plan)
                extensions.append(solution_resource)

            extension_results.append(ElementExtension(id=element.id, type=self._get_task_type(plan), data=ExtensionData(suggestions=extensions)))

        return extension_results
