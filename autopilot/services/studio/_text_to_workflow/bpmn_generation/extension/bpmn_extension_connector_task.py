from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import BpmnElementTypes, ExtensionTypes, ResourceKinds
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    ActionApp,
    Activity,
    BpmnExtensionPlan,
    BpmnRequestContext,
    Connector,
    ConnectorDirection,
    ElementExtension,
    ExtensionData,
    Process,
    SolutionResource,
    Trigger,
)
from services.studio._text_to_workflow.bpmn_generation.connector_service import ActivityWithEmbedding, ConnectorService
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_base_task import BpmnExtensionBaseTask
from services.studio._text_to_workflow.bpmn_generation.fps_client import get_solution_resources
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time

LOGGER = AppInsightsLogger()
CONNECTOR_SERVICE: ConnectorService = ConnectorService()


class BpmnExtensionConnectorTask(BpmnExtensionBaseTask):
    def __init__(self):
        super().__init__()
        self.embedding_model = ModelManager().get_embeddings_model("bpmn_embedding_model")

    @log_execution_time("bpmn_extension_connector_task.generate")
    async def generate(
        self,
        context: BpmnRequestContext,
        plans: list[BpmnExtensionPlan],
        connectors: list[Connector],
        external_agents: list[Process],
        model_name: str,
        chat_history: str,
    ) -> list[ElementExtension]:
        connectors_by_key = {connector.key: connector for connector in connectors}
        return await self._process_extensions(context, plans, connectors_by_key)

    async def _process_extensions(
        self,
        context: BpmnRequestContext,
        plans: list[BpmnExtensionPlan],
        connectors: dict[str, Connector],
    ) -> list[ElementExtension]:
        extension_results: list[ElementExtension] = []
        for plan in plans:
            element = plan.element
            if element.type == BpmnElementTypes.START:
                plan.direction = ConnectorDirection.INCOMING
            elif element.type == BpmnElementTypes.END:
                plan.direction = ConnectorDirection.OUTGOING

            query_embedding = self.embedding_model.encode(f"{element.purpose}", instruction_set="icl", instruction_type="query")
            extensions: list[Process | ActionApp | Connector | SolutionResource] = []
            for suggestion in plan.connectors or []:
                if suggestion.connector not in connectors:
                    LOGGER.error(f"LLM generated hallucinated suggestion id for element [{element.id}]")
                    continue

                connector = connectors[suggestion.connector]
                connector.score = suggestion.score or 0
                solutions = await get_solution_resources(
                    context.request_context,
                    context.solution_id,
                    context.project_key,
                    ResourceKinds.CONNECTION,
                    [connector.key],
                )
                if solutions and len(solutions) > 0:
                    connector.connection = await CONNECTOR_SERVICE._get_connection(connector.key, context, solutions[0].name)
                if plan.direction == ConnectorDirection.OUTGOING:
                    connector.type = ExtensionTypes.CONNECTOR_ACTIVITY
                    if connector.connection:
                        activity: ActivityWithEmbedding = await CONNECTOR_SERVICE._get_activity_trigger(connector.key, context, "activity", query_embedding)
                        connector.activity = Activity(name=activity["name"], displayName=activity["displayName"], description=activity["description"])
                else:
                    connector.type = ExtensionTypes.CONNECTOR_EVENT
                    if connector.connection:
                        trigger: ActivityWithEmbedding = await CONNECTOR_SERVICE._get_activity_trigger(connector.key, context, "trigger", query_embedding)
                        connector.trigger = Trigger(
                            name=trigger["name"], displayName=trigger["displayName"], description=trigger["description"], objectName=trigger["objectName"]
                        )
                extensions.append(connector)

            extension_results.append(
                ElementExtension(
                    id=element.id,
                    type=self._get_extension_type(plan),
                    data=ExtensionData(suggestions=extensions),
                )
            )

        return extension_results

    def _get_extension_type(self, plan: BpmnExtensionPlan) -> str:
        if plan.element.type in [BpmnElementTypes.START, BpmnElementTypes.END, BpmnElementTypes.INTERMEDIATE_CATCH]:
            return plan.element.type
        elif plan.direction == ConnectorDirection.OUTGOING:
            return BpmnElementTypes.SEND
        else:
            return BpmnElementTypes.RECEIVE
