import base64
import binascii
import io
import os
import pathlib
import re
import tempfile
import xml.etree.ElementTree as ET
import zipfile
from typing import List, Optional, Tuple, Union

import langchain.schema
import langchain.schema.messages
import pdfplumber
from docx import Document
from markitdown import MarkItDown

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import (
    MAX_FILE_SIZE_MB,
    MIME_TO_EXTENSION,
    MIME_TO_READABLE_NAME,
    SUPPORTED_DOCUMENT_TYPES,
    SUPPORTED_IMAGE_TYPES,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnGenerationToolResult,
    BpmnRequestContext,
    ModelType,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_validator import BpmnValidator
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    log_execution_time,
)


class DocumentProcessingError(Exception):
    pass


class BpmnGenerationFromDocumentTask(BpmnBaseTask):
    """Task for generating BPMN diagrams from documents and images.

    This class handles the conversion of various document types (PDFs, images, Word docs)
    to BPMN using large language models with comprehensive vision and text capabilities.
    Supports both structured document processing with text extraction and direct image processing.

    Attributes:
        SUPPORTED_IMAGE_TYPES: List of supported image MIME types
        SUPPORTED_DOCUMENT_TYPES: List of supported document MIME types
    """

    VISIO_NAMESPACES = {
        "v2010": "http://schemas.microsoft.com/visio/2010/main",
        "v2012": "http://schemas.microsoft.com/office/visio/2012/main",
        "v2003": "http://schemas.microsoft.com/visio/2003/core",
    }

    def __init__(self) -> None:
        """Initialize the BPMN generation task with document and image support."""
        super().__init__("generation_prompt.yaml")

        self._validator = BpmnValidator()
        self._logger = AppInsightsLogger()
        self.bpmn_image_example: str = ""
        self.supported_element_examples: str = ""
        self.convert_document_bpmn_examples: str = ""

        self._load_configuration_files()
        self._logger.info("Initialized BpmnGenerationFromDocumentTask with document and image support")

    def _load_configuration_files(self) -> None:
        """Load configuration files for BPMN generation."""
        config_path = pathlib.Path(__file__).parent / "config"

        config_files = {
            "bpmn_image_example": "bpmn_image_example.json",
            "supported_element_examples": "supported_element_examples.md",
            "convert_document_bpmn_examples": "convert_document_bpmn_examples.md",
        }

        for attr_name, filename in config_files.items():
            file_path = (config_path / filename).absolute()
            setattr(self, attr_name, BpmnBaseTask.read_content(file_path))

    def _get_file_extension(self, mime_type: str) -> Optional[str]:
        """Get file extension from MIME type."""
        return MIME_TO_EXTENSION.get(mime_type)

    def _get_readable_type_name(self, mime_type: str) -> str:
        """Get human-readable type name from MIME type."""
        return MIME_TO_READABLE_NAME.get(mime_type, mime_type)

    def _validate_file_size(self, file_data: str) -> None:
        """Validate that file size is within acceptable limits."""
        try:
            # Extract base64 content if it's in data URI format
            base64_content = self._extract_base64_content(file_data)

            # Decode base64 to get actual file size
            file_bytes = base64.b64decode(base64_content)
            file_size_mb = len(file_bytes) / (1024 * 1024)

            if file_size_mb > MAX_FILE_SIZE_MB:
                raise DocumentProcessingError(
                    f"File size ({file_size_mb:.1f}MB) exceeds the maximum allowed size of {MAX_FILE_SIZE_MB}MB. Please provide a smaller file."
                )

            self._logger.info(f"File size validation passed: {file_size_mb:.2f}MB")

        except binascii.Error:
            raise DocumentProcessingError("Invalid base64 file data provided")
        except Exception as e:
            if isinstance(e, DocumentProcessingError):
                raise
            raise DocumentProcessingError(f"File size validation failed: {str(e)}")

    @log_execution_time("bpmn_generation_from_document_task.detect_file_type")
    def _detect_file_type(self, file_data: str) -> str:
        """Detect file type from base64 encoded data."""
        if not file_data:
            raise DocumentProcessingError("No file data provided")

        # Validate file size first
        self._validate_file_size(file_data)

        try:
            # Handle data URI format
            if file_data.startswith("data:"):
                return self._extract_mime_from_data_uri(file_data)

            # Detect from magic bytes for plain base64
            return self._detect_from_base64_content(file_data)

        except Exception as e:
            self._logger.error(f"File type detection failed: {e}")
            raise DocumentProcessingError(f"File type detection failed: {str(e)}")

    def _extract_mime_from_data_uri(self, data_uri: str) -> str:
        """Extract MIME type from data URI format."""
        pattern = r"^data:([^;]+);base64,"
        match = re.match(pattern, data_uri)

        if not match:
            raise DocumentProcessingError("Invalid data URI format")

        mime_type = match.group(1).lower()
        supported_types = SUPPORTED_IMAGE_TYPES + SUPPORTED_DOCUMENT_TYPES

        if mime_type not in supported_types:
            raise DocumentProcessingError(f"Unsupported file type from data URI: {mime_type}")

        return mime_type

    def _detect_from_base64_content(self, file_data: str) -> str:
        """Detect file type from base64 content using magic bytes."""
        try:
            file_bytes = base64.b64decode(file_data)
            detected_type = self._detect_from_magic_bytes(file_bytes)

            supported_types = SUPPORTED_IMAGE_TYPES + SUPPORTED_DOCUMENT_TYPES
            if detected_type not in supported_types:
                raise DocumentProcessingError(f"Unsupported file type detected: {detected_type}")

            return detected_type

        except Exception as decode_error:
            raise DocumentProcessingError(f"Cannot decode base64 for file type detection: {str(decode_error)}")

    def _detect_from_magic_bytes(self, file_bytes: bytes) -> str:
        """Detect file type from magic bytes."""
        # Magic byte patterns for different file types
        magic_patterns = [
            (lambda b: b.startswith(b"%PDF") or b.startswith(b"JVBERi"), "application/pdf"),
            (lambda b: b.startswith(b"PK\x03\x04") and b"word/" in b[:1024], "application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
            (lambda b: b.startswith(b"PK\x03\x04") and b"visio/" in b[:1024], "application/vnd.ms-visio.drawing"),
            (lambda b: b.startswith(b"<?xml") and b"VisioDocument" in b[:1024], "application/vnd.visio"),
            (lambda b: b.startswith(b"\xd0\xcf\x11\xe0\xa1\xb1\x1a\xe1"), "application/msword"),
            (lambda b: b.startswith(b"\x89PNG"), "image/png"),
            (lambda b: b.startswith(b"\xff\xd8\xff"), "image/jpeg"),
            (lambda b: b.startswith(b"RIFF") and b"WEBP" in b[:12], "image/webp"),
        ]

        for pattern_func, mime_type in magic_patterns:
            if pattern_func(file_bytes):
                return mime_type

        self._logger.warning("Unknown file type detected")
        return "application/octet-stream"

    def _extract_base64_content(self, file_data: str) -> str:
        """Extract base64 content from file data, handling data URI format."""
        if file_data.startswith("data:"):
            return file_data.split(",", 1)[1] if "," in file_data else file_data
        return file_data

    @log_execution_time("bpmn_generation_from_document_task.extract_structured_document_content")
    def _extract_structured_document_content(self, document_data: str, document_type: str) -> Tuple[str, List[str]]:
        """Extract text from structured document."""
        readable_type = self._get_readable_type_name(document_type)
        try:
            self._logger.info(f"Extracting text from {readable_type} document")

            base64_content = self._extract_base64_content(document_data)
            return self._extract_document_content(base64_content, document_type)

        except Exception as e:
            self._logger.error(f"{readable_type} text extraction failed: {e}")
            raise DocumentProcessingError(f"{readable_type} text extraction failed: {str(e)}")

    def _extract_document_content(self, base64_content: str, document_type: str) -> Tuple[str, List[str]]:
        """Extract text from document using appropriate parser."""
        try:
            file_extension = self._get_file_extension(document_type)
            if file_extension is None:
                raise DocumentProcessingError(f"Unsupported document type: {document_type}. Supported types: {SUPPORTED_DOCUMENT_TYPES}")

            document_bytes = base64.b64decode(base64_content)
            readable_type = self._get_readable_type_name(document_type)

            if document_type == "application/pdf":
                extracted_text = self._extract_pdf_content(document_bytes)
            else:
                extracted_text = self._extract_office_document_content(document_bytes, file_extension)

            extracted_text = self._format_extracted_text(extracted_text, readable_type)
            self._validate_extracted_content(extracted_text, readable_type)

            self._logger.info(f"Successfully extracted {len(extracted_text)} characters from {readable_type}")
            return extracted_text, []

        except Exception as e:
            self._logger.error(f"Document content extraction failed: {e}")
            raise DocumentProcessingError(f"Document content extraction failed: {str(e)}")

    @log_execution_time("bpmn_generation_from_document_task.extract_office_document_content")
    def _extract_office_document_content(self, document_bytes: bytes, file_extension: str) -> str:
        """Extract content from Office documents."""
        if file_extension == ".docx":
            # Use python-docx for DOCX files
            try:
                document_stream = io.BytesIO(document_bytes)
                doc = Document(document_stream)

                # Extract text from paragraphs
                paragraphs = []
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        paragraphs.append(paragraph.text)

                # Extract text from tables
                for table in doc.tables:
                    for row in table.rows:
                        row_text = []
                        for cell in row.cells:
                            if cell.text.strip():
                                row_text.append(cell.text.strip())
                        if row_text:
                            paragraphs.append(" | ".join(row_text))

                return "\n".join(paragraphs)

            except Exception as e:
                return f"[Error extracting text from DOCX file: {str(e)}]"

        elif file_extension == ".doc":
            # Use MarkItDown for DOC files
            temp_file = None
            try:
                temp_file = tempfile.NamedTemporaryFile(suffix=file_extension, delete=False)
                temp_file.write(document_bytes)
                temp_file.flush()
                temp_file.close()

                markitdown = MarkItDown()
                result = markitdown.convert(temp_file.name)

                return result.text_content or "[No text content extracted from DOC file]"

            except Exception as e:
                return f"[Error extracting text from DOC file: {str(e)}]"
            finally:
                if temp_file and os.path.exists(temp_file.name):
                    os.unlink(temp_file.name)

        elif file_extension in [".vdx", ".vsdx"]:
            # Use unified Visio extraction for both VDX and VSDX formats
            try:
                extracted_content = self._extract_visio_content(document_bytes, file_extension)

                # Add contextual header for Visio files
                if extracted_content and not extracted_content.startswith("["):
                    format_name = "VSDX" if file_extension == ".vsdx" else "VDX"
                    extracted_content = f"# VISIO DIAGRAM CONTENT ({format_name})\n\nThis content was extracted from a {format_name} Visio diagram file. The extracted information includes shape names, text annotations, connectors, and other diagram elements that describe the workflow or process.\n\n{extracted_content}"

                return extracted_content

            except Exception as e:
                return f"[Error extracting content from {file_extension.upper()} file: {str(e)}]"
        else:
            return f"[Unsupported format: {file_extension}]"

    def _format_extracted_text(self, extracted_text: str, readable_type: str) -> str:
        """Format extracted text with header."""
        if not extracted_text or len(extracted_text.strip()) < 10:
            self._logger.warning(f"Minimal text content extracted from {readable_type}")
            return f"# DOCUMENT CONTENT EXTRACTED FROM {readable_type}\n\n[Minimal text content extracted - document may contain primarily images, tables, or formatting elements. Please provide a document with clear business process text content for optimal BPMN generation.]"

        return f"# DOCUMENT CONTENT EXTRACTED FROM {readable_type}\n\n{extracted_text}"

    def _validate_extracted_content(self, extracted_text: str, readable_type: str) -> None:
        """Validate that sufficient content was extracted."""
        # Be more lenient with validation - allow minimal content to pass through to LLM for better error handling
        if len(extracted_text.strip()) < 30:
            self._logger.warning(f"Very minimal content extracted from {readable_type}: {len(extracted_text)} characters")

        # Only raise error for completely empty content
        if not extracted_text or len(extracted_text.strip()) == 0:
            raise DocumentProcessingError(f"No text content could be extracted from {readable_type}")

    @log_execution_time("bpmn_generation_from_document_task.extract_pdf_content")
    def _extract_pdf_content(self, document_bytes: bytes) -> str:
        """Extract text from PDF with layout preservation and table detection."""
        try:
            pdf_stream = io.BytesIO(document_bytes)
            extracted_text = ""

            with pdfplumber.open(pdf_stream) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # Extract text with layout preservation
                    page_text = page.extract_text(layout=True, x_tolerance=2, y_tolerance=2)
                    if page_text:
                        extracted_text += f"\n--- Page {page_num + 1} ---\n{page_text}\n"

                    # Extract tables if any
                    tables = page.extract_tables()
                    if tables:
                        extracted_text += self._format_pdf_tables(tables, page_num + 1)

            return extracted_text.strip()

        except Exception as e:
            self._logger.error(f"PDF extraction failed: {e}")
            raise DocumentProcessingError(f"PDF extraction failed: {str(e)}")

    def _format_pdf_tables(self, tables: List, page_num: int) -> str:
        """Format PDF tables for text output."""
        formatted_tables = ""

        for table_num, table in enumerate(tables):
            formatted_tables += f"\n--- Table {table_num + 1} on Page {page_num} ---\n"
            for row in table:
                if row:  # Skip empty rows
                    row_text = " | ".join([cell or "" for cell in row])
                    formatted_tables += f"{row_text}\n"
            formatted_tables += "\n"

        return formatted_tables

    @log_execution_time("bpmn_generation_from_document_task.generate")
    async def generate(self, context: BpmnRequestContext) -> ToolResult:
        """Generate BPMN diagram from documents and images."""
        try:
            self._logger.info("Starting BPMN generation from document")

            if not context.document_data:
                self._logger.error("No document data provided")
                return self._create_error_result("No Document Provided", "Please provide a document or image file.")

            file_data = self._prepare_file_data(context.document_data)
            return await self._process_document(file_data, context)

        except Exception as e:
            self._logger.error(f"Error in document BPMN generation: {e}")
            return self._create_error_result("Generation Failed", f"Failed to generate BPMN from document: {str(e)}")

    def _prepare_file_data(self, document_data: Union[bytes, str]) -> str:
        """Prepare file data for processing."""
        if isinstance(document_data, bytes):
            return document_data.decode("utf-8")
        return document_data

    def _create_error_result(self, title: str, explanation: str) -> BpmnGenerationToolResult:
        """Create a standardized error result."""
        return BpmnGenerationToolResult(
            tool=Tool.CONVERT_DOCUMENT,
            title=title,
            explanation=explanation,
            add=[],
            update=[],
            delete=[],
        )

    @log_execution_time("bpmn_generation_from_document_task.process_document")
    async def _process_document(self, document_data: str, context: BpmnRequestContext) -> BpmnGenerationToolResult:
        """Process document_data path for both images and documents."""
        try:
            detected_type = self._detect_file_type(document_data)
            self._logger.info(f"Detected file type: {detected_type}")

            if detected_type in SUPPORTED_IMAGE_TYPES:
                return await self._process_document_image(document_data, context, detected_type)
            elif detected_type in SUPPORTED_DOCUMENT_TYPES:
                return await self._process_structured_document(document_data, context, detected_type)
            else:
                return self._create_error_result(
                    "Unsupported File Type", f"File type {detected_type} is not supported. Please provide an image or supported document."
                )

        except Exception as e:
            self._logger.error(f"Document processing failed: {e}")
            return self._create_error_result("Document Processing Failed", f"Failed to process document: {str(e)}")

    @log_execution_time("bpmn_generation_from_document_task.process_document_image")
    async def _process_document_image(self, image_data: str, context: BpmnRequestContext, mime_type: str) -> BpmnGenerationToolResult:
        """Process image file through document_data path."""
        readable_type = self._get_readable_type_name(mime_type)
        try:
            self._logger.info(f"Processing {readable_type} image")

            model_type = context.override_model_type if context.override_model_type == ModelType.Google else ModelType.Anthropic
            model_name = self._get_model_name(model_type)

            result, usage = await self._generate_from_image(context.user_request, image_data, model_name)
            result_json = result.strip("```json\n").strip("\n```")

            self._logger.info(f"Generated BPMN from {readable_type} image")
            return await self._create_bpmn_result(context, result_json, usage, model_name)

        except Exception as e:
            self._logger.error(f"Image processing failed: {e}")
            return self._create_error_result("Image Processing Failed", f"Failed to process image: {str(e)}")

    @log_execution_time("bpmn_generation_from_document_task.process_structured_document")
    async def _process_structured_document(self, document_data: str, context: BpmnRequestContext, document_type: str) -> BpmnGenerationToolResult:
        """Process structured document with text extraction."""
        readable_type = self._get_readable_type_name(document_type)
        try:
            self._logger.info(f"Processing {readable_type} document")

            extracted_text, _ = self._extract_structured_document_content(document_data, document_type)
            self._logger.info(f"Extracted {len(extracted_text)} characters from {readable_type}")

            model_type = context.override_model_type if context.override_model_type == ModelType.Google else ModelType.Anthropic
            model_name = self._get_model_name(model_type)

            result, usage = await self._generate_from_document_text(context.user_request, extracted_text, model_name)
            result_json = result.strip("```json\n").strip("\n```")

            self._logger.info(f"Generated BPMN from {readable_type} document")
            return await self._create_bpmn_result(context, result_json, usage, model_name)

        except Exception as e:
            self._logger.error(f"{readable_type} processing failed: {e}")
            return self._create_error_result(f"{readable_type} Processing Failed", f"Failed to process {document_type}: {str(e)}")

    async def _create_bpmn_result(self, context: BpmnRequestContext, result_json: str, usage: TokenUsage, model_name: str) -> BpmnGenerationToolResult:
        """Create BPMN result from validated output."""
        current_bpmn = self.common_config["default_bpmn_xml"] if context.current_bpmn == "" else context.current_bpmn
        output = await self._validate_output(context.support_validation, current_bpmn, result_json, usage, model_name)

        # Handle process deletion for replacement
        deleted_elements = output.get("delete") or []
        process_id_to_delete = self.extract_process_id(current_bpmn)
        if process_id_to_delete:
            deleted_elements.append({"id": process_id_to_delete, "type": "bpmn:process"})

        return BpmnGenerationToolResult(
            tool=Tool.CONVERT_DOCUMENT,
            title=output.get("title"),
            explanation=output["explanation"],
            add=output.get("add") or [],
            update=output.get("update") or [],
            delete=deleted_elements,
        )

    @log_execution_time("bpmn_generation_from_document_task.generate_from_document_text")
    async def _generate_from_document_text(self, user_request: str, extracted_text: str, model_name: str) -> Tuple[str, TokenUsage]:
        """Generate BPMN from extracted document text using convert_document system prompt."""
        try:
            system_message = self._system_template(self.config["prompt"]["system_template"]["convert_document"]).format(
                bpmn_patch_schema=self.bpmn_patch_schema,
                supported_element_examples=self.supported_element_examples,
                convert_document_bpmn_examples=self.convert_document_bpmn_examples,
            )

            user_message = self._human_template(self.config["prompt"]["user_template"]["document_extracted"]).format(
                user_request=user_request, extracted_text=extracted_text
            )

            return await self._call_llm(system_message, user_message, model_name)

        except Exception as e:
            self._logger.error(f"Document text generation failed: {e}")
            raise DocumentProcessingError(f"Document text generation failed: {str(e)}")

    @log_execution_time("bpmn_generation_from_document_task.generate_from_image")
    async def _generate_from_image(self, query: str, image_data: str, model_name: str) -> Tuple[str, TokenUsage]:
        """Generate BPMN from image using vision model."""
        try:
            system_message = self._system_template(self.config["prompt"]["system_template"]["convert_document"]).format(
                bpmn_patch_schema=self.bpmn_patch_schema,
                supported_element_examples=self.supported_element_examples,
                convert_document_bpmn_examples=self.convert_document_bpmn_examples,
            )

            # Determine image type for model
            mime_type = self._get_image_mime_type(image_data)

            user_message = langchain.schema.messages.HumanMessage(
                content=[
                    {"type": "text", "text": query},
                    {"type": mime_type, "image_url": {"url": image_data}},
                ]
            )

            return await self._call_llm(system_message, user_message, model_name)

        except Exception as e:
            self._logger.error(f"Image generation failed: {e}")
            raise DocumentProcessingError(f"Image generation failed: {str(e)}")

    def _get_image_mime_type(self, image_data: str) -> str:
        """Get MIME type for image."""
        try:
            mime_type = self._detect_file_type(image_data)
            if mime_type in SUPPORTED_IMAGE_TYPES:
                return mime_type
            else:
                self._logger.error(f"Detected unsupported image type: {mime_type}")
                raise DocumentProcessingError(f"Unsupported image type: {mime_type}")
        except Exception as e:
            self._logger.error(f"Failed to detect image MIME type: {e}")
            raise DocumentProcessingError(f"Failed to detect image MIME type: {str(e)}")

    def _parse_shape(self, shape, ns) -> Tuple[str, List[str], List[str]]:
        """Parse a Visio shape element to extract name, texts, and relevant cell data."""
        sid = shape.get("ID", "Unknown")
        sname = shape.get("Name", f"Shape_{sid}")
        texts = [self._extract_all_text(t) for t in shape.findall(".//visio:Text", ns) if self._extract_all_text(t)]
        cells = [
            f"{c.get('N')}={c.get('V')}"
            for c in shape.findall(".//visio:Cell", ns)
            if c.get("N") and c.get("V") and any(k in c.get("N", "").lower() for k in ["process", "description", "notes", "name", "text"])
        ]
        return sname, texts, cells

    @log_execution_time("bpmn_generation_from_document_task.extract_visio_content")
    def _extract_visio_content(self, data: bytes, ext: str) -> str:
        """Extract content from Visio files (both VDX and VSDX formats)."""
        try:
            if ext == ".vsdx":
                return self._extract_vsdx_content(data)
            elif ext == ".vdx":
                return self._extract_vdx_content(data)
            else:
                return f"[Unsupported Visio format: {ext}]"
        except Exception as e:
            self._logger.warning(f"Could not extract Visio content: {e}")
            return f"[Visio extraction error: {str(e)}]"

    @log_execution_time("bpmn_generation_from_document_task.extract_vsdx_content")
    def _extract_vsdx_content(self, data: bytes) -> str:
        """Extract content from VSDX files using improved XML parsing."""
        items = []
        try:
            with zipfile.ZipFile(io.BytesIO(data)) as z:
                names = sorted(z.namelist())
                for name in names:
                    if not name.startswith("visio/"):
                        continue
                    if name.endswith(".xml"):
                        try:
                            content = z.read(name).decode("utf-8", errors="ignore")
                            if "pages/" in name:
                                items.append(f"=== PAGE: {name} ===")
                                items.extend(self._parse_visio_page_xml(content))
                            elif "masters/" in name:
                                items.append(f"=== MASTER: {name} ===")
                                items.extend(self._parse_visio_master_xml(content))
                        except Exception as e:
                            self._logger.warning(f"Could not parse {name}: {e}")
        except zipfile.BadZipFile as e:
            raise DocumentProcessingError(f"Invalid VSDX archive: {e}")

        if items:
            self._logger.info(f"Successfully extracted {len(items)} content items from VSDX")
            return "\n".join(items)
        else:
            self._logger.warning("No content extracted from VSDX file structure")
            return "[No content extracted from VSDX]"

    @log_execution_time("bpmn_generation_from_document_task.extract_vdx_content")
    def _extract_vdx_content(self, data: bytes) -> str:
        """Extract content from VDX files by parsing direct XML structure."""
        items = []
        try:
            xml = data.decode("utf-8", errors="ignore")
            root = ET.fromstring(xml)
            ns = {"visio": self.VISIO_NAMESPACES["v2003"]}

            # Find all pages in the VDX structure
            for page in root.findall(".//visio:Page", ns):
                pname = page.get("Name", "Unknown")
                items.append(f"=== PAGE: {pname} ===")
                for shape in page.findall(".//visio:Shape", ns):
                    sname, texts, cells = self._parse_shape(shape, ns)
                    for t in texts:
                        items.append(f"Shape '{sname}': {t}")
                    for c in cells:
                        items.append(f"Shape '{sname}' {c}")

            # Find master shapes
            for master in root.findall(".//visio:Master", ns):
                mname = master.get("Name", "Master")
                items.append(f"=== MASTER SHAPE: {mname} ===")
                for shape in master.findall(".//visio:Shape", ns):
                    sname, texts, cells = self._parse_shape(shape, ns)
                    for t in texts:
                        items.append(f"Master Text: {t}")

        except Exception as e:
            raise DocumentProcessingError(f"VDX parsing error: {e}")

        return "\n".join(items) or "[No content extracted from VDX]"

    @log_execution_time("bpmn_generation_from_document_task.parse_visio_page_xml")
    def _parse_visio_page_xml(self, xml_content: str) -> List[str]:
        """Parse Visio page XML to extract shape text and connector information."""
        content = []

        try:
            root = ET.fromstring(xml_content)

            # Try different namespaces
            for _ns_key, ns_url in self.VISIO_NAMESPACES.items():
                ns = {"visio": ns_url}
                shapes = root.findall(".//visio:Shape", ns)

                if shapes:
                    for shape in shapes:
                        sname, texts, cells = self._parse_shape(shape, ns)
                        for t in texts:
                            content.append(f"Shape '{sname}': {t}")
                        for c in cells:
                            content.append(f"Shape '{sname}' {c}")
                    break  # Found shapes with this namespace, stop trying others

            # If no shapes found with any namespace, try without namespace
            if not content:
                shapes = root.findall(".//Shape")
                for shape in shapes:
                    shape_id = shape.get("ID", "Unknown")
                    shape_name = shape.get("Name", f"Shape_{shape_id}")

                    # Extract text elements without namespace
                    for text_elem in shape.findall(".//Text"):
                        text_content = self._extract_all_text(text_elem)
                        if text_content:
                            content.append(f"Shape '{shape_name}': {text_content}")

                    # Extract relevant cell values without namespace
                    for cell in shape.findall(".//Cell"):
                        cell_name = cell.get("N", "")
                        cell_value = cell.get("V", "")
                        if cell_name and cell_value and any(k in cell_name.lower() for k in ["process", "description", "notes", "name", "text"]):
                            content.append(f"Shape '{shape_name}' {cell_name}={cell_value}")

        except ET.ParseError as e:
            self._logger.warning(f"XML parsing error in Visio page: {e}")

        return content

    def _extract_all_text(self, elem) -> str:
        """Extract all text content from an XML element, including nested elements."""
        parts = []
        if elem.text:
            parts.append(elem.text.strip())
        for ch in elem:
            txt = self._extract_all_text(ch)
            if txt:
                parts.append(txt)
            if ch.tail:
                parts.append(ch.tail.strip())
        return " ".join(parts)

    @log_execution_time("bpmn_generation_from_document_task.parse_visio_master_xml")
    def _parse_visio_master_xml(self, xml_content: str) -> List[str]:
        """Parse Visio master XML to extract shape template information."""
        content = []

        try:
            root = ET.fromstring(xml_content)

            # Extract master shape information
            master_name = root.get("Name", "Unknown Master")
            content.append(f"Master Shape Template: {master_name}")

            # Try different namespaces
            for _ns_key, ns_url in self.VISIO_NAMESPACES.items():
                ns = {"visio": ns_url}
                text_elements = root.findall(".//visio:Text", ns)

                if text_elements:
                    for text_elem in text_elements:
                        text_content = self._extract_all_text(text_elem)
                        if text_content:
                            content.append(f"Template Text: {text_content}")
                    break

            # Also try without namespace if no text found
            if len(content) <= 1:  # Only master name was added
                text_elements = root.findall(".//Text")
                for text_elem in text_elements:
                    text_content = self._extract_all_text(text_elem)
                    if text_content:
                        content.append(f"Template Text: {text_content}")

        except ET.ParseError as e:
            self._logger.warning(f"XML parsing error in Visio master: {e}")

        return content
