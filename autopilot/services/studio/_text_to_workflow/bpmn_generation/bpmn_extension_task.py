import asyncio
import os
import pathlib
from typing import Sequence

from fastapi.encoders import jsonable_encoder
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.runnables.config import RunnableConfig
from pydantic import ValidationError

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import (
    CANNOT_PROCESS_REQUEST_EXPLANATION,
    BpmnElementTypes,
    ExtensionTypes,
    ResourceKinds,
    ResourceTypes,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    ActionApp,
    Activity,
    AgentExtensionInfo,
    BaseExtensionResponse,
    BpmnExtensionToolResult,
    BpmnRequestContext,
    ChatHistory,
    Connector,
    ConnectorObject,
    ElementExtension,
    ElementExtensionInfo,
    ElementExtensionResult,
    ExtensionData,
    ExtensionInfo,
    ExtensionRequest,
    ModelType,
    Process,
    SolutionResource,
    Tool,
    ToolResult,
    Trigger,
)
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import BusinessProcessAgentState
from services.studio._text_to_workflow.bpmn_generation.connector_service import ActivityWithEmbedding, ConnectorService, ObjectWithEmbedding
from services.studio._text_to_workflow.bpmn_generation.fps_client import (
    get_action_apps,
    get_processes_and_agents,
    get_solution_resources,
)
from services.studio._text_to_workflow.utils import errors
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

LOGGER = AppInsightsLogger()
CONNECTOR_SERVICE: ConnectorService = ConnectorService()


class BpmnExtensionTask(BpmnBaseTask):
    def __init__(self):
        super().__init__("extension_prompt.yaml")
        self.examples = BpmnBaseTask.read_content((pathlib.Path(__file__).parent / "config/extension_examples.md").absolute())
        self.history_dependencies = [Tool.EXTENSION, Tool.EDIT_BPMN, Tool.QA]
        self.embedding_model = self.model_manager.get_embeddings_model("bpmn_embedding_model")
        self.patch_config = yaml_load(pathlib.Path(os.path.join(os.path.dirname(__file__), "config", "extension_patch_prompt.yaml")))

    @log_execution_time("bpmn_extension_task.generate")
    async def generate(self, context: BpmnRequestContext) -> ToolResult:
        bpmn_xml = context.current_bpmn
        extensionRequest, resources = await self._gather_resources(context)
        if not extensionRequest:
            LOGGER.error("No extension resources found")
            return BpmnExtensionToolResult(tool=Tool.EXTENSION, explanation="No extension resources found", title=None, update=None)

        # Generate extension suggesion using LLM
        model = context.override_model_type or ModelType.Anthropic
        result, _ = await self._generate(context.user_request, bpmn_xml, extensionRequest, model, context.chat_history)
        # LOGGER.info(f"Token usage of extension suggestion is : [{usage}]")

        # Parse and process suggestions
        extension_results = await self._process_extensions(context, result, resources)
        return BpmnExtensionToolResult(
            tool=Tool.EXTENSION,
            explanation=result.explanation,
            title=result.title,
            update=extension_results,
        )

    @log_execution_time("bpmn_extension_node.run")
    async def run(self, state: BusinessProcessAgentState, config: RunnableConfig):
        message_emitter = config.get("configurable", {})["message_emitter"]
        user_request = await self.custom_stream(state, message_emitter)

        context = state["context"]
        bpmn_xml = context.current_bpmn
        extensionRequest, resources = await self._gather_resources(context)
        tool_results = state.get("tool_results") or []
        if not extensionRequest:
            LOGGER.error("No extension resources found")
            await message_emitter.emit_message(CANNOT_PROCESS_REQUEST_EXPLANATION, end_stream=True)
            tool_results.append(None)
            return {"tool_results": tool_results, "current_tool_index": state["current_tool_index"] + 1}

        patch = None
        if len(tool_results) > 0 and tool_results[-1] and tool_results[-1]["tool"] == Tool.EDIT_BPMN:
            patch = {
                "add": tool_results[-1].get("add", []),
                "delete": tool_results[-1].get("delete", []),
                "update": tool_results[-1].get("update", []),
            }

        # Generate extension suggesion using LLM
        model = context.override_model_type or ModelType.Anthropic
        result, _ = await self._generate(user_request, bpmn_xml, extensionRequest, model, context.chat_history, True, patch)
        # LOGGER.info(f"Token usage of extension suggestion is : [{usage}]")

        # Parse and process suggestions
        extension_results = await self._process_extensions(context, result, resources)
        extension_tool_result = BpmnExtensionToolResult(
            tool=Tool.EXTENSION,
            explanation=result.explanation,
            title=result.title,
            update=extension_results,
        )
        output = {"tool": Tool.EXTENSION, "update": extension_results}
        await message_emitter.emit_message(jsonable_encoder(output), "result")

        tool_results.append(extension_tool_result)
        return {"tool_results": tool_results, "current_tool_index": state["current_tool_index"] + 1}

    @log_execution_time("bpmn_extension_task.call_llm")
    async def _generate(
        self,
        query: str,
        bpmn_xml: str,
        extensionRequest: ExtensionRequest,
        model: ModelType,
        chat_history: dict[Tool, list[ChatHistory]] | None = None,
        use_patch: bool = False,
        bpmn_patch: dict | None = None,
    ) -> tuple[ElementExtensionResult, TokenUsage]:
        prompts = self.patch_config["prompt"] if use_patch else self.config["prompt"]
        parser = PydanticOutputParser(pydantic_object=ElementExtensionResult)
        system_message = self._system_template(prompts["system"], partial_variables={"schema": parser.get_format_instructions()}).format(
            examples=self.examples,
        )

        model_name = self._get_model_name(model)
        chat_history_str = await self._get_related_chat_history(chat_history, self.history_dependencies, model_name)
        if use_patch:
            user_message = self._human_template(prompts["user"]).format(
                bpmn_xml=bpmn_xml,
                user_query=query,
                extensions=extensionRequest.model_dump_json(exclude_none=True, by_alias=True),
                chat_history=chat_history_str,
                bpmn_patch=bpmn_patch,
            )
        else:
            user_message = self._human_template(prompts["user"]).format(
                bpmn_xml=bpmn_xml,
                user_query=query,
                extensions=extensionRequest.model_dump_json(exclude_none=True, by_alias=True),
                chat_history=chat_history_str,
            )

        result, usage = await self._call_llm(system_message, user_message, use_case=model_name)

        try:
            return ElementExtensionResult.model_validate_json(result.strip("```json\n").strip("\n```")), usage
        except ValidationError as ve:
            LOGGER.error(f"Validation error in LLM response: {ve} while trying to parse: {result}")
            raise errors.FailedDependencyError(f"LLM response validation failed: {ve}. Please check the LLM response format.") from ve

    @log_execution_time("bpmn_extension_task.gather_resources")
    async def _gather_resources(self, context: BpmnRequestContext) -> tuple[ExtensionRequest, dict]:
        """Gather resources for BPMN extension from APIs."""
        if not context.extension_data_override:
            request_context = context.request_context
            if context.solution_id and context.project_key:
                api_calls = [
                    get_solution_resources(
                        request_context, context.solution_id, context.project_key, ResourceKinds.PROCESS, [ResourceTypes.PROCESS, ResourceTypes.AGENT]
                    ),
                    get_solution_resources(
                        request_context, context.solution_id, context.project_key, ResourceKinds.APP, [ResourceTypes.WORKFLOW_ACTION, ResourceTypes.VB_ACTION]
                    ),
                    CONNECTOR_SERVICE._get_connectors_and_agents(request_context),
                ]
            else:
                api_calls = [
                    get_processes_and_agents(request_context),
                    get_action_apps(request_context),
                    CONNECTOR_SERVICE._get_connectors_and_agents(request_context),
                ]
            # Execute API calls concurrently
            results = await asyncio.gather(*api_calls)
        else:
            # Use the overridden data if provided
            extension_data = context.extension_data_override
            connections = extension_data.connections or []
            external_agents: list[Process] = []
            connectors: list[Connector] = []
            for connector in connections:
                if connector.isExternalAgent:
                    external_agents.append(
                        Process(
                            id=connector.key,
                            name=connector.name,
                            type=ExtensionTypes.EXTERNAL_AGENT,
                            description=connector.description,
                            connection=None,
                            activity=None,
                            fakeId=None,
                            score=0,
                        )
                    )
                else:
                    connectors.append(connector)
            results = [extension_data.processes or [], extension_data.actionApps or [], (connectors, external_agents)]

        # Initialize extension request
        extension_request = ExtensionRequest(processes=[], agents=[], **{"human-in-the-loop": []})

        # Build simpler data structure for processes and agents and fake integer id for reducing token size
        processes = results[0]
        fake_id = 1
        processes_by_fake_id = {}
        for process in processes:
            process.fakeId = fake_id
            extension_info = ExtensionInfo(id=fake_id, name=process.name, description=process.description, score=None)
            if process.type == ExtensionTypes.AGENT or process.type == "agent":
                process.type = ExtensionTypes.AGENT
                extension_request.agents.append(extension_info)
            else:
                process.type = ExtensionTypes.PROCESS
                extension_request.processes.append(extension_info)
            processes_by_fake_id[process.fakeId] = process
            fake_id += 1

        # Build simpler data structure for action apps and fake integer id for reducing token size
        action_apps = results[1]
        action_apps_by_fake_id = {}
        for action_app in action_apps:
            action_app.fakeId = fake_id
            extension_request.actionApps.append(ExtensionInfo(id=fake_id, name=action_app.name, description=None, score=None))
            action_apps_by_fake_id[action_app.fakeId] = action_app
            action_app.type = ExtensionTypes.ACTION
            fake_id += 1

        # Build simpler data structure for connectors
        connectors, external_agents = results[2]
        connectors_by_key = {}
        for connector in connectors:
            connector.fakeId = fake_id
            extension_request.connections.append(ExtensionInfo(id=fake_id, name=connector.name, description=connector.description, score=None))
            connectors_by_key[connector.fakeId] = connector
            fake_id += 1

        for external_agent in external_agents:
            external_agent.fakeId = fake_id
            extension_request.agents.append(
                AgentExtensionInfo(id=fake_id, name=external_agent.name, description=external_agent.description, score=None, external=True)
            )
            processes_by_fake_id[external_agent.fakeId] = external_agent
            fake_id += 1

        return extension_request, {"processes": processes_by_fake_id, "apps": action_apps_by_fake_id, "connectors": connectors_by_key}

    async def _process_extensions(self, context: BpmnRequestContext, result: ElementExtensionResult, resources: dict) -> list[ElementExtension]:
        element_extensions = result.update or []
        element_extension_results: list[ElementExtension] = []
        processes = resources["processes"]
        action_apps = resources["apps"]
        connectors = resources["connectors"]
        for element_extension in element_extensions:
            task_type, res = self._build_extensions(element_extension, processes, action_apps, connectors)
            if res:
                element_extension.type = task_type or element_extension.type
                extension_result = ElementExtension(id=element_extension.id, type=element_extension.type, data=ExtensionData(suggestions=res))
                element_extension_results.append(extension_result)

                query_embedding = self.embedding_model.encode(f"{element_extension.purpose}", instruction_set="icl", instruction_type="query")
                if extension_result.type == BpmnElementTypes.SEND:
                    for extension in res:
                        if not isinstance(extension, Connector):
                            continue
                        extension.type = ExtensionTypes.CONNECTOR_ACTIVITY
                        extension.connection = await CONNECTOR_SERVICE._get_connection(extension.key, context)
                        if extension.connection:
                            activity: ActivityWithEmbedding = await CONNECTOR_SERVICE._get_activity_trigger(extension.key, context, "activity", query_embedding)
                            extension.activity = Activity(name=activity["name"], displayName=activity["displayName"], description=activity["description"])
                elif extension_result.type == BpmnElementTypes.RECEIVE:
                    for extension in res:
                        if not isinstance(extension, Connector):
                            continue
                        extension.type = ExtensionTypes.CONNECTOR_EVENT
                        extension.connection = await CONNECTOR_SERVICE._get_connection(extension.key, context)
                        if extension.connection:
                            trigger: ActivityWithEmbedding = await CONNECTOR_SERVICE._get_activity_trigger(extension.key, context, "trigger", query_embedding)
                            extension.trigger = Trigger(
                                name=trigger["name"], displayName=trigger["displayName"], description=trigger["description"], objectName=trigger["objectName"]
                            )

                elif extension_result.type == BpmnElementTypes.SERVICE:
                    for extension in res:
                        if isinstance(extension, Process) and extension.type == ExtensionTypes.EXTERNAL_AGENT:
                            extension.connection = await CONNECTOR_SERVICE._get_connection(extension.id, context)
                            if extension.connection:
                                object: ObjectWithEmbedding | None = await CONNECTOR_SERVICE._get_object(extension.id, context, query_embedding)
                                if object:
                                    execution_type = object["executionType"]
                                    extension.type = f"Intsvc.{execution_type.capitalize()}AgentExecution"
                                    extension.activity = ConnectorObject(name=object["name"], displayName=object["displayName"], executionType=execution_type)

            else:
                LOGGER.error(f"LLM generated hallucinated extension id for element [{element_extension.type}/{element_extension.id}]")

        return element_extension_results

    def _build_extensions(
        self, task: ElementExtensionInfo, processes: dict[int, Process], action_apps: dict[int, ActionApp], connectors: dict[int, Connector]
    ) -> tuple[str | None, list[Process | ActionApp | Connector | SolutionResource]]:
        extensions: Sequence = (task.data or {}).extensions or []
        result: list[Process | ActionApp | Connector | SolutionResource] = []
        task_type: str | None = None
        for extension in extensions:
            # Check if extension id returned from LLM is valid
            resources_by_id = None
            if extension.id in processes:
                task_type = BpmnElementTypes.SERVICE
                resources_by_id = processes
            elif extension.id in action_apps:
                task_type = BpmnElementTypes.USER
                resources_by_id = action_apps
            elif extension.id in connectors:
                if task.type == BpmnElementTypes.RECEIVE or task.type == BpmnElementTypes.SEND:
                    task_type = task.type
                else:
                    task_type = BpmnElementTypes.RECEIVE
                resources_by_id = connectors
            if resources_by_id:
                res: BaseExtensionResponse = resources_by_id[extension.id]
                res.score = extension.score or 0
                result.append(res)
            else:
                LOGGER.error(f"LLM generated hallucinated extension id [{extension.id}] for element [{task.type}/{task.id}]")

        return task_type, result
