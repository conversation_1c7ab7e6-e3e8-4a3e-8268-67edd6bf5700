import typing_extensions as t
from aiocache import cached
from sentence_transformers import util

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import ExtensionTypes
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnRequestContext,
    Connection,
    Connector,
    ExtensionDataOverride,
    Process,
)
from services.studio._text_to_workflow.bpmn_generation.fps_client import get_activities_and_triggers, get_connection, get_connector_elements, get_objects
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time

LOGGER = AppInsightsLogger()


class ActivityWithEmbedding(t.TypedDict):
    name: str
    displayName: str
    description: str | None
    objectName: str | None


class ObjectWithEmbedding(t.TypedDict):
    name: str
    displayName: str
    executionType: str


class ConnectorService:
    def __init__(self):
        self.embedding_model = ModelManager().get_embeddings_model("bpmn_embedding_model")

    def _search(self, query_embedding, records):
        """
        Perform semantic search using cosine similarity
        """
        # Check if records are empty
        if not records:
            return None
        for record in records:
            similarity = util.cos_sim(query_embedding, record["embedding"]).item()
            record["score"] = similarity

        # Sort results by similarity in descending order
        return max(records, key=lambda p: p["score"] or 0)

    def connector_activity_cache_key(*args, **kwargs):
        connector = args[2]
        request_context = args[3]
        tenant_id = request_context.tenant_id if isinstance(request_context, RequestContext) else ""
        return f"{tenant_id}_{connector}"

    # Apply the cache with the custom key function
    @cached(ttl=43200, key_builder=connector_activity_cache_key)
    async def get_activities_and_triggers(self, connector: str, request_context: RequestContext):
        activities, triggers = await get_activities_and_triggers(request_context, connector)
        cached_activities = [
            ActivityWithEmbedding(name=activity.name, displayName=activity.displayName, description=activity.description, objectName=None)
            for activity in activities
        ]
        cached_triggers = [
            ActivityWithEmbedding(name=trigger.name, displayName=trigger.displayName, description=trigger.description, objectName=None) for trigger in triggers
        ]
        self._build_activities_embeddings(cached_activities)
        self._build_activities_embeddings(cached_triggers)
        return cached_activities, cached_triggers

    def connector_object_cache_key(*args, **kwargs):
        connector = args[2]
        request_context = args[3]
        tenant_id = request_context.tenant_id if isinstance(request_context, RequestContext) else ""
        return f"{tenant_id}_{connector}_objects"

    # Apply the cache with the custom key function
    @cached(ttl=43200, key_builder=connector_object_cache_key)
    async def get_objects(self, connector: str, request_context: RequestContext):
        objects = await get_objects(request_context, connector)
        cached_objects = [ObjectWithEmbedding(name=obj.name, displayName=obj.displayName, executionType=obj.executionType) for obj in objects]
        self._build_activities_embeddings(cached_objects)
        return cached_objects

    def _build_activities_embeddings(self, activities):
        if len(activities) == 0:
            return activities

        texts = [f"{activity['displayName']} {activity.get('description', '')}".strip() for activity in activities]
        embeddings = self.embedding_model.encode_batch(texts, batch_size=64, instruction_set="icl", instruction_type="query")
        assert len(embeddings) == len(activities), "The number of embeddings should be equal to the number of tasks"
        for i in range(len(embeddings)):
            activities[i]["embedding"] = embeddings[i]

        return activities

    def connector_elements_cache_key(*args, **kwargs):
        request_context = args[2]
        tenant_id = request_context.tenant_id if isinstance(request_context, RequestContext) else ""
        return f"{tenant_id}_connector_elements"

    @cached(ttl=43200, key_builder=connector_elements_cache_key)
    async def get_connectors(self, request_context: RequestContext):
        return await get_connector_elements(request_context)

    async def _get_connectors_and_agents(self, request_context: RequestContext | None) -> tuple[list[Connector], list[Process]]:
        connectors, agents = await self.get_connectors(request_context)

        cached_connectors = [
            Connector(
                name=connector.name,
                key=connector.key,
                description=connector.description,
                connection=None,
                activity=None,
                trigger=None,
                fakeId=None,
                score=0,
                type="",
                isExternalAgent=False,
                inputJson=None,
            )
            for connector in connectors
        ]
        cached_agents = [
            Process(
                id=agent.key,
                name=agent.name,
                type=ExtensionTypes.EXTERNAL_AGENT,
                description=agent.description,
                connection=None,
                activity=None,
                fakeId=None,
                score=0,
            )
            for agent in agents
        ]
        return cached_connectors, cached_agents

    @log_execution_time(tracked_section="bpmn_extension_task.get_activity_trigger")
    async def _get_activity_trigger(self, connector: str, context: BpmnRequestContext, task_type, query_embedding) -> ActivityWithEmbedding | None:
        """
        Get activity or trigger for the given connector
        """
        if not context.extension_data_override:
            activities, triggers = await self.get_activities_and_triggers(connector, context.request_context)
        else:
            activities, triggers = self.get_override_activities_and_triggers(connector, context)
        return self._search(query_embedding, activities if task_type == "activity" else triggers)

    async def _get_object(self, connector: str, context: BpmnRequestContext, query_embedding) -> ObjectWithEmbedding | None:
        if not context.extension_data_override:
            objects = await self.get_objects(connector, context.request_context)
        else:
            objects = self.get_override_objects(connector, context)
        return self._search(query_embedding, objects)

    async def _get_connection(self, connector: str, context: BpmnRequestContext, connection_name: str | None = None) -> Connection | None:
        if not context.extension_data_override:
            return await get_connection(context.request_context, connector, connection_name)
        else:
            extension_data = context.extension_data_override or ExtensionDataOverride()
            connections = extension_data.connections or []
            for connection in connections:
                if connection.key == connector:
                    return connection.connection
        return None

    def get_override_activities_and_triggers(self, connector: str, context: BpmnRequestContext):
        extension_data = context.extension_data_override or ExtensionDataOverride()
        connector_activities = extension_data.activities or {}
        connector_triggers = extension_data.triggers or {}
        activities = connector_activities.get(connector, [])
        triggers = connector_triggers.get(connector, [])

        cached_activities = [
            ActivityWithEmbedding(name=activity.name, displayName=activity.displayName, description=activity.description, objectName=None)
            for activity in activities
        ]
        cached_triggers = [
            ActivityWithEmbedding(name=trigger.name, displayName=trigger.displayName, description=trigger.description, objectName=None) for trigger in triggers
        ]

        self._build_activities_embeddings(cached_activities)
        self._build_activities_embeddings(cached_triggers)
        return cached_activities, cached_triggers

    def get_override_objects(self, connector: str, context: BpmnRequestContext):
        extension_data = context.extension_data_override or ExtensionDataOverride()
        connector_objects = extension_data.objects or {}
        objects = connector_objects.get(connector, [])

        cached_objects = [
            ObjectWithEmbedding(name=activity.name, displayName=activity.displayName, executionType=activity.executionType) for activity in objects
        ]

        self._build_activities_embeddings(cached_objects)
        return cached_objects
