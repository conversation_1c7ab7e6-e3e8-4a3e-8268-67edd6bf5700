element_types: |
  ### Parent Objects
  1. `bpmn:collaboration`
  2. `bpmn:participant`
  3. `bpmn:process`
  4. `bpmn:subProcess`
  5. `bpmn:callActivity`
  6. `bpmn:laneSet`
  7. `bpmn:childLaneSet`
  8. `bpmn:lane`

  ### Flow Objects
  #### Events
  9. `bpmn:startEvent`
  10. `bpmn:endEvent`
  11. `bpmn:intermediateCatchEvent`
  12. `bpmn:intermediateThrowEvent`
  13. `bpmn:boundaryEvent`
  14. `bpmn:signal`
  15. `bpmn:error`
  16. `bpmn:escalation`

  ### Event Definitions
  Note: They are not standalone elements and do not add, update, or delete as a single item.
  17. `bpmn:timerEventDefinition`
  18. `bpmn:messageEventDefinition`
  19. `bpmn:signalEventDefinition`
  20. `bpmn:errorEventDefinition`
  21. `bpmn:escalationEventDefinition`
  22. `bpmn:compensateEventDefinition`
  23. `bpmn:conditionalEventDefinition`
  24. `bpmn:terminateEventDefinition`

  ### Activities
  25. `bpmn:userTask`
  26. `bpmn:sendTask`
  27. `bpmn:receiveTask`
  28. `bpmn:manualTask`
  29. `bpmn:scriptTask`
  30. `bpmn:businessRuleTask`
  31. `bpmn:serviceTask`

  ### Gateways
  32. `bpmn:exclusiveGateway`
  33. `bpmn:parallelGateway`
  34. `bpmn:inclusiveGateway`
  35. `bpmn:eventBasedGateway`

  ### Connecting Objects
  36. `bpmn:sequenceFlow`
  37. `bpmn:messageFlow`
  38. `bpmn:association`
  39. `bpmn:dataInputAssociation`
  40. `bpmn:dataOutputAssociation`

  ### Artifacts
  41. `bpmn:dataObjectReference`
  42. `bpmn:dataStoreReference`
  43. `bpmn:textAnnotation`
  
elements:
  bpmn:collaboration: |
    {
      "id": String,
      "name": String,
      "type": "bpmn:collaboration"
    }
  
  bpmn:participant: |
    {
      "id": String,
      "name": String,
      "type": "bpmn:participant",
      "parentId": String, // immediate collaboration id
      "data":
      {
          "processRef": String, // reffered process id
          "label": String,
          "key": "value" // or "key": {}
      }
    }
  
  bpmn:process: |
    {
      "id": String,
      "name": String,
      "type": "bpmn:process",
      "data":
      {
          "label": String,
          "key": "value" // or "key": {}
      }
    }
  
  bpmn:subProcess: |
    {
      "id": String,
      "name": String,
      "type": "bpmn:subProcess",
      "parentId": String, // immediate parent process or subprocess id
      "data":
      {
          "label": String,
          "isExpanded": Boolean, // optional
          "triggeredByEvent": Boolean, // optional
          "laneId": String, // optional, avialble only when under a Lane
          "participantId": String // optional, available when under a Participant
      }
    }
  
  bpmn:callActivity: |
    {
      "id": String,
      "name": String,
      "type": "bpmn:callActivity",
      "parentId": String, // immediate parent process or subprocess id
      "data":
      {
          "label": String,
          "calledElement": String, // the element id intend to call
          "laneId": String, // optional, avialble only when under a Lane
          "participantId": String, // optional, available when under a Participant
          "variablesMapping": {"key": "value"} // optional
      }
    }
  
  bpmn:laneSet: |
    {
      "id": String,
      "type": "bpmn:laneSet",
      "parentId": String // immediate parent process or subprocess id
    }
  
  bpmn:childLaneSet: |
    {
      "id": String,
      "type": "bpmn:childLaneSet",
      "parentId": String // immediate parent LaneSet or Lane id
    }
  
  bpmn:lane: |
    {
      "id": String,
      "type": "bpmn:lane",
      "parentId": String // immediate parent LaneSet or ChildLaneSet id
      "data": {
        "participantId": String, // the Participant id this lane belongs to
      }
    }
  
  bpmn:startEvent: |
    {
      "type": "bpmn:startEvent",
      "id": String,
      "parentId": String,
      "name": String,
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String, // optional, available when under a Participant
        "eventDefinition": // Optional
          {
              "type": String, // allowed types: Timer, Message, Signal, Error, Escalation, Compensation, Conditional
              "key": "value" // Optional additional attributes specific to the element type
          }
      }
    }

    **Examples:**
    ```json
      {
          "type": "bpmn:startEvent",
          "id": "Event_start",
          "parentId": "Process_112358",
          "name": "goodStart",
          "data": {
              "label": "Start of a process 111111"
          }
      }
    ```

    ```json
      {
          "type": "bpmn:startEvent",
          "id": "Event_start_timer",
          "parentId": "Process_112358",
          "name": "goodStart",
          "data": {
              "label": "A timer start event",
              "eventDefinition": {
                  "type": "bpmn:timerEventDefinition",
                  "timeCycle": "PT1H/T2"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:startEvent",
          "id": "Event_start_msg",
          "parentId": "Process_112358",
          "name": "goodStart",
          "data": {
              "label": "A message start event",
              "eventDefinition": {
                  "type": "bpmn:messageEventDefinition",
                  "messageRef": "Message_1enp267"
              }
          }
      }
    ```

    ```json
    {
        "type": "bpmn:startEvent",
        "id": "Event_start_sig",
        "parentId": "Process_112358",
        "name": "goodStart",
        "data": {
            "label": "A signal start event",
            "eventDefinition": {
                "type": "bpmn:signalEventDefinition",
                "signalRef": "Signal_GCXfZu" 
            }
        }
    }
    ```

    ```json
      {
          "type": "bpmn:startEvent",
          "id": "Event_start_error",
          "parentId": "Process_112358",
          "name": "errorStart",
          "data": {
              "label": "An error start event",
              "eventDefinition": {
                  "type": "bpmn:errorEventDefinition",
                  "errorRef": "Error_9876AB"
              }
          }
      }
    ```

    ```json
        {
            "type": "bpmn:startEvent",
            "id": "Event_start_escalation",
            "parentId": "Process_112358",
            "name": "escalationStart",
            "data": {
                "label": "An escalation start event",
                "eventDefinition": {
                    "type": "bpmn:escalationEventDefinition",
                    "escalationRef": "Escalation_1234CD"
                }
            }
        }
    ```

    ```json
        {
            "type": "bpmn:startEvent",
            "id": "Event_start_compensation",
            "parentId": "Process_112358",
            "name": "compensationStart",
            "data": {
                "label": "A compensation start event",
                "eventDefinition": {
                    "type": "bpmn:compensateEventDefinition"
                }
            }
        }
    ```

    ```json
        {
            "type": "bpmn:startEvent",
            "id": "Event_start_conditional",
            "parentId": "Process_112358",
            "name": "conditionalStart",
            "data": {
                "label": "A conditional start event",
                "eventDefinition": {
                    "type": "bpmn:conditionalEventDefinition",
                    "conditionExpression": "${temperature > 30}"
                }
            }
        }
    ```
  
  bpmn:endEvent: |
    {
      "type": "bpmn:endEvent",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String, // optional, available when under a Participant
        "eventDefinition": // Optional
          {
              "type": String, // allowed types: Message, Signal, Error, Escalation, Compensation, Terminate
              "key": "value" // Optional additional attributes specific to the element type
          }
      }
    }

    **Examples:**
    ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end",
          "parentId": "Process_45678",
          "name": "processEnd",
          "data": {
              "label": "End of the process"
          }
      }
    ```

    ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end_message",
          "parentId": "Process_45678",
          "name": "messageEnd",
          "data": {
              "label": "A message end event",
              "eventDefinition": {
                  "type": "bpmn:messageEventDefinition",
                  "messageRef": "Message_5678EF"
              }
          }
      }
    ```
    ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end_signal",
          "parentId": "Process_45678",
          "name": "signalEnd",
          "data": {
              "label": "A signal end event",
              "eventDefinition": {
                  "type": "bpmn:signalEventDefinition",
                  "signalRef": "Signal_9012GH"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end_error",
          "parentId": "Process_45678",
          "name": "errorEnd",
          "data": {
              "label": "An error end event",
              "eventDefinition": {
                  "type": "bpmn:errorEventDefinition",
                  "errorRef": "Error_3456IJ"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end_escalation",
          "parentId": "Process_45678",
          "name": "escalationEnd",
          "data": {
              "label": "An escalation end event",
              "eventDefinition": {
                  "type": "bpmn:escalationEventDefinition",
                  "escalationRef": "Escalation_7890KL"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end_compensation",
          "parentId": "Process_45678",
          "name": "compensationEnd",
          "data": {
              "label": "A compensation end event",
              "eventDefinition": {
                  "type": "bpmn:compensateEventDefinition"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:endEvent",
          "id": "Event_end_terminate",
          "parentId": "Process_45678",
          "name": "terminateEnd",
          "data": {
              "label": "A terminate end event",
              "eventDefinition": {
                  "type": "bpmn:terminateEventDefinition"
              }
          }
      }
    ```
  
  bpmn:intermediateCatchEvent: |
    {
      "type": "bpmn:intermediateCatchEvent",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String, // optional, available when under a Participant
        "eventDefinition":
          {
              "type": String, // allowed types: Timer, Message, Signal, Conditional
              "key": "value" // Optional additional attributes specific to the element type
          }
      }
    }

    **Examples:**
    ```json
      {
        "type": "bpmn:intermediateCatchEvent",
        "id": "Event_timer_catch",
        "parentId": "Process_45678",
        "name": "timerCatch",
        "data": {
            "label": "A timer intermediate catch event",
            "eventDefinition": {
                "type": "bpmn:timerEventDefinition",
                "timeDuration": "PT5M"
            }
        }
      }
    ```

    ```json
      {
          "type": "bpmn:intermediateCatchEvent",
          "id": "Event_message_catch",
          "parentId": "Process_45678",
          "name": "messageCatch",
          "data": {
              "label": "A message intermediate catch event",
              "eventDefinition": {
                  "type": "bpmn:messageEventDefinition",
                  "messageRef": "Message_ABC123"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:intermediateCatchEvent",
          "id": "Event_signal_catch",
          "parentId": "Process_45678",
          "name": "signalCatch",
          "data": {
              "label": "A signal intermediate catch event",
              "eventDefinition": {
                  "type": "bpmn:signalEventDefinition",
                  "signalRef": "Signal_789XYZ"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:intermediateCatchEvent",
          "id": "Event_conditional_catch",
          "parentId": "Process_45678",
          "name": "conditionalCatch",
          "data": {
              "label": "A conditional intermediate catch event",
              "eventDefinition": {
                  "type": "bpmn:conditionalEventDefinition",
                  "conditionExpression": "${orderAmount > 1000}"
              }
          }
      }
    ```

  
  bpmn:intermediateThrowEvent: |
    {
      "type": "bpmn:intermediateThrowEvent",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String, // optional, available when under a Participant
        "eventDefinition": // Optional
          {
              "type": String, // allowed types: Message, Signal, Escalation, Compensation
              "key": "value" // Optional additional attributes specific to the element type
          }
      }
    }

    **Examples:**
    ```json
      {
          "type": "bpmn:intermediateThrowEvent",
          "id": "Event_none_throw",
          "parentId": "Process_45678",
          "name": "noneThrow",
          "data": {
              "label": "A generic intermediate throw event with no specific definition"
          }
      }
    ```

    ```json
      {
          "type": "bpmn:intermediateThrowEvent",
          "id": "Event_message_throw",
          "parentId": "Process_45678",
          "name": "messageThrow",
          "data": {
              "label": "A message intermediate throw event",
              "eventDefinition": {
                  "type": "bpmn:messageEventDefinition",
                  "messageRef": "Message_ABC123"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:intermediateThrowEvent",
          "id": "Event_signal_throw",
          "parentId": "Process_45678",
          "name": "signalThrow",
          "data": {
              "label": "A signal intermediate throw event",
              "eventDefinition": {
                  "type": "bpmn:signalEventDefinition",
                  "signalRef": "Signal_789XYZ"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:intermediateThrowEvent",
          "id": "Event_escalation_throw",
          "parentId": "Process_45678",
          "name": "escalationThrow",
          "data": {
              "label": "An escalation intermediate throw event",
              "eventDefinition": {
                  "type": "bpmn:escalationEventDefinition",
                  "escalationRef": "Escalation_456XYZ"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:intermediateThrowEvent",
          "id": "Event_compensation_throw",
          "parentId": "Process_45678",
          "name": "compensationThrow",
          "data": {
              "label": "A compensation intermediate throw event",
              "eventDefinition": {
                  "type": "bpmn:compensateEventDefinition",
                  "activityRef": "Task_CompensateXYZ"
              }
          }
      }
    ```
  
  bpmn:boundaryEvent: |
    {
      "type": "bpmn:boundaryEvent",
      "id": String,
      "parentId": String, // the element this boundry event is attched to
      "name": String, // Optional
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String, // optional, available when under a Participant
        "eventDefinition":
          {
              "type": String, // allowed types: Message, Timer, Error, Signal, Escalation, Compensation, Conditional
              "isInterrupting": Boolean, // Optional, indicates if the bounday event interrupt the flow, if original xml contains `cancelActivity="false"` then it is false
              "key": "value" // Optional additional attributes specific to the element type
          }
      }
    }

    **Examples:**
    ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_message_boundary",
          "parentId": "Task_1234",
          "name": "messageBoundary",
          "data": {
              "label": "A message boundary event",
              "eventDefinition": {
                  "type": "bpmn:messageEventDefinition",
                  "messageRef": "Message_ABC123"
              }
          }
      }
    ```
    ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_timer_boundary",
          "parentId": "Task_5678",
          "name": "timerBoundary",
          "data": {
              "label": "A timer boundary event",
              "eventDefinition": {
                  "type": "bpmn:timerEventDefinition",
                  "timeDuration": "PT10M"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_timer_boundary",
          "parentId": "Task_5678",
          "name": "timerBoundary",
          "data": {
              "label": "A timer boundary event",
              "eventDefinition": {
                  "type": "bpmn:timerEventDefinition",
                  "isInterrupting": false,
                  "timeDuration": "PT10M"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_error_boundary",
          "parentId": "Task_9876",
          "name": "errorBoundary",
          "data": {
              "label": "An error boundary event",
              "eventDefinition": {
                  "type": "bpmn:errorEventDefinition",
                  "errorRef": "Error_567XYZ"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_signal_boundary",
          "parentId": "Task_6543",
          "name": "signalBoundary",
          "data": {
              "label": "A signal boundary event",
              "eventDefinition": {
                  "type": "bpmn:signalEventDefinition",
                  "signalRef": "Signal_ABC987"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_signal_boundary",
          "parentId": "Task_6543",
          "name": "signalBoundary",
          "data": {
              "label": "A signal boundary event",
              "eventDefinition": {
                  "type": "bpmn:signalEventDefinition",
                  "signalRef": "Signal_ABC987"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_escalation_boundary",
          "parentId": "Task_2468",
          "name": "escalationBoundary",
          "data": {
              "label": "An escalation boundary event",
              "eventDefinition": {
                  "type": "bpmn:escalationEventDefinition",
                  "escalationRef": "Escalation_999XYZ"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_compensation_boundary",
          "parentId": "Task_1357",
          "name": "compensationBoundary",
          "data": {
              "label": "A compensation boundary event",
              "eventDefinition": {
                  "type": "bpmn:compensateEventDefinition",
                  "activityRef": "Task_CompensateXYZ"
              }
          }
      }
    ```

    ```json
      {
          "type": "bpmn:boundaryEvent",
          "id": "Event_conditional_boundary",
          "parentId": "Task_7890",
          "name": "conditionalBoundary",
          "data": {
              "label": "A conditional boundary event",
              "eventDefinition": {
                  "type": "bpmn:conditionalEventDefinition",
                  "conditionExpression": "${orderAmount > 500}"
              }
          }
      }
    ```

  
  bpmn:signal: |
    {
      "type": "bpmn:signal",
      "id": String,
      "name": String, // Optional
      "data": {
        "label": String
      }
    }
  
  bpmn:error: |
    {
      "type": "bpmn:error",
      "id": String,
      "name": String, // Optional
      "data": {
        "label": String,
        "errorCode": String
      }
    }
  
  bpmn:escalation: |
    {
      "type": "bpmn:escalation",
      "id": String,
      "name": String, // Optional
      "data": {
        "label": String,
        "escalationCode": String
      }
    }
  
  bpmn:userTask: |
    {
      "type": "bpmn:userTask",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String // optional, available when under a Participant
      }
    }
  
  bpmn:sendTask: |
    {
      "type": "bpmn:sendTask",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String, // optional, available when under a Participant
        "messageRef": String // Optional
      }
    }
  
  bpmn:receiveTask: |
    {
      "type": "bpmn:receiveTask",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String, // optional, available when under a Participant
        "messageRef": String // Optional
      }
    }
  
  bpmn:manualTask: |
    {
      "type": "bpmn:manualTask",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String // optional, available when under a Participant
      }
    }
  
  bpmn:scriptTask: |
    {
      "type": "bpmn:scriptTask",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String, // optional, available when under a Participant
        "scriptFormat": String, // Optional
        "script": String // Optional
      }
    }
  
  bpmn:businessRuleTask: |
    {
      "type": "bpmn:businessRuleTask",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String, // optional, available when under a Participant
        "ruleSet": String
      }
    }

  bpmn:serviceTask: |
    {
      "type": "bpmn:serviceTask",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String, // optional, available when under a Participant
        "implementation": String // optional 
      }
    }
  
  bpmn:exclusiveGateway: |
    {
      "type": "bpmn:exclusiveGateway",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String, // Optional
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String // optional, available when under a Participant
      }
    }
  
  bpmn:parallelGateway: |
    {
      "type": "bpmn:parallelGateway",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String, // Optional
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String // optional, available when under a Participant
      }
    }
  
  bpmn:inclusiveGateway: |
    {
      "type": "bpmn:inclusiveGateway",
      "id": String,
      "parentId": String,
      "name": String, // Optional
      "data": {
        "label": String, // Optional
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String, // optional, available when under a Participant
        "conditionExpressions": String
      }
    }
  
  bpmn:eventBasedGateway: |
    {
      "type": "bpmn:eventBasedGateway",
      "id": String,
      "parentId": String,
      "name": String,
      "data":{
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String // optional, available when under a Participant
      }
    }
  
  bpmn:sequenceFlow: |
    {
      "type": "bpmn:sequenceFlow",
      "id": "String",
      "parentId": String,
      "source": "String", // ID of source node
      "target": "String", // ID of target node
      "data":
      {
          "label": "String", //optional, description 
          "conditionExpression": "String"
      }
    }
  
  bpmn:messageFlow: |
    {
      "type": "bpmn:messageFlow",
      "id": String,
      "parentId": String, // Id of collaboration
      "source": String,   // ID of source node
      "target": String,   // ID of target node
      "data": {
        "label": String,
        "messageRef": String // Optional
      }
    }
  
  bpmn:association: |
    {
      "type": "bpmn:association",
      "id": String,
      "parentId": String,
      "source": String,   // ID of source node
      "target": String,   // ID of target node
      "data": {
        "label": String
      }
    }
  
  bpmn:dataInputAssociation: |
    {
      "type": "bpmn:dataInputAssociation",
      "id": String,
      "parentId": String,
      "source": String,   // ID of source node
      "target": String,   // ID of target node
    }
  
  bpmn:dataOutputAssociation: |
    {
      "type": "bpmn:dataOutputAssociation",
      "id": String,
      "parentId": String,
      "source": String,   // ID of source node
      "target": String,   // ID of target node
    }
  
  bpmn:dataObjectReference: |
    {
      "type": "bpmn:dataObjectReference",
      "id": String,
      "parentId": String,
      "name": String,
      "data": {
        "dataState": String, // optional
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String // optional, available when under a Participant
      }
    }
  
  bpmn:dataStoreReference: |
    {
      "type": "bpmn:dataStoreReference",
      "id": String,
      "parentId": String,
      "name": String,
      "data": {
        "storageType": String, // optional, value can be database or file
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String // optional, available when under a Participant
      }
    }
  
  bpmn:textAnnotation: |
    {
      "type": "bpmn:textAnnotation",
      "id": String,
      "parentId": String,
      "data": {
        "label": String,
        "laneId": String, // optional, avialble only when under a Lane
        "participantId": String, // optional, available when under a Participant
        "textContent": String
      }
    }
