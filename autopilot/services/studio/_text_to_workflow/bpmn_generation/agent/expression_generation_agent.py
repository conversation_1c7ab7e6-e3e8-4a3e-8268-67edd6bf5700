import json
from typing import TypedDict

from fastapi.encoders import jsonable_encoder
from langchain_core.runnables.config import RunnableConfig
from pydantic import ValidationError

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnRequestContext,
    ExpressionGenerationAnalysisInput,
    ExpressionGenerationResult,
    ExpressionGenerationToolResult,
    ModelType,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import BusinessProcessAgentState
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time

LOGGER = AppInsightsLogger()

MODEL_TYPE_LITE = {
    ModelType.Google: "expression_generation_bpmn_model_google",
    ModelType.OpenAI: "expression_generation_bpmn_model_openai",
}


class ExpressionGenerationError(Exception):
    """Base exception for expression generation failures"""


class AgentState(TypedDict):
    analysis_input: ExpressionGenerationAnalysisInput
    expression_result: ExpressionGenerationResult | None
    usage_token: list[TokenUsage]


class ExpressionGenerationAgent(BpmnBaseTask):
    def __init__(self) -> None:
        super().__init__("expression_agent.yaml")  # not used

    @log_execution_time("bpmn_expression_generation_agent.generate.generate_expression")
    async def generate_expression(self, analysis_input: ExpressionGenerationAnalysisInput, bpmn_patch: str | None = None) -> dict:
        formatted_system_prompt = self._system_template(self.config["system_msg"]).format(
            input_schema=self.config["input_format"], output_schema=self.config["output_format"], examples=self.config["examples"]
        )
        formatted_user_prompt = self._human_template(self.config["user_msg"]).format(analysis_input=analysis_input, bpmn_patch=json.dumps(bpmn_patch or {}))

        use_case = MODEL_TYPE_LITE.get(analysis_input.override_model_type, "expression_generation_model_google")

        response, token_usage = await self._call_llm(formatted_system_prompt, formatted_user_prompt, use_case)
        stripped_response = response.strip("```json\n").strip("\n```")

        try:
            response_obj = json.loads(stripped_response)
            expression_result = ExpressionGenerationResult(**response_obj)
            return {"expression_result": expression_result, "usage_token": [token_usage]}
        except (json.JSONDecodeError, ValidationError) as e:
            LOGGER.error(f"Failed to parse LLM response: {str(e)}")
            raise ExpressionGenerationError(f"Failed to parse LLM response: {str(e)}") from e

    async def generate(self, context: BpmnRequestContext) -> ToolResult:
        user_request = context.user_request
        current_bpmn = context.current_bpmn
        chat_history = context.chat_history
        model_override_type = context.override_model_type or ModelType.Google

        agent_input = ExpressionGenerationAnalysisInput(
            currentBpmn=current_bpmn, userRequest=user_request, chatHistory=chat_history, override_model_type=model_override_type
        )

        try:
            result = await self.generate_expression(agent_input)
            expression_result = result["expression_result"]
        except ExpressionGenerationError as e:
            return ToolResult(tool=Tool.GENERATE_EXP, explanation=str(e))
        return ExpressionGenerationToolResult(
            tool=Tool.GENERATE_EXP,
            expression=expression_result.expression,
            elementId=expression_result.elementId,
            dataType=expression_result.dataType,
            varName=expression_result.varName,
            explanation=expression_result.explanation,
            currentExpression=expression_result.currentExpression.lstrip("="),  # a backup to eliminate the left most = in expression
        )

    @log_execution_time("bpmn_expression_node.run")
    async def run(self, state: BusinessProcessAgentState, config: RunnableConfig):
        message_emitter = config.get("configurable", {})["message_emitter"]
        user_request = await self.custom_stream(state, message_emitter)

        context = state["context"]
        model_override_type = context.override_model_type or ModelType.Google
        tool_results = state.get("tool_results") or []
        bpmn_patch = None
        for tool_result in tool_results:
            if tool_result and tool_result["tool"] == Tool.EDIT_BPMN:
                bpmn_patch = json.dumps(
                    {
                        "add": tool_result.get("add", []),
                        "delete": tool_result.get("delete", []),
                        "update": tool_result.get("update", []),
                    }
                )
                break

        agent_input = ExpressionGenerationAnalysisInput(
            currentBpmn=context.current_bpmn,
            userRequest=user_request,
            chatHistory=context.chat_history,
            override_model_type=model_override_type,
        )

        try:
            result = await self.generate_expression(agent_input, bpmn_patch)
            expression_result = result["expression_result"]
        except ExpressionGenerationError as e:
            await message_emitter.emit_message(str(e), end_stream=True)
            tool_results.append(None)
            return {"tool_results": tool_results, "current_tool_index": state["current_tool_index"] + 1}

        expression_tool_result = ExpressionGenerationToolResult(
            tool=Tool.GENERATE_EXP,
            expression=expression_result.expression,
            elementId=expression_result.elementId,
            dataType=expression_result.dataType,
            varName=expression_result.varName,
            explanation=expression_result.explanation,
            currentExpression=expression_result.currentExpression.lstrip("="),
        )

        output = {"tool": Tool.GENERATE_EXP, "update": expression_result}
        await message_emitter.emit_message(jsonable_encoder(output), "result")

        tool_results.append(expression_tool_result)
        return {"tool_results": tool_results, "current_tool_index": state["current_tool_index"] + 1}
