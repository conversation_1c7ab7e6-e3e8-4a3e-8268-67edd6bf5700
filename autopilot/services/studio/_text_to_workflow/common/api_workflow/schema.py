import pathlib
from typing import Any, ForwardRef, Literal, Optional, Union

from pydantic import BaseModel, ConfigDict, Field, create_model, model_serializer

from services.studio._text_to_workflow.api_workflow.api_workflow_utils import rewrite_code_as_literal
from services.studio._text_to_workflow.common import constants
from services.studio._text_to_workflow.common.schema import ActivitiesGenerationMode, ActivityDefinition, InputProperty
from services.studio._text_to_workflow.common.schema_utils import reorder_properties, reorder_schema_properties
from services.studio._text_to_workflow.utils.activity_utils import remove_dynamic_activity_prefix_from_name
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

LOGGER = AppInsightsLogger()

API_WF_EXPRESSION_LANGUAGE = Literal["jq", "js"]

# Load field descriptions from YAML file
_descriptions_path = pathlib.Path(__file__).parent.absolute() / "schema_descriptions.yaml"
FIELD_DESCRIPTIONS = yaml_load(_descriptions_path)

# Property type definition
ARGUMENT_TYPE = Literal["string", "number", "integer", "boolean", "array", "object"]

# Response type definition
RESPONSE_TYPE = Literal["SUCCESS", "FAILURE"]


# HTTP Method literals
HTTP_METHOD = Literal["GET", "POST", "MERGE", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"]

# Mapping between DAP JsonSchema and Python types
TYPE_MAPPING_TABLE = {
    "string": str,
    "integer": int,
    "number": float,
    "boolean": bool,
    "object": dict[str, Any],
    "array": list[Any],
}

JSON_SCHEMA_EXTRA = {"location": None, "direction": "input"}

# ======================================================================
# SANITIZED BASE MODEL
# Plays well with the openAI structured output API and allow better serialization
# ======================================================================


class SanitizedBaseModel(BaseModel):
    """
    Base model for all schemas with specific configs. Some of the configs are required by the openAI structured output API.

    "extra": "forbid" - Required by the openAI structured output API. Does not allow unspecified fields
    "strict": True - Required by the openAI structured output API. Enforces strict type checking and no coercing.
    "use_enum_values": True - Converts enums to their values. Helpful when serializing with SanitizedModel.model_dump()
    "validate_by_name": True - Validates fields by name.
    "serialize_by_alias": True - Serializes fields by alias. Useful for fields such as `schema_`
    """

    model_config = {
        "extra": "forbid",
        "strict": True,
        "use_enum_values": True,
        "validate_by_name": True,
        "serialize_by_alias": True,
    }

    @classmethod
    def model_parametrized_name(cls, params: tuple) -> str:
        """Remove any invalid characters from the class name, like `[` and `]` for generic models."""
        return params[0].__name__.title().replace(".", "").replace("_", "").replace("-", "").replace("[", "").replace("]", "")


# ======================================================================
# Constants and Literals used to define the activity types
# ======================================================================


SEQUENCE_ACTIVITY_TYPE_LITERAL = Literal["Sequence"]
FOR_EACH_ACTIVITY_TYPE_LITERAL = Literal["ForEach"]
IF_ACTIVITY_TYPE_LITERAL = Literal["If"]
HTTP_ACTIVITY_TYPE_LITERAL = Literal["HttpRequest"]
JS_INVOKE_ACTIVITY_TYPE_LITERAL = Literal["JsInvoke"]
RESPONSE_ACTIVITY_TYPE_LITERAL = Literal["Response"]
TRY_CATCH_ACTIVITY_TYPE_LITERAL = Literal["TryCatch"]
DO_WHILE_ACTIVITY_TYPE_LITERAL = Literal["DoWhile"]
BREAK_ACTIVITY_TYPE_LITERAL = Literal["Break"]

SEQUENCE_ACTIVITY_TYPE = "Sequence"
FOR_EACH_ACTIVITY_TYPE = "ForEach"
IF_ACTIVITY_TYPE = "If"
HTTP_ACTIVITY_TYPE = "HttpRequest"
JS_INVOKE_ACTIVITY_TYPE = "JsInvoke"
RESPONSE_ACTIVITY_TYPE = "Response"
TRY_CATCH_ACTIVITY_TYPE = "TryCatch"
DO_WHILE_ACTIVITY_TYPE = "DoWhile"
BREAK_ACTIVITY_TYPE = "Break"

NON_DYNAMIC_ACTIVITY_TYPES = set(
    [
        SEQUENCE_ACTIVITY_TYPE,
        FOR_EACH_ACTIVITY_TYPE,
        IF_ACTIVITY_TYPE,
        HTTP_ACTIVITY_TYPE,
        JS_INVOKE_ACTIVITY_TYPE,
        RESPONSE_ACTIVITY_TYPE,
        TRY_CATCH_ACTIVITY_TYPE,
        DO_WHILE_ACTIVITY_TYPE,
        BREAK_ACTIVITY_TYPE,
    ]
)

# ======================================================================
# STANDARD FUNCTIONALITY BLOCKS
# Used as components in specific activities or API workflows in general
# ======================================================================


class For(SanitizedBaseModel):
    each: str = Field(description=FIELD_DESCRIPTIONS["for_each"], json_schema_extra=JSON_SCHEMA_EXTRA)
    in_: str = Field(alias="in", description=FIELD_DESCRIPTIONS["for_in"], json_schema_extra=JSON_SCHEMA_EXTRA)
    at: str = Field(description=FIELD_DESCRIPTIONS["for_at"], json_schema_extra=JSON_SCHEMA_EXTRA)


# ======================================================================
# ACTIVITY DEFINITIONS
# ======================================================================


# Base activity properties that all activities share
class BaseWorkflowActivity(SanitizedBaseModel):
    thought: str = Field(description=FIELD_DESCRIPTIONS["base_workflow_activity_thought"])
    id: str = Field(description=FIELD_DESCRIPTIONS["base_workflow_activity_id"])

    # TO DO: implement some custom logic on the Pydantic models to avoid crashing the deserialization if deserializing a single activity is not possible
    # We are using extra="ignore" - to avoid Pydantic from complaining about unknown fields OR if missaligned schema properties are causing issues
    model_config = ConfigDict(populate_by_name=True, validate_assignment=True, extra="ignore", json_schema_extra=reorder_schema_properties)

    def get_activity_name(self) -> str:
        if hasattr(self, "activity"):
            return self.activity  # type: ignore

        raise ValueError("Activity name not found")

    # Due to inheritance, we need to reorder the properties
    @model_serializer(when_used="always", mode="wrap")
    def serialize_model(self, handler, info):
        # Get the original serialized data from the default serializer
        # due to a bug in Pydantic, we can get an exception here, which can be ignored - https://github.com/pydantic/pydantic/issues/6830
        data: dict = handler(self, info)  # @IgnoreException

        # Reorder the data using the common helper function
        return reorder_properties(data)

    def model_remove_configuration(self) -> None:
        pass

    def model_remove_children(self) -> None:
        pass

    @classmethod
    def model_configurable_properties(cls) -> list[str]:
        return []


# HTTP Request specific model
class HTTPCallParameters(BaseModel):
    """Parameters for an HTTP call"""

    method: HTTP_METHOD = Field(description=FIELD_DESCRIPTIONS["http_call_parameters_method"], json_schema_extra=JSON_SCHEMA_EXTRA)
    endpoint: str = Field(description=FIELD_DESCRIPTIONS["http_call_parameters_endpoint"], json_schema_extra=JSON_SCHEMA_EXTRA)
    headers: Optional[str] = Field(default=None, description=FIELD_DESCRIPTIONS["http_call_parameters_headers"], json_schema_extra=JSON_SCHEMA_EXTRA)
    body: Optional[str] = Field(default=None, description=FIELD_DESCRIPTIONS["http_call_parameters_body"], json_schema_extra=JSON_SCHEMA_EXTRA)


class IntegrationHTTPCallParameters(BaseModel):
    """Parameters for an dynamic HTTP call"""

    method: HTTP_METHOD = Field(description=FIELD_DESCRIPTIONS["http_call_parameters_method"], json_schema_extra=JSON_SCHEMA_EXTRA)
    url: Optional[str] = Field(description=FIELD_DESCRIPTIONS["integration_http_call_parameters_url"], json_schema_extra=JSON_SCHEMA_EXTRA)
    headers: Optional[str] = Field(default=None, description=FIELD_DESCRIPTIONS["http_call_parameters_headers"], json_schema_extra=JSON_SCHEMA_EXTRA)
    body: Optional[str] = Field(default=None, description=FIELD_DESCRIPTIONS["http_call_parameters_body"], json_schema_extra=JSON_SCHEMA_EXTRA)
    query: Optional[str] = Field(default=None, description=FIELD_DESCRIPTIONS["integration_http_call_parameters_query"], json_schema_extra=JSON_SCHEMA_EXTRA)


# Generic HTTP request activity (NON-DAP)
class HttpRequest(BaseWorkflowActivity):
    """Represents a generic http request activity"""

    activity: HTTP_ACTIVITY_TYPE_LITERAL = Field(description=FIELD_DESCRIPTIONS["http_request_activity_type"])
    # Using with_ to avoid keyword conflict
    with_: HTTPCallParameters = Field(alias="with", description=FIELD_DESCRIPTIONS["http_request_activity_with"])

    def model_remove_configuration(self) -> None:
        self.with_.method = "GET"
        self.with_.endpoint = ""
        self.with_.headers = ""
        self.with_.body = ""

    def model_remove_children(self) -> None:
        pass

    @classmethod
    def model_configurable_json_schema(cls) -> dict:
        http_request_schema = cls.model_json_schema()
        http_call_parameters_schema = http_request_schema["$defs"]["HTTPCallParameters"]
        props = cls.model_configurable_properties()
        return {
            "description": http_request_schema["description"],
            "properties": {prop: http_call_parameters_schema["properties"][prop.split(".").pop()] for prop in props},
            "required": http_call_parameters_schema["required"],
        }

    @classmethod
    def model_configurable_properties(cls) -> list[str]:
        return ["with.method", "with.endpoint", "with.headers", "with.body"]


# Base class for connector integration activities both static and dynamic
class BaseConnectorIntegration(BaseWorkflowActivity):
    """Represents a connector integration operation whose schema is provided by the user"""

    activity: str = Field(description=FIELD_DESCRIPTIONS["connector_activity_type"])


# Static Connector activity (DAP)
class ConnectorIntegration(BaseConnectorIntegration):
    """Represents a connector integration operation"""

    # Using with_ to avoid keyword conflict
    with_: dict[str, str] = Field(alias="with", description=FIELD_DESCRIPTIONS["connector_activity_with"])

    def model_remove_configuration(self) -> None:
        self.with_ = {}

    def model_remove_children(self) -> None:
        pass

    @classmethod
    def model_configurable_json_schema(cls) -> dict:
        return {}

    @classmethod
    def model_configurable_properties(cls) -> list[str]:
        return []


class JsInvoke(BaseWorkflowActivity):
    """Contains a javascript code block to be executed when workflow invokes the current activity"""

    activity: JS_INVOKE_ACTIVITY_TYPE_LITERAL
    code: str = Field(description=FIELD_DESCRIPTIONS["js_invoke_activity_run"], json_schema_extra=JSON_SCHEMA_EXTRA)

    def model_remove_configuration(self) -> None:
        self.code = ""

    def model_remove_children(self) -> None:
        pass

    @classmethod
    def model_configurable_json_schema(cls) -> dict:
        schema = cls.model_json_schema()
        props = cls.model_configurable_properties()
        return {
            "description": schema["description"],
            "properties": {prop: schema["properties"][prop] for prop in props},
            "required": props,
        }

    @classmethod
    def model_configurable_properties(cls) -> list[str]:
        return ["code"]


class Response(BaseWorkflowActivity):
    """Represents a generic response activity"""

    activity: RESPONSE_ACTIVITY_TYPE_LITERAL = Field(description=FIELD_DESCRIPTIONS["response_activity"])
    type: RESPONSE_TYPE = Field(description=FIELD_DESCRIPTIONS["response_activity_type"], json_schema_extra=JSON_SCHEMA_EXTRA)
    response_message: str = Field(description=FIELD_DESCRIPTIONS["response_activity_response_message"], json_schema_extra=JSON_SCHEMA_EXTRA)

    def model_remove_configuration(self) -> None:
        self.type = "SUCCESS"
        self.response_message = ""

    def model_remove_children(self) -> None:
        pass

    @classmethod
    def model_configurable_json_schema(cls) -> dict:
        schema = cls.model_json_schema()
        props = cls.model_configurable_properties()
        return {
            "description": schema["description"],
            "properties": {prop: schema["properties"][prop] for prop in props},
            "required": props,
        }

    @classmethod
    def model_configurable_properties(cls) -> list[str]:
        return ["type", "response_message"]


# ======================================================================
# SCOPE ACTIVITY DEFINITIONS
# ======================================================================


# Base class for both dynamic and static sequence activities
class BaseSequence(BaseWorkflowActivity):
    activity: SEQUENCE_ACTIVITY_TYPE_LITERAL


# Static Sequence activity
class Sequence(BaseSequence):
    """Represents a sequence of activities to be executed in order"""

    do: list["BASE_API_ACTIVITIES"] = Field(description=FIELD_DESCRIPTIONS["sequence_activity_do"])

    def model_remove_configuration(self) -> None:
        pass

    def model_remove_children(self) -> None:
        self.do = []

    @classmethod
    def model_configurable_json_schema(cls) -> dict:
        schema = cls.model_json_schema()["$defs"]["Sequence"]
        props = cls.model_configurable_properties()
        return {
            "description": schema["description"],
            "properties": {prop: schema["properties"][prop] for prop in props},
            "required": props,
        }

    @classmethod
    def model_configurable_properties(cls) -> list[str]:
        return []


# TO DO: see if following a schema closer to Serverless Workflows 1.0.0 is a good idea - to try once v 1.0.0 gains more traction
class BaseIf(BaseWorkflowActivity):
    activity: IF_ACTIVITY_TYPE_LITERAL
    condition: str = Field(description=FIELD_DESCRIPTIONS["if_condition"], json_schema_extra=JSON_SCHEMA_EXTRA)


class If(BaseIf):
    """Represents a conditional activity that executes one of two branches based on a condition"""

    then: list["BASE_API_ACTIVITIES"] = Field(default_factory=list, description=FIELD_DESCRIPTIONS["if_activity_true_sequence"])
    # default is an empty list to avoid Pydantic from complaining when the else branch is not present
    else_: list["BASE_API_ACTIVITIES"] = Field(default_factory=list, alias="else", description=FIELD_DESCRIPTIONS["if_activity_false_sequence"])

    def model_remove_configuration(self) -> None:
        self.condition = ""

    def model_remove_children(self) -> None:
        self.then = []
        self.else_ = []

    @classmethod
    def model_configurable_json_schema(cls) -> dict:
        schema = cls.model_json_schema()["$defs"]["If"]
        props = cls.model_configurable_properties()
        return {
            "description": schema["description"],
            "properties": {prop: schema["properties"][prop] for prop in props},
            "required": props,
        }

    @classmethod
    def model_configurable_properties(cls) -> list[str]:
        return ["condition"]


# Base class for both dynamic and static ForEach activities
class BaseForEach(BaseWorkflowActivity):
    activity: FOR_EACH_ACTIVITY_TYPE_LITERAL
    # Using for_ to avoid keyword conflict
    for_: For = Field(alias="for", description=FIELD_DESCRIPTIONS["for_each_activity_for"])


# Static ForEach activity
class ForEach(BaseForEach):
    """Represents a loop that iterates over a list of items and executes a set of activities for each one"""

    # we will represent only the BASE_API_ACTIVITY_TYPES in the scope itself
    do: list["BASE_API_ACTIVITIES"] = Field(description=FIELD_DESCRIPTIONS["for_each_activity_do"])

    def model_remove_configuration(self) -> None:
        self.for_.each = ""
        self.for_.in_ = ""
        self.for_.at = ""

    def model_remove_children(self) -> None:
        self.do = []

    @classmethod
    def model_configurable_json_schema(cls) -> dict:
        defs = cls.model_json_schema()["$defs"]
        for_each_schema = defs["ForEach"]
        for_schema = defs["For"]
        props = cls.model_configurable_properties()
        return {
            "description": for_each_schema["description"],
            "properties": {prop: for_schema["properties"][prop.split(".").pop()] for prop in props},
            "required": props,
        }

    @classmethod
    def model_configurable_properties(cls) -> list[str]:
        return ["for.each", "for.in", "for.at"]


class BaseCatch(SanitizedBaseModel):
    as_: str = Field(alias="as", description=FIELD_DESCRIPTIONS["try_catch_activity_error"])


class Catch(BaseCatch):
    do: list["BASE_API_ACTIVITIES"] = Field(description=FIELD_DESCRIPTIONS["try_catch_activity_catch_sequence"])


class BaseTryCatch(BaseWorkflowActivity):
    activity: TRY_CATCH_ACTIVITY_TYPE_LITERAL


class TryCatch(BaseTryCatch):
    """Represents a try-catch activity that executes a set of activities and catches exceptions"""

    try_: list["BASE_API_ACTIVITIES"] = Field(alias="try", description=FIELD_DESCRIPTIONS["try_catch_activity_try_sequence"])

    catch: Catch = Field(description=FIELD_DESCRIPTIONS["try_catch_activity_catch"])

    def model_remove_configuration(self) -> None:
        self.catch.as_ = "error"

    def model_remove_children(self) -> None:
        self.try_ = []
        self.catch.do = []

    @classmethod
    def model_configurable_json_schema(cls) -> dict:
        defs = cls.model_json_schema()["$defs"]
        try_catch_schema = defs["TryCatch"]
        catch_schema = defs["Catch"]
        props = cls.model_configurable_properties()
        return {
            "description": try_catch_schema["description"],
            "properties": {prop: catch_schema["properties"][prop.split(".").pop()] for prop in props},
            "required": props,
        }

    @classmethod
    def model_configurable_properties(cls) -> list[str]:
        return ["catch.as"]


class BaseDoWhile(BaseWorkflowActivity):
    activity: DO_WHILE_ACTIVITY_TYPE_LITERAL
    condition: str = Field(description=FIELD_DESCRIPTIONS["do_while_activity_condition"], json_schema_extra=JSON_SCHEMA_EXTRA)
    limit: int | None = Field(default=None, description=FIELD_DESCRIPTIONS["do_while_activity_limit"], json_schema_extra=JSON_SCHEMA_EXTRA)


class DoWhile(BaseDoWhile):
    do: list["BASE_API_ACTIVITIES"] = Field(default_factory=list, description=FIELD_DESCRIPTIONS["do_while_activity_do"])

    def model_remove_configuration(self) -> None:
        self.condition = ""
        self.limit = None

    def model_remove_children(self) -> None:
        self.do = []

    @classmethod
    def model_configurable_json_schema(cls) -> dict:
        schema = cls.model_json_schema()["$defs"]["DoWhile"]
        props = cls.model_configurable_properties()
        return {
            "description": schema["description"],
            "properties": {prop: schema["properties"][prop] for prop in props},
            "required": props,
        }

    @classmethod
    def model_configurable_properties(cls) -> list[str]:
        return ["condition", "limit"]


class Break(BaseWorkflowActivity):
    activity: BREAK_ACTIVITY_TYPE_LITERAL

    def model_configurable_json_schema(self) -> dict:
        return {"description": "Break", "properties": {}, "required": []}


BASE_API_ACTIVITIES = Union[HttpRequest, Sequence, ForEach, JsInvoke, If, ConnectorIntegration, Response, TryCatch, DoWhile, Break]


# ======================================================================
# API Workflow
# ======================================================================


class ApiWorkflow(SanitizedBaseModel):
    input: str = Field(description=FIELD_DESCRIPTIONS["api_workflow_input_arguments"])
    root: Sequence = Field(description=FIELD_DESCRIPTIONS["api_workflow_root"])

    def yaml(self) -> str:
        return yaml_dump(rewrite_code_as_literal(self.model_dump(exclude_none=True, by_alias=True))).strip()


class ApiWorkflowForActivityEdit(SanitizedBaseModel):
    input: dict = Field(description=FIELD_DESCRIPTIONS["api_workflow_input_arguments_for_activity_edit"])
    root: Sequence = Field(description=FIELD_DESCRIPTIONS["api_workflow_root"])


class ApiWorkflowDataPoint(SanitizedBaseModel):
    model_config = {
        "extra": "allow",
    }

    mode: ActivitiesGenerationMode  # TODO: we should try to remove this field
    query: str
    language: API_WF_EXPRESSION_LANGUAGE
    plan: str  # planning details - might be useful to use as thinking tokens or for the activity retrieval service
    used_activities: list[str]  # activities that are actually used to build the workflow for the current dataset
    existing_workflow: ApiWorkflow | None = None
    solution_workflow: ApiWorkflow
    solution_workflow_query: str | None = None  # query that best describes the solution workflow
    solution_workflow_schemas: dict | None = None
    focused_activity_id: str | None = None
    # true if only new activities were added to the solution workflow, no replacements/ deletions were done
    contains_no_replacements: bool = Field(default=False)

    # optional params for datapoint, benign unless used explicitly
    seed: int | None = None


def build_custom_workflow_model(
    activity_defs: list[ActivityDefinition],
    non_default_properties_limit,
) -> type[BaseModel]:
    """Builds a custom workflow model that can be used to create a specific JSON schema for a given API workflow generation task."""

    # TO DO: we should probably cache these - so we don't have to build them every time as it could impact the performance
    custom_call_activities = []
    for activity in activity_defs:
        # remove the Dynamic Activity Prefix from the activity name only if it appears at the start
        model_name = remove_dynamic_activity_prefix_from_name(activity["fullClassName"])

        # create a dynamic subclass of DAP HTTP Activity
        if activity["fullActivityName"] == constants.DAP_HTTP_ACTIVITY_NAME:
            custom_call_activity = create_model(
                model_name,
                activity=(
                    str,
                    Field(
                        description=FIELD_DESCRIPTIONS["custom_activity_type"].format(placeholder=model_name),
                        json_schema_extra={"const": model_name, "enum": [model_name], "title": "activity", "type": "string"},
                    ),
                ),
                with_=(IntegrationHTTPCallParameters, Field(alias="with", description=FIELD_DESCRIPTIONS["dynamic_http_call_parameters_with"])),
                __base__=BaseConnectorIntegration,
                __doc__=f"Send a generic HTTP request to the {activity['category']} API, must be used whenever you want to perform something in the {activity['category']} API, but a more specific {activity['category']} Integration Operation is not available for the desired action in {activity['category']}.",
            )
            custom_call_activities.append(custom_call_activity)
            continue

        # Create a dynamic subclass of Curated DAP Activity
        if activity.get("inputPropertiesJsonSchema") is None:
            # if this happens, it likely means that a state connection was used when populating the EmbeddingsDB
            LOGGER.warning(f"Input schema not found for {activity['fullClassName']}.")
            # we still add the activity to the custom schema, but we use a generic dict to define its contents
            with_model = dict[str, str]
        else:
            # get the activity definition from the list of activity definitions
            with_model = _build_model_from_DAP_schema(activity.get("inputPropertiesJsonSchema") or [], f"{model_name}_with", non_default_properties_limit)

        custom_call_activity = create_model(
            model_name,
            activity=(
                str,
                Field(
                    description=FIELD_DESCRIPTIONS["custom_activity_type"].format(placeholder=model_name),
                    json_schema_extra={"const": model_name, "enum": [model_name], "title": "activity", "type": "string"},
                ),
            ),
            with_=(with_model, Field(alias="with", description=FIELD_DESCRIPTIONS["custom_activity_with"])),
            __base__=BaseConnectorIntegration,
            __doc__=activity["description"],
        )
        custom_call_activities.append(custom_call_activity)

    # Create a Union type with all custom call activities and standard activities
    # WARNING: to get around pydantic limiations, we need the ref names to be different than the class names
    ForEachRef = ForwardRef("ForEachActivity")
    SequenceRef = ForwardRef("SequenceActivity")
    IfRef = ForwardRef("IfActivity")
    TryCatchRef = ForwardRef("TryCatchActivity")
    DoWhileRef = ForwardRef("DoWhileActivity")
    # Combine all activities with their tags
    union_activities = Union[tuple([Response, HttpRequest, JsInvoke, SequenceRef, ForEachRef, IfRef, TryCatchRef, DoWhileRef, Break] + custom_call_activities)]  # type: ignore

    # rebuild a type of Sequence activity that accepts our newly created activity models
    sequenceActivity = create_model(
        SEQUENCE_ACTIVITY_TYPE,
        do=(list[union_activities], Field(description=FIELD_DESCRIPTIONS["sequence_activity_do"])),
        __base__=BaseSequence,
    )

    # rebuild a type of ForEach activity that accepts our newly created activity models
    foreachActivity: type[BaseForEach] = create_model(
        FOR_EACH_ACTIVITY_TYPE,
        do=(list[union_activities], Field(description=FIELD_DESCRIPTIONS["for_each_activity_do"])),
        __base__=BaseForEach,
    )

    ifActivity = create_model(
        IF_ACTIVITY_TYPE,
        then=(list[union_activities], Field(description=FIELD_DESCRIPTIONS["if_activity_true_sequence"])),
        else_=(list[union_activities], Field(alias="else", description=FIELD_DESCRIPTIONS["if_activity_false_sequence"])),
        __base__=BaseIf,
    )

    # Create a Catch model that matches the structure in the TryCatch class
    catchModel = create_model(
        "Catch",
        as_=(str, Field(alias="as", description=FIELD_DESCRIPTIONS["try_catch_activity_error"])),
        do=(list[union_activities], Field(description=FIELD_DESCRIPTIONS["try_catch_activity_catch_sequence"])),
        __base__=BaseCatch,
    )

    tryCatchActivity = create_model(
        TRY_CATCH_ACTIVITY_TYPE,
        try_=(list[union_activities], Field(alias="try", description=FIELD_DESCRIPTIONS["try_catch_activity_try_sequence"])),
        catch=(catchModel, Field(description=FIELD_DESCRIPTIONS["try_catch_activity_catch"])),
        __base__=BaseTryCatch,
    )

    doWhileActivity = create_model(
        DO_WHILE_ACTIVITY_TYPE,
        do=(list[union_activities], Field(description=FIELD_DESCRIPTIONS["do_while_activity_do"])),
        __base__=BaseDoWhile,
    )

    api_workflow = create_model(
        "ApiWorkflow",
        input=(str, Field(description=FIELD_DESCRIPTIONS["api_workflow_input_arguments"])),
        root=(sequenceActivity, Field(description=FIELD_DESCRIPTIONS["api_workflow_root"])),
    )

    # Rebuild the model to replace the lookups with the actual activity models
    api_workflow.model_rebuild(
        _types_namespace={
            "CatchBlock": catchModel,
            "ForEachActivity": foreachActivity,
            "SequenceActivity": sequenceActivity,
            "IfActivity": ifActivity,
            "TryCatchActivity": tryCatchActivity,
            "DoWhileActivity": doWhileActivity,
        }
    )
    return api_workflow


def _build_model_from_DAP_schema(input_properties: list[InputProperty], model_name: str, non_default_properties_limit: int):
    """
    Builds a Pydantic model from a list of input properties.
    The model is used to create a specific JSON schema for a given API workflow generation task. Example:
    """
    # Split properties into required/visible and optional
    required_visible_props = []
    optional_props = []
    for prop in input_properties:
        if prop["required"] or prop.get("visibleByDefault", False):
            required_visible_props.append(prop)
        else:
            optional_props.append(prop)

    # Take only as many optional properties as we need to reach the limit
    optional_props_to_use = optional_props[: max(0, non_default_properties_limit - len(required_visible_props))]

    # Combine the properties and build the fields
    fields = {}
    for prop in required_visible_props + optional_props_to_use:
        prop_type = TYPE_MAPPING_TABLE.get(prop["type"], Any)
        default = ... if prop["required"] else None
        fields[prop["schema_name"]] = (prop_type, Field(default=default, description=prop.get("description", None)))

    return create_model(model_name, **fields)


class DiscoveredApi(BaseModel):
    api_base_url: str = Field(description="Base URL of the API service")
    api_page: str = Field(description="Page where the API schema was discovered")
    api_subpage_urls: list[str] = Field(
        description="List of URLs of the subpages of the official documentation page. You MUST visit all of them to compute the API schema."
    )
    usage_example: str = Field(description="Example of how to use the API endpoint")
    api_name: str = Field(description="Name of the API service")
    request_type: HTTP_METHOD = Field(description="Type of request to be made to the API")
    swagger_schema_string: str = Field(description="OpenAPI/Swagger schema definition in string format. Must be on a single line.")


class PerplexitySearchOutputConfig(BaseModel):
    plan: list[str] = Field(description="Plan of the search operation for discovering the API schema")
    provider: str = Field(description="Provider of the API schema")
    discovered_apis: list[DiscoveredApi] = Field(description="List of discovered API with the requested information", min_length=1)


"""
Example of a DAP schema with 2 required properties 'start' and 'end':
    ```json
    {
        "properties": {
            "start": {
                "title": "Start",
                    "type": "string"
                },
                "end": {
                    "title": "End",
                    "type": "string"
                },
                "employeeId": {
                    "default": null,
                    "title": "Employeeid",
                    "type": "string"
                },
                "type": {
                    "default": null,
                    "title": "Type",
                    "type": "string"
            },
            "timeOffStatus": {
                "default": null,
                "title": "Timeoffstatus",
                "type": "string"
            }
        },
        "required": [
            "start",
            "end"
        ],
        "title": "UiPath.IntegrationService.Activities.Runtime.Activities.BambooHRGet_Time_off_Requests_with",
        "type": "object"
    },
```
"""
