import json
import re
import typing
from collections import deque

import jq
import yaml
from pydantic import BaseModel

from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.constants import ANY_OUTPUT_PATH_REGEX, STATIC_PROPERTIES
from services.studio._text_to_workflow.common.api_workflow.js_expressions_helper import JavaScriptExpressionsHelper
from services.studio._text_to_workflow.common.api_workflow.post_generation_schema import (
    POST_GENERATION_ACTIVITIES,
    ConnectorIntegrationMetadata,
    ConnectorIntegrationWithMetadata,
    ConnectorRequestParameters,
    PostGenDoWhile,
    PostGenForEach,
    PostGenIf,
    PostGenSequence,
    PostGenTryCatch,
)
from services.studio._text_to_workflow.common.api_workflow.schema import (
    API_WF_EXPRESSION_LANGUAGE,
    BASE_API_ACTIVITIES,
    BaseWorkflowActivity,
    ConnectorIntegration,
    Do<PERSON><PERSON>e,
    For<PERSON>ach,
    HTTPCallParameters,
    HttpRequest,
    If,
    JsInvoke,
    Response,
    Sequence,
    TryCatch,
)
from services.studio._text_to_workflow.common.constants import DAP_HTTP_ACTIVITY_NAME, DAP_HTTP_METHOD_PARAM_NAME, DAP_NAMESPACE
from services.studio._text_to_workflow.common.schema import ActivityDefinition, Connection, InputProperty, InputPropertyLocation, OutputType
from services.studio._text_to_workflow.expression_generation.expression_generation_endpoint import GENERATION_TASK_CONFIGURATIONS as EXPRESSION_GENERATION_TASKS
from services.studio._text_to_workflow.expression_generation.expression_generation_jq_helper import JqExpressionsHelper
from services.studio._text_to_workflow.expression_generation.expression_generation_schema import ExpressionGenerationFixRequest, SourceType
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import (
    ActivityError,
    InvalidConnectionError,
    InvalidExpressionError,
    MissingRequiredFieldError,
    PathSegment,
    WorkflowProcessingErrorType,
)

LOGGER = AppInsightsLogger()

# TO DO: enhance this and, if possible, support the inner properties of these objects
AVAILABLE_VARIABLES = {"context", "workflow"}


# Properties that are either variable references or constants that should not be escaped with ${....}:
NON_ESCAPABLE_PROPERTIES: dict[type[BASE_API_ACTIVITIES], list[str]] = {
    ForEach: ["for_.at", "for_.in_", "for_.each"],
    HttpRequest: ["with_.method"],
    JsInvoke: ["code"],
    TryCatch: ["catch.as_"],
    DoWhile: ["limit"],
}

# Regex to check if expression starts with .content.<output_type> or ?.content.<output_type>
CONTENT_OUTPUT_TYPE_START_REGEX = re.compile(
    r"^(?:\??\.content\??\.)([a-zA-Z0-9_]+)"
)  # matches .content.<output_type> or ?.content?.<output_type> at the start

# TEMPORARY: remove after SW will support customizable export properties
IDENTIFIER_SANITIZATION_SUBSTITUTIONS = {
    ":::": "_sub_",
    "[*]": "_array",
    "::": "_sub_",
    ".": "_sub_",
}
NON_SANITIZABLE_CHARACTERS = {"-", "+", "$"}


class PostGenerationApiWfParser:
    """Handles validation and processing of API workflow activities and expressions.
    Should be instantiated every time we process a new API workflow, as it holds state."""

    def __init__(
        self,
        activity_retriever: APIActivitiesRetriever,
        expression_language: API_WF_EXPRESSION_LANGUAGE,
        config_map: dict[str, dict],
        connections_by_key: dict[str, Connection],
        clear_invalid_expressions: bool = True,
        output_type_definitions: str | None = None,
    ):
        self.activity_retriever = activity_retriever
        self.config_map = config_map
        self.expression_language = expression_language
        self.clear_invalid_expressions = clear_invalid_expressions
        self.connections_by_key = connections_by_key
        self.variables = deque()  # Use deque as a stack
        self.activity_ids = set()
        self.activity_output_types_dict: dict[str, str] = {}
        self.hallucinated_activity_ids = set()
        self.errors = []
        self.post_generation_activity_types = typing.get_args(POST_GENERATION_ACTIVITIES)
        self.output_type_definitions = output_type_definitions

        # TEMPORARY: remove after SW will support customizable export properties
        self.export_properties_mapping: dict[str, list[tuple[str, str]]] = {}

    def _validate_expression(
        self,
        expression: str,
        current_path: list[PathSegment],
        activity_identifier: str,
        param_name: str,
        activity: BASE_API_ACTIVITIES,
        is_code_expression: bool,
    ) -> tuple[bool, list[InvalidExpressionError]]:
        """
        Validates a JQ or JS expression by attempting to compile it with mock variables.
        Returns whether the expression is valid.
        """
        is_valid = True
        errors: list[InvalidExpressionError] = []

        if is_code_expression:
            # JS Invoke code does not need to be decoded nor trimmed
            trimmed_expression = expression[2:-1] if expression.startswith("${") and expression.endswith("}") else expression
        else:
            # first decode the expression to handle escape sequences
            # decoded_expression = codecs.decode(expression, "unicode_escape")

            # Trim the expression if it starts with ${ and ends with }
            # TO DO: we might have to replace this with something more robust, but I couldn't find a situation where this was not enough
            trimmed_expression = expression[2:-1] if expression.startswith("${") and expression.endswith("}") else expression

        if is_code_expression:
            # we validated the code in the JsInvoke activity separately
            context_variables = {"$" + v for v in AVAILABLE_VARIABLES} | set(self.variables)
            error, evaluated_code = JavaScriptExpressionsHelper.validate_js_invoke(trimmed_expression, context_variables)
            if error:
                is_valid = False
                errors.append(
                    InvalidExpressionError(
                        workflow_path=current_path,
                        activity_identifier=activity_identifier,
                        param_name=param_name,
                        expression=expression,
                        error=error,
                        evaluated_expression=evaluated_code,
                    )
                )

        elif self.expression_language == "jq":
            context_variables = AVAILABLE_VARIABLES | set(self.variables)
            try:
                # Mock variables and compile the expression
                to_check = JqExpressionsHelper.create_variables_pipe(context_variables) + " | " + trimmed_expression
                # exceptions here are expected, so we want to ignore them for the most part
                jq.compile(to_check)  # @IgnoreException
            except ValueError as e:
                is_valid = False
                errors.append(
                    InvalidExpressionError(
                        workflow_path=current_path,
                        activity_identifier=activity_identifier,
                        param_name=param_name,
                        expression=expression,
                        evaluated_expression=trimmed_expression,
                        error=";".join(e.args),
                    )
                )

        elif self.expression_language == "js":
            context_variables = {"$" + v for v in AVAILABLE_VARIABLES} | set(self.variables)
            error, evaluated_code = JavaScriptExpressionsHelper.validate_js_expression(trimmed_expression, context_variables)
            if error:
                is_valid = False
                errors.append(
                    InvalidExpressionError(
                        workflow_path=current_path,
                        activity_identifier=activity_identifier,
                        param_name=param_name,
                        expression=expression,
                        evaluated_expression=evaluated_code,
                        error=error,
                    )
                )
        else:
            raise ValueError(f"Unsupported expression language: {self.expression_language}")

        # Find all context output path references and validate them
        path_matches = ANY_OUTPUT_PATH_REGEX.finditer(expression)
        for match in path_matches:
            id_segment = match.group(1)
            if id_segment in self.hallucinated_activity_ids:
                # references to hallucinated activity ids make the expression invalid
                is_valid = False
                continue

            if id_segment in self.activity_output_types_dict:
                # sometimes the DAP output type is wrongly added to an expression accessing the output - we must validate for this
                # $context.outputs.issue_1.content.github_issue.issue_name - WRONG
                # $context.outputs.issue_1.content.issue_name - CORRECT
                output_type = self.activity_output_types_dict[id_segment]

                # Check if the expression continues with .content.<output_type> or ?.content.<output_type> after the matched group
                remaining_expression = expression[match.end() :]
                output_type_match = CONTENT_OUTPUT_TYPE_START_REGEX.match(remaining_expression)
                if output_type_match and output_type_match.group(1) == output_type:
                    is_valid = False
                    errors.append(
                        InvalidExpressionError(
                            workflow_path=current_path,
                            activity_identifier=activity_identifier,
                            param_name=param_name,
                            expression=expression,
                            evaluated_expression=remaining_expression,
                            error=f"Invalid reference to output type '{output_type}' in path for activity '{id_segment}'.",
                        )
                    )

            if id_segment not in self.activity_ids:
                is_valid = False
                # TODO: Re-enable when Studio Web doesn't screw up the activity ids
                # self.errors.append(
                #     InvalidExpressionError(
                #         workflow_path=current_path,
                #         activity_identifier=activity_identifier,
                #         param_name=param_name,
                #         expression=expression,
                #         error=f"{id_segment} references an invalid activity id.",
                #     )
                # )
        return is_valid, errors

    def _escape_constant_expression(self, value: str) -> str:
        """
        Escapes a value as a constant expression with ${}.
        """
        if value.startswith("${"):
            return value
        elif (value.startswith('"') and value.endswith('"')) or (value.startswith("'") and value.endswith("'")):
            # It's a quoted string - check for nested quotes which would make it invalid
            inner_content = value[1:-1]
            # Format with double quotes
            return '${"' + inner_content + '"}'
        if re.match(r"^[+-]?(?:\d+\.?\d*|\.\d+)$|^[1-9]\d*$|^0$|^(true|false)$", value, re.IGNORECASE):
            # it's a number - format it as such or a boolean
            return "${" + value + "}"
        else:
            # it's a string - format it with quotes
            return '${"' + value + '"}'

    async def _process_expression(
        self,
        value: str,
        current_path: list[PathSegment],
        activity_identifier: str,
        param_name: str,
        activity: BASE_API_ACTIVITIES,
        is_model_retry_permitted: bool = True,
    ) -> str:
        # check if the property is non-escapable
        if type(activity) not in NON_ESCAPABLE_PROPERTIES or param_name not in NON_ESCAPABLE_PROPERTIES[type(activity)]:
            # property might contain a constant expression, so we escape it if it is the case
            value = self._escape_constant_expression(value)

            # If the value is empty due to an error in escaping, return it directly
            if not value:
                return value

        is_code_expression = isinstance(activity, JsInvoke) and param_name == "code"

        is_valid, errors = self._validate_expression(
            expression=value,
            current_path=current_path,
            activity_identifier=activity_identifier,
            param_name=param_name,
            activity=activity,
            is_code_expression=is_code_expression,
        )

        if self.clear_invalid_expressions and not is_valid:
            if is_model_retry_permitted:
                try:
                    # Try to fix the expression with the LLM
                    value, is_valid, errors = await self.try_fix_expression(
                        value, current_path, activity_identifier, param_name, activity, is_code_expression, errors
                    )
                    if not is_valid:
                        # Try again with the LLM
                        value, is_valid, errors = await self.try_fix_expression(
                            value, current_path, activity_identifier, param_name, activity, is_code_expression, errors
                        )
                        if not is_valid:
                            self.errors.extend(errors)
                            return ""
                except Exception as e:
                    # If the LLM fails to fix the expression, we return the original expression
                    LOGGER.error(f"Error fixing expression within API workflow: {str(e)}")
                    self.errors.extend(errors)
                    return ""
            else:
                self.errors.extend(errors)
                return ""

        # TEMPORARY: remove after SW will support customizable export properties
        return self.get_expression_with_id_replacements(value)

    async def try_fix_expression(self, value, current_path, activity_identifier, param_name, activity, is_code_expression, errors):
        if is_code_expression:
            value = await self._try_fix_code(value, "Error List:\n" + yaml.dump([error.error for error in errors]))
        else:
            value = await self._try_fix_expression(value, "Error List:\n" + yaml.dump([error.error for error in errors]), self.expression_language)
        is_valid, errors = self._validate_expression(
            expression=value,
            current_path=current_path,
            activity_identifier=activity_identifier,
            param_name=param_name,
            activity=activity,
            is_code_expression=is_code_expression,
        )

        return value, is_valid, errors

    async def _validate_activity_expressions(self, activity: BASE_API_ACTIVITIES, current_path: list[PathSegment], is_model_retry_permitted: bool = True):
        """
        Validates all expressions in an activity's properties.
        """
        for field_name in activity.model_fields:
            # Skip static properties
            if field_name in STATIC_PROPERTIES:
                continue

            # Get the field value using getattr
            field_value = getattr(activity, field_name)

            # Skip if the field is an activity or a list of activities, as we handle separately
            if isinstance(field_value, BaseWorkflowActivity) or (
                isinstance(field_value, list) and any(isinstance(x, BaseWorkflowActivity) for x in field_value)
            ):
                continue

            # Handle special cases for 'with'
            if isinstance(field_value, dict):
                for key, value in field_value.items():
                    if isinstance(value, str):
                        field_value[key] = await self._process_expression(
                            value, current_path, activity.id, f"{field_name}.{key}", activity, is_model_retry_permitted
                        )

            # Handle BaseModel fields for cases such as 'for' properties
            if isinstance(field_value, BaseModel):
                for nested_field_name in field_value.model_fields:
                    nested_field_value = getattr(field_value, nested_field_name)
                    if isinstance(nested_field_value, str):
                        setattr(
                            field_value,
                            nested_field_name,
                            await self._process_expression(
                                nested_field_value, current_path, activity.id, f"{field_name}.{nested_field_name}", activity, is_model_retry_permitted
                            ),
                        )

            # check if simple expression
            if isinstance(field_value, str):
                setattr(
                    activity, field_name, await self._process_expression(field_value, current_path, activity.id, field_name, activity, is_model_retry_permitted)
                )

    def _validate_connector_activity_required_fields(self, activity: ConnectorIntegration):
        """
        Validates required fields for a connector activity by checking the activity definition
        and configuration. Adds MissingRequiredFieldError to self.errors for any missing required fields.
        """
        # Get activity definition and config
        full_activity_class_name = DAP_NAMESPACE + "." + activity.activity
        config = self.config_map.get(full_activity_class_name) or {}
        activity_def = self.activity_retriever.get(full_activity_class_name, "activity")

        # Get input properties and check required fields
        input_properties = self._get_input_properties(config, activity_def)

        for prop in input_properties:
            if prop.get("required", False):
                field_name = prop["schema_name"]
                field_value = activity.with_.get(field_name) if field_name in activity.with_ else None

                if field_value is None:
                    self.errors.append(
                        MissingRequiredFieldError(
                            workflow_path=[],  # You can provide the actual path if available
                            activity_identifier=activity.id,
                            field_name=prop["name"],
                        )
                    )

    async def process_activities(
        self,
        activities: list[BASE_API_ACTIVITIES],
        current_path: list[PathSegment] | None = None,
        is_model_retry_permitted: bool = True,
    ) -> list[POST_GENERATION_ACTIVITIES]:
        """
        Recursively validates all activities in a list, including nested activities.
        Also builds a copy of the activities with the metadata needed to display the activity in the SW canvas.
        """
        current_path = current_path or []
        processed_activities: list[POST_GENERATION_ACTIVITIES] = []

        for activity in activities:
            if isinstance(activity, ConnectorIntegration):
                self._validate_connector_activity_required_fields(activity)

            # Validate current activity's expressions
            await self._validate_activity_expressions(activity, current_path, is_model_retry_permitted)

            # Add activity ID to context
            self.activity_ids.add(activity.id)

            # Process nested activities
            if isinstance(activity, (Sequence, ForEach, DoWhile)):
                if isinstance(activity, ForEach) and activity.for_.each:
                    # Add the forEach variable
                    self.variables.append(activity.for_.each)

                if isinstance(activity, ForEach) and activity.for_.at:
                    # Add the forEach variable
                    self.variables.append(activity.for_.at)

                # TEMPORARY: remove after SW will support customizable export properties
                self.replace_activity_id_with_export_property(None, activity)

                do_block_copy = await self.process_activities(activity.do, current_path + [PathSegment(segment_type="property", name="do")])
                processed_activities.append(self.build_post_gen_scope_activity(activity, do_block_copy))

                # Restore variables
                if isinstance(activity, ForEach) and activity.for_.each:
                    self.variables.pop()

                if isinstance(activity, ForEach) and activity.for_.at:
                    self.variables.pop()

                continue

            if isinstance(activity, If):
                # TEMPORARY: remove after SW will support customizable export properties
                self.replace_activity_id_with_export_property(None, activity)

                then_block_copy = await self.process_activities(activity.then, current_path + [PathSegment(segment_type="property", name="then")])
                else_block_copy = await self.process_activities(activity.else_, current_path + [PathSegment(segment_type="property", name="else")])

                # build & append the new if activity
                if_activity_args = activity.model_dump(by_alias=True, exclude={"then", "else"})
                if_activity_args["then"] = then_block_copy
                if_activity_args["else"] = else_block_copy
                processed_activities.append(PostGenIf.model_validate(if_activity_args))
                continue

            if isinstance(activity, TryCatch):
                # Add the catch variable
                if activity.catch.as_:
                    self.variables.append(activity.catch.as_)

                try_block_copy = await self.process_activities(activity.try_, current_path + [PathSegment(segment_type="property", name="try")])
                catch_block_copy = await self.process_activities(activity.catch.do, current_path + [PathSegment(segment_type="property", name="catch.do")])

                # build & append the new try-catch activity
                try_catch_activity_args = activity.model_dump(by_alias=True, exclude={"try", "catch"})

                try_catch_activity_args["try"] = try_block_copy
                try_catch_activity_args["catch"] = {"as": activity.catch.as_, "do": catch_block_copy}
                processed_activities.append(PostGenTryCatch.model_validate(try_catch_activity_args))

                # Restore variables
                if activity.catch.as_:
                    self.variables.pop()
                continue

            if isinstance(activity, ConnectorIntegration):
                # check if the activity exists in the activity retriever
                # TODO: if this logic is not enough, we might want to implement something comparable to the 'try_fix_activity_name' method in the ActivityRetriever
                full_activity_class_name = DAP_NAMESPACE + "." + activity.activity
                activity_def = self.activity_retriever.get(full_activity_class_name, "activity")
                if not activity_def:
                    self.hallucinated_activity_ids.add(activity.id)
                    self.errors.append(
                        ActivityError(
                            type=WorkflowProcessingErrorType.ACTIVITY_DOES_NOT_EXIST,
                            activity_identifier=activity.id,
                            workflow_path=current_path,
                        )
                    )
                    # activity is hallucinated, so we replace it with an empty HTTP activity
                    processed_activities.append(
                        HttpRequest.model_validate(
                            {
                                "thought": activity.thought,
                                "id": activity.id,
                                "activity": "HttpRequest",
                                "with": HTTPCallParameters(method="GET", endpoint="", headers="", body=""),
                            },
                        )
                    )
                    continue

                # save the correspondence between the activity id and the output type
                output_schema: OutputType | None = activity_def.get("outputTypeJsonSchema", None)
                if output_schema and "type" in output_schema:
                    self.activity_output_types_dict[activity.id] = output_schema["type"]

                # TEMPORARY: remove after SW will support customizable export properties
                self.replace_activity_id_with_export_property(activity_def, activity)

                # Get the configuration from the config map
                config = self.config_map.get(full_activity_class_name) or {}
                is_generic_configurable_generic_dap_activity = False

                if not config:
                    self._add_connector_error(activity_def, current_path, full_activity_class_name, activity.id, activity.thought)
                else:
                    is_configured = config.get("activityIsConfigured", False)
                    if (
                        not is_configured
                        and config["actualActivityName"] == DAP_HTTP_ACTIVITY_NAME
                        and activity_def["connectorKey"] in self.connections_by_key
                        and "connectionId" in self.connections_by_key[activity_def["connectorKey"]]
                    ):
                        # the activity is actually configured, but the DAP CLI is not able to set the connection for it. just set the connection to
                        is_generic_configurable_generic_dap_activity = True
                    elif not is_configured:
                        self._add_connector_error(activity_def, current_path, full_activity_class_name, activity.id, activity.thought)

                activity_with_metadata = self._convert_to_connector_activity_with_metadata(activity_def, activity, config)
                if is_generic_configurable_generic_dap_activity:
                    activity_with_metadata.with_.connectionId = self.connections_by_key[activity_def["connectorKey"]]["connectionId"]  # type: ignore
                    if "headers" not in activity_with_metadata.with_.bodyParameters:
                        activity_with_metadata.with_.bodyParameters["headers"] = "${{}}"
                    if "endpoint" in activity_with_metadata.with_.bodyParameters and "url" not in activity_with_metadata.with_.bodyParameters:
                        activity_with_metadata.with_.bodyParameters["url"] = activity_with_metadata.with_.bodyParameters["endpoint"]
                        del activity_with_metadata.with_.bodyParameters["endpoint"]

                processed_activities.append(activity_with_metadata)
                continue

            if isinstance(activity, JsInvoke) or isinstance(activity, HttpRequest) or isinstance(activity, Response):
                # TEMPORARY: remove after SW will support customizable export properties
                self.replace_activity_id_with_export_property(None, activity)

            # Only append activities that are already compatible with POST_GENERATION_ACTIVITIES
            if any(isinstance(activity, t) for t in self.post_generation_activity_types):
                processed_activities.append(activity.model_copy(deep=True))
            else:
                raise ValueError(f"Unsupported activity type: {type(activity)} with ID: {activity.id}")

        return processed_activities

    def _add_connector_error(
        self, activity_def: ActivityDefinition, current_path: list[PathSegment], full_activity_class_name: str, activity_id: str, activity_name: str
    ):
        # the connection could not be initialized - add a connection error to the errors list
        connector_key = activity_def.get("connectorKey")
        if not connector_key:
            raise ValueError(f"Connector key not found for activity: {full_activity_class_name}")
        self.errors.append(
            InvalidConnectionError(
                workflow_path=current_path,
                connector_key=connector_key,
                class_name=activity_def["className"],
                category=activity_def["category"],
                activity_identifier=activity_id,
                activity_name=activity_name,
            )
        )

    def _convert_to_connector_activity_with_metadata(
        self, activity_def: ActivityDefinition, activity: ConnectorIntegration, config: dict
    ) -> ConnectorIntegrationWithMetadata:
        """
        Converts a ConnectorActivity to a ConnectorActivityWithMetadata by matching it with the correct activity definition
        and configuration from the config map.
        """
        # Get input properties
        input_properties = self._get_input_properties(config, activity_def)

        # separate input properties into different dicts based on the location
        path_params, body_params, query_params = {}, {}, {}
        for key, value in activity.with_.items():
            location: InputPropertyLocation | None = next((prop.get("location") for prop in input_properties if prop["schema_name"] == key), None)

            if key == DAP_HTTP_METHOD_PARAM_NAME and activity_def["fullActivityName"] == DAP_HTTP_ACTIVITY_NAME:
                # for DAP HTTP activities we must replace the double quotes in the method parameter with single quotes
                value = value.replace('"', "'")

            if location == "Path":
                path_params[key] = value
                continue
            if location == "Query":
                query_params[key] = value
                continue
            body_params[key] = value
            if location != "Body":
                # Body is the default location, if we don't have a valid location
                print(f"INFO: Invalid location: {location} found for key: {key} in activity: {activity.activity}. Ignoring property")

        # Create the request parameters
        request_params = ConnectorRequestParameters(
            connectionId=config.get("connectionId"),
            pathParameters=path_params,
            queryParameters=query_params,
            bodyParameters=body_params,
        )
        # Create the metadata
        metadata = ConnectorIntegrationMetadata(
            configuration=config.get("configuration") or activity_def["activityConfiguration"],
            method=config.get("httpMethod") or activity_def["httpMethod"],
            uiPathActivityTypeId=activity_def["activityTypeId"],
        )
        # Create and return the new activity with metadata
        activity_args = {
            "thought": activity.thought,
            "id": activity.id,
            "activity": activity.activity,
            "with": request_params,
            "metadata": metadata,
        }

        return ConnectorIntegrationWithMetadata(**activity_args)

    def _get_input_properties(self, config: dict, activity_def: ActivityDefinition | None) -> list[InputProperty]:
        """Get input properties from either config or activity definition."""
        config_input_schema = config.get("inputPropertiesJsonSchema")
        if config_input_schema is not None:
            return config_input_schema

        if activity_def is None:
            return []

        return activity_def.get("inputPropertiesJsonSchema") or []

    @staticmethod
    def build_post_gen_scope_activity(
        activity: BASE_API_ACTIVITIES, do_block_copy: list[POST_GENERATION_ACTIVITIES]
    ) -> PostGenSequence | PostGenForEach | PostGenTryCatch | PostGenDoWhile:
        """
        Helper method to build a post-process scope activity.
        """
        if isinstance(activity, Sequence):
            return PostGenSequence.model_validate({**activity.model_dump(by_alias=True, exclude={"do"}), "do": do_block_copy})
        elif isinstance(activity, ForEach):
            return PostGenForEach.model_validate({**activity.model_dump(by_alias=True, exclude={"do"}), "do": do_block_copy})
        elif isinstance(activity, DoWhile):
            return PostGenDoWhile.model_validate({**activity.model_dump(by_alias=True, exclude={"do"}), "do": do_block_copy})
        else:
            # This shouldn't happen if used correctly
            raise ValueError(f"Unsupported activity type: {type(activity)}")

    # TEMPORARY: remove after SW will support customizable export properties
    def replace_activity_id_with_export_property(self, activity_def: ActivityDefinition | None, activity: BaseWorkflowActivity):
        """
        Replaces the activity id with an export property.
        This is a temporary solution to mimic the export properties set for dynamic activities in SW.
        """

        @staticmethod
        def sanitize_identifier(identifier: str) -> str:
            for old, new in IDENTIFIER_SANITIZATION_SUBSTITUTIONS.items():
                identifier = identifier.replace(old, new)
            return identifier

        if not isinstance(activity, ConnectorIntegration):
            return  # we don't need to replace the id for non-connector activities

        if activity_def is None:
            # TODO: log an error
            return

        # find the correct id value
        if activity_def["fullActivityName"] == DAP_HTTP_ACTIVITY_NAME:
            id_base = activity_def["className"]
        else:
            # Deserialize the activity configuration JSON if it exists
            if not activity_def["activityConfiguration"]:
                raise ValueError(f"Activity configuration not found for activity: {activity_def['fullActivityName']}")

            if "objectName" not in activity_def["activityConfiguration"]:
                # most likely a generic DAP activity, so we don't need to replace the id as the activity is not configurable currently
                # very unlikely to happen - but might if an activity is hallucinated or if a generic DAP activity is part of the demonstrations
                # TODO: log an error
                return

            activity_config = json.loads(activity_def["activityConfiguration"])
            id_base = activity_config["objectName"]

        sanitized_id = sanitize_identifier(id_base)
        if sanitized_id not in self.export_properties_mapping:
            self.export_properties_mapping[sanitized_id] = []

        # override the activity id with the re-constructed export property
        old_id = activity.id

        activity.id = f"{sanitized_id}_{len(self.export_properties_mapping[sanitized_id]) + 1}"

        self.export_properties_mapping[sanitized_id].append((old_id, activity.id))

    def get_expression_with_id_replacements(self, expression: str) -> str:
        """
        Replaces the activity id with the export property.
        This is a temporary solution to replace the activity ids with the SW compatible export properties.
        """

        # these are all of the activity ids that were replaced with SW compatible export properties
        activity_id_replacements = [replacement for replacements in self.export_properties_mapping.values() for replacement in replacements]

        # Find all context output path references and validate them
        id_match = ANY_OUTPUT_PATH_REGEX.finditer(expression)
        for match in id_match:
            expression_segment = match.group(0)
            id_to_replace = match.group(1)
            for old_id, new_id in activity_id_replacements:
                if old_id == id_to_replace:
                    if any(char in new_id for char in NON_SANITIZABLE_CHARACTERS):
                        # if the sanitized id contains any non-sanitizable characters, we use a dictionary notation to access the property
                        new_id = f'["{new_id}"]'
                        id_to_replace = "." + id_to_replace  # we replace the dot with a dictionary notation
                    replacement = expression_segment.replace(id_to_replace, new_id)
                    expression = expression.replace(expression_segment, replacement)
                    break

        return expression

    async def _try_fix_expression(self, expression: str, error: str, expression_language: str) -> str:
        expression_fix_task = EXPRESSION_GENERATION_TASKS[SourceType.ApiWorkflow]
        result, _ = await expression_fix_task._run_fix_expression(
            ExpressionGenerationFixRequest(
                currentError=error,
                availableVariables=self._build_variables_for_expression_fix(),
                additionalTypeDefinitions=self.output_type_definitions,
                expressionTypeDefinition="Any",
                expressionLanguage="javascript" if expression_language == "js" else expression_language,
                currentExpression=expression,
                source=SourceType.ApiWorkflow,
            )
        )

        return result["expression"]

    async def _try_fix_code(self, code: str, error: str) -> str:
        expression_fix_task = EXPRESSION_GENERATION_TASKS[SourceType.JsInvoke]
        result, _ = await expression_fix_task._run_fix_expression(
            ExpressionGenerationFixRequest(
                currentError=error,
                availableVariables=self._build_variables_for_expression_fix(),
                additionalTypeDefinitions=self.output_type_definitions,
                expressionTypeDefinition="Any",
                expressionLanguage="javascript",
                currentExpression=code,
                source=SourceType.JsInvoke,
            )
        )

        return result["expression"]

    def _build_variables_for_expression_fix(self) -> list[dict]:
        variables = list(self.variables)
        variables.extend(AVAILABLE_VARIABLES)
        variables = [f"${v}" for v in AVAILABLE_VARIABLES]
        return [{"name": v, "type": "var"} for v in variables]
