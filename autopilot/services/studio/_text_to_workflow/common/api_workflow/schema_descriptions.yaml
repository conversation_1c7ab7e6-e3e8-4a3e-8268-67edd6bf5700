# Field descriptions for schema.py
# These descriptions are used in Pydantic Field definitions

# For
for_each: "Name of variable to store the current item of the iteration"
for_in: "The collection to iterate over"
for_at: "Name of variable to store the current index of the iteration"

# Base Properties
base_workflow_activity_thought: "Name of the current activity giving a brief description of what it does"
base_workflow_activity_id: "Unique identifier for the current activity"
base_workflow_activity_then: "The id of the next activity to execute after the current one"

# HttpRequestActivity
http_request_activity_type: "Constant that should always be set to 'HttpRequest'"
http_request_activity_with: "Configuration for the HTTP request including method, endpoint and headers"

# HTTPCallParameters
http_call_parameters_method: "Constant representing the HTTP method to use for the request. Should always be set to one of the following: GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS"
http_call_parameters_endpoint: "Expression that resolves to the URL endpoint of the the HTTP request"
http_call_parameters_headers: "Expression that resolves to the dictionary of HTTP headers"
http_call_parameters_body: "Expression that resolves to the body of the HTTP request"

# DynamicHttpRequest
dynamic_http_call_parameters_with: "Configuration for the integration HTTP activity that executes an API HTTP request with parameters method, url, headers, query and body"

# IntegrationHTTPCallParameters
integration_http_call_parameters_url: "The relative URL endpoint for the HTTP request"
integration_http_call_parameters_query: "Expression that resolves to the query parameters of the HTTP request"

# ConnectorActivity
connector_activity_type: "The type of Integration Activity. Should only be a value from the list of provided Integration Activities"
connector_activity_with: "A dictionary of key-value pairs containing the input arguments for the integration activity which should be configured based on the schema of the user provided operations, the values must be expressions"

# JsInvokeActivity
js_invoke_activity_run: "A Javascript block of code that ends in a return statement. This code will have access to the entirety of the $context and $workflow objects and and will be executed by the JsInvoke activity"

# IfActivity
if_condition: "An expression that evaluates to a boolean value. If the condition is met, the trueSequence will be executed, otherwise the falseSequence will be executed."
if_activity_true_sequence: "Activities to execute when the condition is met. Should be empty if no action is needed"
if_activity_false_sequence: "Activities to execute when the condition is not met. Should be empty if no action is needed"

# SequenceActivity
sequence_activity_do: "The inner activities of the sequence. They will execute in order, the output of each activity should be available as input to the subsequent ones. The keys of the dictionary are the ids of the activities and the values are the activities themselves."

# BaseForEachActivity
for_each_activity_for: "Contains the configuration of the ForEach loop"

# ForEachActivity
for_each_activity_do: "The inner activities of the ForEach loop. They will be execute for every iteration of the ForEach, the output of each activity should be available as input to the subsequent ones. The keys of the dictionary are the ids of the activities and the values are the activities themselves."

# ApiWorkflow
api_workflow_input_arguments: "The input arguments for the workflow defined as a string representing a JSON schema. Remember, what we provide here is a schema of the input arguments, not the actual input arguments. The value of the input arguments will be provided in the 'input' field of the $workflow object. Example, for schema: { \"type\": \"object\", \"properties\": { \"name\": { \"type\": \"string\" } } }, the input arguments will be: { \"name\": \"John\" } and you can access it as $workflow.input.name]"
api_workflow_input_arguments_for_activity_edit: "The input arguments for the workflow defined as a string representing a JSON schema."
api_workflow_root: "The root activity of the workflow and starting point of the workflow execution"

# Custom model fields
custom_activity_type: "The type of the current Integration Activity. Must be {placeholder}."
custom_activity_with: "A dictionary of key-value pairs containing the input arguments for the current activity"

# ResponseActivity
response_activity: "Constant that should always be set to 'Response'"
response_activity_type: "The type of response. Should be 'SUCCESS' or 'FAIL'"
response_activity_response_message: "The message of the response."

# TryCatchActivity
try_catch_activity_try_sequence: "The activities in the Try block of the Try-Catch activity. If these activities are executed without throwing any exceptions, the Catch part will be ignored."
try_catch_activity_catch: "The catch part of the TryCatch structure. Contains the configuration for the Catch block"
try_catch_activity_catch_sequence: "Activities that are executed only if an exception is thrown during the Try block execution."
try_catch_activity_error: "The name of the variable that stores the exception details when the Try part fails. This variable contains error properties: 'title' (human-readable summary), 'status' (HTTP code), 'detail' (detailed explanation), and 'data' (an object with additional technical information). Access these in the catch block using the variable name."

# DoWhileActivity
do_while_activity_do: "The activities in the Do block of the DoWhile activity. These activities will be executed repeatedly until the condition is met."
do_while_activity_condition: "An expression that evaluates to a boolean value. If the condition is met, the Do block will be executed again, otherwise the DoWhile activity will end."
do_while_activity_limit: "The maximum number of iterations to execute. If the limit is reached, the DoWhile activity will end. This is optional. If not provided, the DoWhile activity will run indefinitely."

# BreakActivity
break_activity: "Constant that should always be set to 'Break'"