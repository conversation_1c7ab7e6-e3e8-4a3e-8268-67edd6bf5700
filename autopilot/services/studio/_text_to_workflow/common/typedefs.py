import copy
import functools
import pathlib
import pickle

import tqdm

from services.studio._text_to_workflow.common import build_embeddings, connections_loader, constants
from services.studio._text_to_workflow.common.schema import ActivityDefinition, ParamTypeDef, TypeDef
from services.studio._text_to_workflow.common.typedefs_parser import parse_typedef, parse_typedefs
from services.studio._text_to_workflow.utils import dotnet_dynamic_activities_discovery, paths, telemetry_utils
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump

LOGGER = telemetry_utils.AppInsightsLogger()

_bin_path = paths.get_typedefs_embeddings_path() / "typedefs.bin"
_activity_typedefs_path: pathlib.Path = paths.get_typedefs_embeddings_path() / "type_defs.yaml"
_additional_typedefs_path: pathlib.Path = paths.get_typedefs_embeddings_path() / "add_type_defs.yaml"
_activity_typedefs: dict[str, TypeDef] = {}
_additional_typedefs: dict[str, TypeDef] = {}


def exists():
    return _activity_typedefs_path.exists() and _additional_typedefs_path.exists()


def raise_if_not_loaded(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        if not exists():
            raise ValueError("Typedefs not built. Please `typedefs.build()` once and then ensure `typedefs.load()` each run.")
        if not _activity_typedefs:  # TODO: would be better to have an explicit loaded bool
            raise ValueError("Typedefs either empty or not loaded. Please ensure `typedefs.load()` each run.")

        return func(*args, **kwargs)

    return wrapper


async def build(show_progress_bar: bool = False) -> tuple[int, int]:
    activity_documents = build_embeddings.build("activity", show_progress_bar=show_progress_bar)["activities"]
    trigger_documents = build_embeddings.build("trigger", show_progress_bar=show_progress_bar)["activities"]
    # load and split documents
    documents = activity_documents + trigger_documents
    nondap_documents, dap_documents = {}, {}
    if show_progress_bar:
        documents = tqdm.tqdm(documents, desc="Typedefs: splitting documents", unit="documents", dynamic_ncols=True)
    for document in documents:
        if document["activityTypeId"] is not None:
            dap_documents[document["fullActivityId"]] = document
        else:
            nondap_documents[document["fullActivityId"]] = document
    # configure dap documents
    connections = connections_loader.get_connections_data()
    connections_by_key = {c["connector"]: c for c in connections}
    is_package_name, is_package_version, is_connections, documents_by_config = None, None, [], {}
    dap_documents_configured = {}
    dap_documents_not_configured = {}
    iterator = dap_documents.keys()
    if show_progress_bar:
        iterator = tqdm.tqdm(iterator, desc="Typedefs: Grouping DAP documents", unit="documents", dynamic_ncols=True)
    for document_fqn in iterator:
        document: ActivityDefinition = dap_documents[document_fqn]
        if is_package_version is None or document["packageVersion"] > is_package_version:
            is_package_name = document["packageName"]
            is_package_version = document["packageVersion"]
        connector_key = document["connectorKey"]
        document["typeDefinition"] = f"class {document['className']} : Activity {{}}"
        if connector_key in connections_by_key and connector_key is not None:
            is_connections.append(
                {
                    "ConnectionId": connections_by_key[connector_key]["connectionId"],
                    "Configuration": document["activityConfiguration"],
                    "ClassDefinition": document["typeDefinition"],
                    "ActivityFullName": document["fullActivityName"],
                    "UiPathActivityTypeId": document["activityTypeId"],
                }
            )
            if document["activityConfiguration"] not in documents_by_config:
                documents_by_config[document["activityConfiguration"]] = []
            documents_by_config[document["activityConfiguration"]].append(document)
            # print(f"Connector key found for {document['fullActivityId']} {connector_key}")
        else:
            # print(f"Connector key not found for {document['fullActivityId']} {connector_key}")
            dap_documents_not_configured[document["fullActivityId"]] = document
    # call is Generator cli
    is_generated_connections = await dotnet_dynamic_activities_discovery.get_type_definitions(
        is_package_name, is_package_version, is_connections, capture_output=False
    )
    if is_generated_connections is None:
        configurations = []
        LOGGER.warning("IS Generator CLI failed, using default type definitions")
    else:
        configurations = is_generated_connections["Configurations"]
    # Parse dap type definitions
    if show_progress_bar:
        configurations = tqdm.tqdm(configurations, desc="Typedefs: Patching DAP documents", unit="documents", dynamic_ncols=True)
    for generated_connection in configurations:
        config_documents = documents_by_config[generated_connection["OldConfiguration"]]
        for document in config_documents:
            if generated_connection["IsSuccess"]:
                document["typeDefinition"] = generated_connection["NewClassDefinition"] or ""
                if document["additionalTypeDefinitions"] and not document["additionalTypeDefinitions"].endswith("\r\n"):
                    document["additionalTypeDefinitions"] += "\r\n"
                document["additionalTypeDefinitions"] += generated_connection["AdditionalTypesDefinitions"] or ""
                if document["additionalNamespaces"] and not document["additionalNamespaces"].endswith("\r\n"):
                    document["additionalNamespaces"] += "\r\n"
                document["additionalNamespaces"] += generated_connection["AdditionalTypesNamespaces"] or ""
                document["activityConfiguration"] = generated_connection["NewConfiguration"] or ""
                document["activityIsConfigured"] = generated_connection["OldConfiguration"] != generated_connection["NewConfiguration"]
                dap_documents_configured[document["fullActivityId"]] = document
                # print(f"Generated type definition for {document['fullActivityId']}")
            else:
                # print(f"Failed to generate type definition for {document['fullActivityId']}")
                dap_documents_not_configured[document["fullActivityId"]] = document
    # create type def collections
    all_documents = {}
    all_documents.update(nondap_documents)
    all_documents.update(dap_documents_configured)
    all_documents.update(dap_documents_not_configured)
    iterator = all_documents.keys()
    if show_progress_bar:
        iterator = tqdm.tqdm(iterator, desc="Typedefs: Creating type definitions", unit="documents", dynamic_ncols=True)
    for document_fqn in iterator:
        document = all_documents[document_fqn]
        activity_name = document["fullActivityId"]
        activity_typedef_text = document["typeDefinition"]
        if activity_name in _activity_typedefs:
            LOGGER.error(f"Activity {activity_name} already exists in typedefs")
        activity_typedef = parse_typedef(activity_typedef_text)
        _activity_typedefs[activity_name] = activity_typedef
        try:
            additional_typedefs = parse_typedefs(document["additionalTypeDefinitions"])
            for additional_typedef_name, additional_typedef in additional_typedefs.items():
                _additional_typedefs[additional_typedef_name] = additional_typedef
        except ValueError:
            LOGGER.error(f"Failed to parse additional typedefs for {activity_name}:\n{document['additionalTypeDefinitions']}")

    # persist
    paths.get_typedefs_embeddings_path().mkdir(parents=True, exist_ok=True)
    yaml_dump(_activity_typedefs, _activity_typedefs_path)
    yaml_dump(_additional_typedefs, _additional_typedefs_path)
    with open(_bin_path, "wb") as f:
        pickle.dump({"activity_typedefs": _activity_typedefs, "additional_typedefs": _additional_typedefs}, f)
    return len(_activity_typedefs), len(_additional_typedefs)


def load() -> tuple[int, int]:
    global _activity_typedefs, _additional_typedefs
    with open(_bin_path, "rb") as f:
        data = pickle.load(f)
    _activity_typedefs = data["activity_typedefs"]
    _additional_typedefs = data["additional_typedefs"]
    return len(_activity_typedefs), len(_additional_typedefs)


def get_normal_activity_name(name: str) -> str:
    return constants.ACTIVITY_TYPEDEF_NAME_ALIASES.get(name, name)


@raise_if_not_loaded
def activity_typedef_exists(name: str) -> bool:
    name = get_normal_activity_name(name)
    return name in _activity_typedefs


@raise_if_not_loaded
def additional_typedef_exists(name: str) -> bool:
    return name in _additional_typedefs


@raise_if_not_loaded
def get_activity_typedef(name: str) -> TypeDef:
    name = get_normal_activity_name(name)
    return copy.deepcopy(_activity_typedefs[name])


@raise_if_not_loaded
def get_additional_typedef(name: str) -> TypeDef:
    return copy.deepcopy(_additional_typedefs[name])


@raise_if_not_loaded
def get_activity_aditional_typedefs(name: str) -> dict[str, TypeDef]:
    activity_typedef = get_activity_typedef(name)
    additional_typedefs = {}
    for param in activity_typedef["params"].values():
        for _type in param["components"]:
            if _type not in additional_typedefs and additional_typedef_exists(_type):
                additional_typedefs[_type] = get_additional_typedef(_type)
    return additional_typedefs


def get_outargument_type(param_typedef: ParamTypeDef) -> str:
    if param_typedef["category"] != "OutArgument":
        raise ValueError(f"param_typedef is not an OutArgument: {param_typedef}")
    param_typedef_type = param_typedef["type"]
    if param_typedef_type == "OutArgument":
        return "object"
    return param_typedef_type.removeprefix("OutArgument<").removesuffix(">")
