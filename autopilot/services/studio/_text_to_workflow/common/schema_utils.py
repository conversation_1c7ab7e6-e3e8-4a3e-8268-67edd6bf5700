# Comprehensive list of all possible activity fields in the desired order
# This is used to ensure consistent property ordering in JSON schemas
DYNAMIC_MODEL_FIELD_ORDER = ["thought", "activity", "id", "condition", "limit", "code", "with", "for", "do", "export"]


def reorder_schema_properties(schema: dict):
    """
    Reorders properties in a JSON schema based on the DYNAMIC_MODEL_FIELD_ORDER.
    """
    # If there's no properties field or it's not a dict, return early
    if "properties" not in schema or not isinstance(schema["properties"], dict):
        return

    # Reorder the properties and replace the original
    schema["properties"] = reorder_properties(schema["properties"])


def reorder_properties(source_dict: dict) -> dict:
    """
    Creates a new dictionary with properties reordered based on the predefined DYNAMIC_MODEL_FIELD_ORDER.
    """
    # Create a new ordered dictionary
    ordered_dict = {}

    # First add properties in the specified order
    ordered_dict = {k: source_dict[k] for k in DYNAMIC_MODEL_FIELD_ORDER if k in source_dict}

    # Then add any remaining properties that weren't in the order list
    ordered_dict.update(source_dict)

    return ordered_dict
