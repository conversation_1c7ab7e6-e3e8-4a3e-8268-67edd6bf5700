import argparse
import asyncio

import typing_extensions as t

from services.studio._text_to_workflow.activity_config import (
    activity_config_config,
    activity_config_demo_retriever,
    activity_config_evaluation,
)
from services.studio._text_to_workflow.common import typedefs
from services.studio._text_to_workflow.common.schema import SubsetName, TargetFramework
from services.studio._text_to_workflow.utils import dotnet_dynamic_activities_discovery, paths
from services.studio._text_to_workflow.utils.testing import get_testing_request_context


async def run_build_typedefs():
    dataset_connections_path = paths.get_dataset_connections_cache_path()
    dotnet_dynamic_activities_discovery.load_cached_connections(dataset_connections_path)
    await typedefs.build(show_progress_bar=True)


async def run_build_demo_retriever(include_production_demos):
    typedefs.load()
    await activity_config_demo_retriever.init_and_load(include_production_demos)


async def run_evaluate(
    name: str | None,
    group: str,
    frameworks: tuple[TargetFramework, ...],
    subsets: tuple[SubsetName, ...],
    limit: int | None,
    model_type: t.Literal["openai", "azure", "local"] | None,
    model_name: str | None,
    model_deployment_name: str | None,
    update_whitelist: bool,
):
    activity_config_config.update(model_type=model_type, model_name=model_name, model_deployment_name=model_deployment_name)
    await activity_config_demo_retriever.init_and_load()
    await activity_config_evaluation.evaluate(name, group, frameworks, subsets, limit, update_whitelist)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument(
        "job",
        type=str,
        default="evaluate",
        choices=["build", "build-typedefs", "build-demo-retriever", "evaluate", "evaluate-finetuning"],
    )
    args, _ = ap.parse_known_args()

    request_context = get_testing_request_context("en", "Activity Config Evaluation")

    if args.job == "build" or args.job == "build-typedefs":
        asyncio.run(run_build_typedefs())
    if args.job == "build" or args.job == "build-demo-retriever":
        ap.add_argument("--include_production", action="store_true", help="Include production demos for building the demo retriever.")
        args, _ = ap.parse_known_args(namespace=args)
        asyncio.run(run_build_demo_retriever(args.include_production))
    if args.job == "evaluate":
        ap.add_argument("--name", default=None, type=str, help="The run name.")
        ap.add_argument("--group", required=True, type=str, help="The group name for the evaluation run. Added to wandb.")
        ap.add_argument("--frameworks", required=True, type=str, choices=["Windows", "Portable"], nargs="+", help="Evaluate on frameworks.")
        ap.add_argument("--subsets", required=True, type=str, choices=["train", "test", "static", "uia", "debug"], nargs="+", help="Evaluate on subsets.")
        ap.add_argument("--limit", default=None, type=int, help="Limit #examples to process from each subset. Does not affect cached evaluations.")
        ap.add_argument("--model_type", default=None, type=str, help="The model type to evaluate.")
        ap.add_argument("--model_name", default=None, type=str, help="The model name to evaluate.")
        ap.add_argument("--model_deployment_name", default=None, type=str, help="The deployment name to evaluate.")
        ap.add_argument("--update_whitelist", action="store_true", help="Update the list of supported activities based on evaluation results.")
        args, _ = ap.parse_known_args(namespace=args)
        asyncio.run(
            run_evaluate(
                args.name,
                args.group,
                tuple(args.frameworks),
                tuple(args.subsets),
                args.limit,
                args.model_type,
                args.model_name,
                args.model_deployment_name,
                args.update_whitelist,
            )
        )
