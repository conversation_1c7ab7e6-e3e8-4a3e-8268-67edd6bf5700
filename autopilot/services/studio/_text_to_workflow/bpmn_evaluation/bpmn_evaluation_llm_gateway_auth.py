import asyncio
import os
from contextlib import asynccontextmanager

from experimental.autopilot_dataset import studio_token_helper
from services.studio._text_to_workflow.core.dev_config import dev_settings
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.uipath_cloud_platform import is_token_valid

# Global connection pooling variables
_connection_lock = asyncio.Lock()
_connection_pool = {}
_max_connections = int(os.getenv("MAX_GATEWAY_CONNECTIONS", "20"))


@asynccontextmanager
async def connection_pool_manager():
    """Manage connection pool to prevent too many concurrent connections."""
    pool_key = os.getenv("TENANT_ID", "default")

    async with _connection_lock:
        if pool_key not in _connection_pool:
            _connection_pool[pool_key] = asyncio.Semaphore(value=_max_connections)

    semaphore = _connection_pool[pool_key]
    async with semaphore:
        yield


def get_bpmn_evaluation_request_context(localization="en", client_name="BPMN Evaluation") -> RequestContext:
    """Create a request context for BPMN evaluation using environment variables or dev_settings as fallback."""
    tenant_id = os.getenv("UIPATH_TENANT_ID") or dev_settings.TEST_TENANT_ID
    organization_id = os.getenv("UIPATH_ORG_ID") or dev_settings.TEST_ORGANIZATION_ID
    user_id = os.getenv("UIPATH_USER_ID") or dev_settings.TEST_USER_ID

    data = {
        "request_id": "123",
        "tenant_id": tenant_id,
        "organization_id": organization_id,
        "user_id": user_id,
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "Test",
        "client_name": client_name,
        "localization": localization,
        "client_version": "1.0.0",
    }
    return RequestContext(**data)


async def get_studio_token():
    """Get a valid studio token from LLM gateway or return None if credentials are missing."""
    token = None

    if os.getenv("UIPATH_CLIENT_ID") and os.getenv("UIPATH_CLIENT_SECRET"):
        # Use LLM gateway if credentials are available
        async with connection_pool_manager():
            for _ in range(3):  # Try up to 3 times
                token = await studio_token_helper.get_studio_token()
                if token and is_token_valid(token):
                    break

            if not token or not is_token_valid(token):
                print("Warning: Could not obtain valid token from LLM gateway")

    return token


async def setup_llm_gateway_authentication(tenant_id=None):
    """Setup authentication using LLM gateway if credentials are available, otherwise use direct endpoints."""

    # Get tenant ID from environment if not provided, then fall back to dev_config
    if tenant_id is None:
        tenant_id = os.getenv("UIPATH_TENANT_ID") or dev_settings.TEST_TENANT_ID
        if os.getenv("UIPATH_TENANT_ID"):
            print(f"Using tenant ID from environment: {tenant_id}")
        else:
            print(f"Using tenant ID from dev_config: {tenant_id}")

    token = await get_studio_token()

    if token:
        # Configure to use LLM gateway
        os.environ["USE_LLM_GATEWAY"] = "true"
        os.environ["UIPATH_TOKEN"] = token

        # Setup request context with token using our custom function
        request_context = get_bpmn_evaluation_request_context("en", "BPMN Evaluation")
        request_utils.set_request_context(request_context)
        print(f"Using LLM gateway for tenant: {tenant_id}")
    else:
        # Fall back to direct OpenAI endpoint
        os.environ["USE_LLM_GATEWAY"] = "false"

        # Setup request context without token using our custom function
        request_context = get_bpmn_evaluation_request_context("en", "BPMN Evaluation")
        request_utils.set_request_context(request_context)
        print("Using direct OpenAI endpoint (no credentials for LLM gateway)")

    return token
