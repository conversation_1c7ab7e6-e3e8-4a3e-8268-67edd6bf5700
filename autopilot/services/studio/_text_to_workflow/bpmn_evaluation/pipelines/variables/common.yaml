variables:
  poolName: "cm-mls-1t4-gpu-8a-cd"
  workdir: "/workspace/data"
  cudaVisibleDevices: "all"
  keyVaultName: "ml-dev-gptproxy-kv"
  openapiType: $(gpt-api-type)
  openapiEndpoint: $(AZURE-OPENAI-API-ENDPOINT)
  openapiVersion: $(AZURE-OPENAI-API-VERSION)
  openapiKey: $(AZURE-OPENAI-API-KEY)
  usellmGateway: "true"
  appInsightsInstrumentationKey: "$(APP-INSIGHTS-KEY)"
  appInsightsConnectionString: "$(APP-INSIGHTS-CONNECTION-STRING)"
  uipathClientId: "$(uipath-client-id)"
  uipathClientSecret: "$(uipath-client-secret)"
  uipathTenantId: "be014e6a-4c8c-4642-82c5-3c1131ab46bb"
  uipathOrgId: "bc2ddac5-57bc-40e6-93fe-3b319b60ce36"
  uipathS2sClientId: "65E61F8C-F740-4A83-B2FF-EBE91EFE7AE8"
  uipathS2sClientSecret: "$(s2s-client-secret)"
