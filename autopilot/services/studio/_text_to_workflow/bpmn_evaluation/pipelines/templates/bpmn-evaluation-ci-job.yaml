jobs:
- job: run_bpmn_eval
  displayName: "Run BPMN Evaluation"
  # pool: "Autopilot-Developers-Test-Agents"
  pool:
    vmImage: 'ubuntu-latest'

  steps:
    # 1) Install Python 3.10 Manually (if not present)
    - script: |
        echo "Updating package list and installing Python 3.10..."
        sudo apt update && sudo apt install -y python3.10 python3.10-venv python3.10-dev
        
        echo "Setting Python 3.10 as default..."
        sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.10 1
        sudo update-alternatives --config python3 <<< '1'  # Automatically select Python 3.10
        python3 --version  # Verify Python version

        # Increase system limits for high concurrency
        echo "Setting system limits for high concurrency..."
        ulimit -n 4096 || echo "Warning: Could not increase file descriptor limit"
      displayName: "Install and Set Python 3.10"

    # 2) Checkout main repository
    - checkout: self
      clean: true
      lfs: false
      fetchDepth: 1
      displayName: "Checkout Main Repository"
      sparseCheckoutDirectories: autopilot
      path: s/ml

    # 3) Checkout external repository
    - checkout: "Autopilot.Samples"
      displayName: "Checkout Autopilot.Samples"
      clean: true
      lfs: true
      fetchDepth: 1
      sparseCheckoutPatterns: /* !/Dataset/Downloads !/UIAutomation/DatasetNotUsed !/UIAutomation/DatasetRaw
      path: s/ml/autopilot/.data/Autopilot.Samples

    # 4) Retrieve Key Vault secrets
    - task: AzureKeyVault@2
      displayName: "Get KeyVault Secrets"
      inputs:
        azureSubscription: "AzureDevTest-EA"
        KeyVaultName: "$(keyVaultName)"
        SecretsFilter: "*"

    # 5) Download Embeddings Model package (optional)
    - task: UniversalPackages@0
      displayName: "Download Embeddings Model"
      inputs:
        command: download
        vstsFeed: "ML-models"
        vstsFeedPackage: "studio-api-sentence-embeddings-model"
        vstsPackageVersion: "0.0.5"
        downloadDirectory: "$(Build.SourcesDirectory)/ml/autopilot/.data/Models/stella-trained"

    # 6) Install OS-level dependencies
    - script: |
        echo "Installing OS-level dependencies..."
        sudo apt update && sudo apt install -y build-essential libonig-dev jq
        chmod +x "$(Build.SourcesDirectory)/ml/autopilot/services/studio/_text_to_workflow/bpmn_evaluation/scripts/os_dependencies.sh"
        "$(Build.SourcesDirectory)/ml/autopilot/services/studio/_text_to_workflow/bpmn_evaluation/scripts/os_dependencies.sh"
      displayName: "Install OS-level Dependencies"
      condition: eq(variables['Agent.OS'], 'Linux')

    # 7) Ensure uv is installed
    - script: |
        echo "Checking for uv..."
        if ! command -v uv &> /dev/null; then
          echo "uv not found, installing..."
          pip install uv
        else
          echo "uv is already installed."
        fi
      displayName: "Install uv tool"

    # 8) Create a Python venv and install dependencies from pyproject.toml
    - script: |
        echo "Creating a local .venv and installing dependencies from pyproject.toml..."

        # Move into the folder with pyproject.toml
        cd "$(Build.SourcesDirectory)/ml/autopilot"

        # Create a Python venv
        python3 -m venv .venv
        source .venv/bin/activate

        # Upgrade pip and install uv in venv
        pip install --upgrade pip uv

        # Compile and sync dependencies
        uv pip compile pyproject.toml

        # Sync (install) main dependencies
        UV_INDEX_UIPATH_PASSWORD=$(System.AccessToken) uv sync --no-sources --reinstall

        # Manually install pyjq if not installed
        if ! python -c "import pyjq" &> /dev/null; then
          echo "Installing pyjq manually..."
          pip install pyjq
        else
          echo "pyjq is already installed."
        fi

        # Configure asyncio for high concurrency
        pip install "aiohttp[speedups]" "cchardet" "aiodns" "brotli"

        pip list
      displayName: "Create Virtualenv & Install Python Deps from pyproject.toml"

    # Disable memory efficient attention for CPU environments
    - task: Bash@3
      displayName: 'Disable memory efficient attention for CPU environments'
      inputs:
        targetType: 'inline'
        script: |
          # Find all config.json files in model folders and disable memory_efficient_attention
          find "$(Build.SourcesDirectory)" -name "config.json" -type f -exec bash -c '
            for file do
              echo "Processing $file"
              # Check if the file contains memory_efficient_attention
              if grep -q "memory_efficient_attention" "$file"; then
                # Update the config to disable memory_efficient_attention
                sed -i -E "s/\"use_memory_efficient_attention\":\s*true/\"use_memory_efficient_attention\": false/g" "$file"
                sed -i -E "s/\"unpad_inputs\":\s*true/\"unpad_inputs\": false/g" "$file"
                echo "Disabled memory_efficient_attention in $file"
              fi
            done
          ' bash {} \;

    # 9) Run BPMN Evaluation
    - script: |
        echo "Using provided RUN_ID: $(runId)"

        echo "Running BPMN Evaluation Entry Script..."
        chmod +x "$(Build.SourcesDirectory)/ml/autopilot/services/studio/_text_to_workflow/bpmn_evaluation/scripts/eval_entry.sh"

        echo "Activating virtual environment..."
        cd "$(Build.SourcesDirectory)/ml/autopilot/services/studio/_text_to_workflow"
        source "$(Build.SourcesDirectory)/ml/autopilot/.venv/bin/activate"

        # Run the evaluation script inside the venv
        ./bpmn_evaluation/scripts/eval_entry.sh
      displayName: "Run BPMN Evaluation Entry Script"
      env:
        RUN_ID: "$(runId)"
        USE_CACHED_CONNECTIONS: "true"
        PYTHONASYNCIODEBUG: "0"
        OPENAI_API_TYPE: "$(openapiType)"
        AZURE_OPENAI_ENDPOINT: "$(openapiEndpoint)"
        OPENAI_API_VERSION: "$(openapiVersion)"
        AZURE_OPENAI_API_KEY: "$(openapiKey)"
        USE_LLM_GATEWAY: "$(usellmGateway)"
        RUN_EMBEDDINGS_BACKGROUND_REBUILD: "false"
        CUDA_VISIBLE_DEVICES: "$(cudaVisibleDevices)"
        APP_INSIGHTS_KEY: "$(appInsightsInstrumentationKey)"
        APP_INSIGHTS_CONNECTION_STRING: "$(appInsightsConnectionString)"
        ENABLE_TELEMETRY: "true"
        UIPATH_CLIENT_ID: "$(uipathClientId)"
        UIPATH_CLIENT_SECRET: "$(uipathClientSecret)"
        S2S_CLIENT_ID: "$(uipathS2sClientId)"
        S2S_CLIENT_SECRET: "$(uipathS2sClientSecret)"
        UIPATH_ORG_ID: "$(uipathOrgId)"
        UIPATH_TENANT_ID: "$(uipathTenantId)"

    # 10) Publish eval summary report
    - task: PublishBuildArtifacts@1
      displayName: "Publish BPMN Evaluation Report & Logs"
      condition: succeededOrFailed()
      inputs:
        pathToPublish: "$(Build.ArtifactStagingDirectory)/bpmn_eval_results"
        artifactName: "BPMN_Evaluation_Report"
