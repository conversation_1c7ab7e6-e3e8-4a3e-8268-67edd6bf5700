import pytest

from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.schema import build_custom_workflow_model
from services.studio._text_to_workflow.common.schema import ActivityDefinition
from services.studio._text_to_workflow.models.api_workflow.output_parsers import (
    ACTIVITY_BLOCK_PROPERTIES,
    ACTIVITY_DEFINITION_NAME,
    ACTIVITY_REFERENCE,
    ApiWorkflowSchemaGenerator,
)

STANDARD_SCHEMA_PROPERTIES = {
    "#/$defs/Response",
    "#/$defs/HttpRequest",
    "#/$defs/JsInvoke",
    "#/$defs/Sequence",
    "#/$defs/ForEach",
    "#/$defs/If",
    "#/$defs/TryCatch",
    "#/$defs/DoWhile",
    "#/$defs/Break",
}


@pytest.mark.parametrize(
    "activity_names",
    [
        [
            "UiPath.IntegrationService.Activities.Runtime.Activities.Microsoft_TeamsSend_Group_Chat_Message",
            "UiPath.IntegrationService.Activities.Runtime.Activities.SalesforceUpdate_Account",
        ]
    ],
)
def test_build_custom_workflow_model(api_activities_retriever: APIActivitiesRetriever, activity_names: list[str]):
    """Test that a custom workflow model can be built from an activity definition."""
    # Get activity definitions from the retriever
    activity_defs = [actdef for activity_name in activity_names if (actdef := api_activities_retriever.get(activity_name, "activity")) is not None]

    # Build the custom workflow model
    custom_model = build_custom_workflow_model(activity_defs, 12)

    # Generate a JSON schema from the model
    json_schema = custom_model.model_json_schema(schema_generator=ApiWorkflowSchemaGenerator)

    # Basic assertion to verify schema generation
    assert json_schema is not None
    assert "$defs" in json_schema

    # check that the activity definition is present
    assert ACTIVITY_DEFINITION_NAME in json_schema["$defs"]
    assert_activity_union_definition(json_schema["$defs"][ACTIVITY_DEFINITION_NAME], activity_defs)

    # check that the activity definitions are present in the scope properties
    for object_name, scope_properties in ACTIVITY_BLOCK_PROPERTIES.items():
        for scope_property in scope_properties:
            property_schema = json_schema["$defs"][object_name]["properties"][scope_property]
            # check that the reference to the activity definition is present
            assert property_schema is not None
            assert property_schema["items"] == ACTIVITY_REFERENCE


def assert_activity_union_definition(activity_union_schema: dict, activity_defs: list[ActivityDefinition]):
    """Assert the JSON schema of the activity union is correct."""

    any_of_types = [t["$ref"] for t in activity_union_schema["anyOf"]]
    # check that the standard schema properties are present
    for standard_schema_property in STANDARD_SCHEMA_PROPERTIES:
        assert standard_schema_property in any_of_types

    # check that the activity definitions are present
    for activity_def in activity_defs:
        assert "#/$defs/" + activity_def["className"] in any_of_types

    # check that other properties are not present
    assert len(any_of_types) == len(STANDARD_SCHEMA_PROPERTIES) + len(activity_defs)
