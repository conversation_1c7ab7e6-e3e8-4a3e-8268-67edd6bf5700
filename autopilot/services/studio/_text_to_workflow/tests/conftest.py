# ruff: noqa: E402
import json
import os
import pathlib as pl
import random

import httpx
import pytest
import pytest_asyncio
from dotenv import load_dotenv

from services.studio import service
from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.core import constants, settings
from services.studio._text_to_workflow.core.dev_config import dev_settings
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.tests.utils import test_configs as tc
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

load_dotenv()  # take environment variables from .env

from services.studio._text_to_workflow.tests.fixtures.agent_evaluation_fixtures.agent_evaluation_input import (
    agent_evaluation_input,
)
from services.studio._text_to_workflow.tests.fixtures.fix_expression_fixtures.fix_expression_input import (
    fix_expression_input,
)
from services.studio._text_to_workflow.tests.fixtures.testdata_generation_fixtures.testdata_generation_input import (
    function_with_multiple_params_input,
    lm_yaml_input,
    simple_function_input,
)
from services.studio._text_to_workflow.utils.testing import get_testing_client_jwt_token, get_testing_request_context

_fixtures_path: pl.Path = pl.Path(__file__).absolute().parent / "fixtures"

# Workaround to get uncaught exception breakpoints in VSCode.
if os.getenv("PYTEST_RAISE_ERROR", "0") == "1":

    @pytest.hookimpl(tryfirst=True)
    def pytest_exception_interact(call):
        raise call.excinfo.value

    @pytest.hookimpl(tryfirst=True)
    def pytest_internalerror(excinfo):
        raise excinfo.value


def pytest_set_excluded_exceptions():
    """
    All tests will be retried unless they fail due to an AssertionError or CustomError
    """
    return [AssertionError]


@pytest.fixture(scope="session", autouse=True)
def set_request_context():
    if settings.USE_LLM_GATEWAY:
        request_utils.set_request_context(get_testing_request_context())
    else:
        request_utils.set_request_context(RequestContext(email="<EMAIL>", first_name="Test", last_name="Test"))


@pytest.fixture(autouse=True)
def make_deterministic():
    random.seed(0)


@pytest.fixture(scope="session")
def activities_retriever():
    return ActivitiesRetriever()


@pytest_asyncio.fixture(scope="session", loop_scope="session", autouse=True)
async def init_endpoints():
    await service._initialize_endpoints(force_rebuild=tc.FORCE_REBUILD_DATASET)


@pytest.fixture(scope="session")
def project_root_path():
    return pl.Path(__file__).absolute().parents[1]


@pytest.fixture(scope="session")
def activity_summary_fixtures_path():
    return _fixtures_path / "activity_summary_fixtures"


@pytest.fixture(scope="session")
def workflow_generation_fixtures_path():
    return _fixtures_path / "workflow_generation_fixtures"


@pytest.fixture(scope="session")
def workflow_fix_fixtures_path():
    return _fixtures_path / "workflow_fix_fixtures"


@pytest.fixture(scope="session")
def translate_fixtures_path():
    return _fixtures_path / "translate_fixtures"


@pytest.fixture(scope="session")
def ai_sequence_fixtures_path():
    return _fixtures_path / "ai_sequence_fixtures"


@pytest.fixture
def workflow_to_prune(ai_sequence_fixtures_path):
    return yaml_load(ai_sequence_fixtures_path / "workflow_to_prune.yaml")


@pytest.fixture(scope="session")
def testadata_generation_fixtures_path():
    return _fixtures_path / "testdata_generation_fixtures"


@pytest.fixture(scope="session")
def workflow_generation_postprocessing_fixtures_path():
    return _fixtures_path / "workflow_generation_postprocessing_fixtures"


@pytest.fixture(scope="session")
def workflow_abstraction_fixtures_path():
    return _fixtures_path / "workflow_abstraction_fixtures"


@pytest.fixture
def log_person_data(activity_summary_fixtures_path):
    return yaml_load(activity_summary_fixtures_path / "log_person_data.yaml")


@pytest.fixture
def log_person_data_localized(activity_summary_fixtures_path):
    return yaml_load(activity_summary_fixtures_path / "log_person_data_localized.yaml")


@pytest.fixture
def send_email_on_upload(workflow_generation_fixtures_path):
    return yaml_load(workflow_generation_fixtures_path / "send_email_on_upload.yaml")


@pytest.fixture
def send_email_on_onedrive_upload(workflow_fix_fixtures_path):
    return yaml_load(workflow_fix_fixtures_path / "send_email_on_onedrive_upload.yaml")


@pytest.fixture
def translate_single(translate_fixtures_path):
    return yaml_load(translate_fixtures_path / "translate_single.yaml")


@pytest.fixture
def translate_multi(translate_fixtures_path):
    return yaml_load(translate_fixtures_path / "translate_multi.yaml")


@pytest.fixture
def translate_yaml(translate_fixtures_path):
    return yaml_load(translate_fixtures_path / "translate_yaml.yaml")


@pytest.fixture
def send_email(ai_sequence_fixtures_path):
    return yaml_load(ai_sequence_fixtures_path / "send_email.yaml")


@pytest.fixture
def switch_sequence(ai_sequence_fixtures_path):
    return yaml_load(ai_sequence_fixtures_path / "switch_sequence.yaml")


@pytest.fixture
def long_uia_sequence(ai_sequence_fixtures_path):
    return yaml_load(ai_sequence_fixtures_path / "long_uia_sequence.yaml")


@pytest.fixture
def excel_uia_sequence(ai_sequence_fixtures_path):
    return yaml_load(ai_sequence_fixtures_path / "excel_uia_sequence.yaml")


@pytest.fixture
def missing_workflow(ai_sequence_fixtures_path):
    return yaml_load(ai_sequence_fixtures_path / "missing_workflow.yaml")


@pytest.fixture
def missing_current_activity_workflow(ai_sequence_fixtures_path):
    return yaml_load(ai_sequence_fixtures_path / "missing_current_activity_workflow.yaml")


@pytest.fixture
def use_excel_online_workflow(ai_sequence_fixtures_path):
    return yaml_load(ai_sequence_fixtures_path / "use_excel_online_workflow.yaml")


@pytest.fixture
def use_excel_desktop_legacy_workflow(ai_sequence_fixtures_path):
    return yaml_load(ai_sequence_fixtures_path / "use_excel_desktop_legacy_workflow.yaml")


@pytest.fixture
def use_excel_desktop_legacy_empty_workflow(ai_sequence_fixtures_path):
    return yaml_load(ai_sequence_fixtures_path / "use_excel_desktop_legacy_empty_workflow.yaml")


@pytest.fixture
def use_excel_desktop_business_workflow(ai_sequence_fixtures_path):
    return yaml_load(ai_sequence_fixtures_path / "use_excel_desktop_business_workflow.yaml")


@pytest.fixture
def fix_expression_cases():
    return fix_expression_input


@pytest.fixture
def agent_evaluation_cases():
    return agent_evaluation_input


@pytest.fixture
def testdata_generation_input_simple_function():
    return simple_function_input


@pytest.fixture
def testdata_generation_input_function_with_multiple_params():
    return function_with_multiple_params_input


@pytest.fixture
def testdata_generation_input_yaml():
    return lm_yaml_input


@pytest.fixture
def testdata_generation_fixtures_input(testadata_generation_fixtures_path):
    return yaml_load(testadata_generation_fixtures_path / "simple_function_data.yaml")


@pytest.fixture
def workflow_postprocessing_input(workflow_generation_postprocessing_fixtures_path):
    with open(
        workflow_generation_postprocessing_fixtures_path / "generated_workflow.json",
        "r",
    ) as f:
        return json.load(f)


@pytest.fixture
def used_types_input(workflow_generation_postprocessing_fixtures_path):
    with open(
        workflow_generation_postprocessing_fixtures_path / "generated_used_types.json",
        "r",
    ) as f:
        return json.load(f)


@pytest.fixture(scope="session")
def api_activities_retriever():
    return APIActivitiesRetriever()


@pytest_asyncio.fixture(scope="session")
async def client():
    url = "http://test"
    headers = {
        "Authorization": get_testing_client_jwt_token(),
        constants.TENANT_ID_HEADER: dev_settings.TEST_TENANT_ID,
        constants.INTERNAL_ORG_ID_HEADER: dev_settings.TEST_ORGANIZATION_ID,
    }
    async with httpx.AsyncClient(transport=httpx.ASGITransport(service.app), base_url=url, headers=headers) as client:
        yield client
