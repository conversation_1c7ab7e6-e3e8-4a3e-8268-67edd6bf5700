import argparse
import asyncio
import copy

import typing_extensions as t

from services.studio._text_to_workflow.activity_config import activity_config_endpoint
from services.studio._text_to_workflow.common import activity_retriever
from services.studio._text_to_workflow.tests.integration import test_activity_config as tac
from services.studio._text_to_workflow.tests.utils.test_cases import common as tcc
from services.studio._text_to_workflow.utils import request_utils, testing


async def add_build_activity_config_demo_examples_case():
    case_names, cases = tcc.get_cases(tac._build_demo_examples_cases_path)
    for case_name in case_names:
        case_input = tcc.get_case_input_from_yaml(case_name, cases)
        case_input = copy.deepcopy(case_input)
        case_expected = await tac.get_activity_config_demo_examples_testable_result(case_input)
        cases[case_name] = {"input": copy.deepcopy(case_input), "expected": copy.deepcopy(case_expected)}
        tcc.save_cases(cases, tac._build_demo_examples_cases_path)  # NOTE: save after each case for diff inspection


async def add_build_activity_config_eval_examples_case():
    case_names, cases = tcc.get_cases(tac._build_eval_examples_cases_path)
    for case_name in case_names:
        case_input = tcc.get_case_input_from_yaml(case_name, cases)
        case_input = copy.deepcopy(case_input)
        case_expected = await tac.get_activity_config_eval_examples_testable_result(case_input)
        cases[case_name] = {"input": copy.deepcopy(case_input), "expected": copy.deepcopy(case_expected)}
        tcc.save_cases(cases, tac._build_eval_examples_cases_path)  # NOTE: save after each case for diff inspection


async def add_build_activity_config_context_case():
    case_names, cases = tcc.get_cases(tac._build_context_cases_path)
    for case_name in case_names:
        case_input = tcc.get_case_input_from_yaml(case_name, cases)
        case_input = copy.deepcopy(case_input)
        case_expected = await tac.get_activity_config_context_testable_result(case_input)
        cases[case_name] = {"input": copy.deepcopy(case_input), "expected": copy.deepcopy(case_expected)}
        tcc.save_cases(cases, tac._build_context_cases_path)  # NOTE: save after each case for diff inspection


async def add_activity_config_endpoint_case():
    case_names, cases = tcc.get_cases(tac._endpoint_cases_path)
    for case_name in case_names:
        case_input = tcc.get_case_input_from_yaml(case_name, cases)
        case_expected = await tac.get_activity_config_endpoint_testable_result(case_input)
        cases[case_name] = {"input": copy.deepcopy(case_input), "expected": copy.deepcopy(case_expected)}
        tcc.save_cases(cases, tac._endpoint_cases_path)  # NOTE: save after each case for diff inspection


async def program(type: t.Literal["demo_examples", "eval_examples", "context", "endpoint"]):
    print(f"Adding/Updating {type} case(s).")
    request_utils.set_request_context(testing.get_testing_request_context())
    await activity_config_endpoint.init()
    activity_retriever.ActivitiesRetriever()

    if type == "demo_examples":
        await add_build_activity_config_demo_examples_case()
    elif type == "eval_examples":
        await add_build_activity_config_eval_examples_case()
    elif type == "context":
        await add_build_activity_config_context_case()
    elif type == "endpoint":
        await add_activity_config_endpoint_case()
    else:
        raise ValueError("Unknown case type")


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("type", type=str, choices=["demo_examples", "eval_examples", "context", "endpoint"], help="Type of case to add.")
    args = ap.parse_args()
    asyncio.run(program(args.type))
