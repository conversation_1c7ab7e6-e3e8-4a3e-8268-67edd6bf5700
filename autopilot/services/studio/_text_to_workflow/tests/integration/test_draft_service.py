import numpy as np
import pytest

from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.utils import yaml_utils as yu
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.paths import get_wf_gen_activity_retriever_dataset_path
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import ActivitiesProposal, ActivityRetrievalResult
from services.studio._text_to_workflow.workflow_generation.services.helpers.ignored_activities_helper import get_ignored_activities_map
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_draft_service import WorkflowGenerationDraftService
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_postprocess_component import WorkflowGenerationPostProcessComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_prompt_builder_component import WorkflowGenerationPromptBuilderComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import ActivityRetrievalGeneration, WfGenDataPointV2

_draft_fixtures_path = conftest._fixtures_path / "draft_fixtures"
_draft_query_samples_path = _draft_fixtures_path / "query_samples.yaml"

_query_samples = (
    "basic_deserialization",
    "gsuite_trigger",
)


@pytest.fixture(scope="module")
def draft_query_samples() -> dict:
    return yu.yaml_load(_draft_query_samples_path)


def load_demonstration(demonstration_path: str) -> WfGenDataPointV2:
    path = get_wf_gen_activity_retriever_dataset_path() / demonstration_path
    data = yu.yaml_load(path)
    # Convert each item in the loaded data to a WfGenDataPointV2
    return WfGenDataPointV2(**data)


@pytest.mark.asyncio
@pytest.mark.parametrize("case", _query_samples)
async def test_draft_service(case: str, draft_query_samples: dict):
    test_data: dict = draft_query_samples[case]

    activities_retriever = ActivitiesRetriever()

    dynamic_activities_component = WorkflowGenerationDynamicActivitiesComponent(activities_retriever)
    postprocess_component = WorkflowGenerationPostProcessComponent(
        activities_retriever, dynamic_activities_component, get_ignored_activities_map(activities_retriever)
    )

    prompt_builder_component = WorkflowGenerationPromptBuilderComponent(activities_retriever)

    draft_service = WorkflowGenerationDraftService(activities_retriever, postprocess_component, prompt_builder_component)
    await draft_service.init_and_load()

    # prepare dummy activity retrieval result
    connections = get_connections_data()
    activity_retrieval_result = ActivityRetrievalResult(
        generation=ActivityRetrievalGeneration(
            plan="",
            ambiguities="",
            score=0,
            triggers=[],
            activities=[],
            inexistentActivities=[],
            inexistentTriggers=[],
        ),
        prompt="",
        token_usage=TokenUsage(prompt_tokens=0, completion_tokens=0),
        proposed_triggers=[],
        proposed_activities=[],
        generation_details=ActivitiesProposal(
            query_proposal_activities=[],
            workflow_proposal_activities=[],
            query_proposal_triggers=[],
            workflow_proposal_triggers=[],
        ),
        unprocessed_retrieved_triggers=[],
        unprocessed_retrieved_activities=[],
        retrieved_activities=test_data["retrieved_activities"],
        retrieved_triggers=test_data["retrieved_triggers"],
        demonstrations=[load_demonstration(demo_path) for demo_path in test_data["demonstrations"]],
        query_embedding=np.array([]),
        connections_embedding=np.array([]),
        connections_by_key={},
        ignored_activities=get_ignored_activities_map(activities_retriever)[test_data["target_framework"]],
        raw_response="",
    )

    result = await draft_service.generate_workflow_draft(
        query=test_data["query"],
        workflow=Workflow("", "", test_data["workflow"]) if "workflow" in test_data else None,
        connections=connections,
        variables=[],
        objects=[],
        mode=test_data["mode"],
        target_framework=test_data["target_framework"],
        activity_retrieval_result=activity_retrieval_result,
        localization="en",
        additional_type_definitions="",
        user_prompt_additional_instructions="",
    )

    # check the output wf contains the expected activities and triggers
    valid_wf = yu.yaml_load(result.result_workflow_details.workflow_generation["workflow_valid"])
    expected_solution: dict = test_data["solution"]
    trigger_name = valid_wf.get("trigger", {}).get("activity", None)

    if trigger_name is None:
        assert expected_solution["trigger"] is None
    else:
        assert trigger_name == expected_solution["trigger"]

    for i, predicted_node in enumerate(valid_wf["workflow"]):
        expected_node = expected_solution["activities"][i]
        assert predicted_node["activity"] == expected_node
