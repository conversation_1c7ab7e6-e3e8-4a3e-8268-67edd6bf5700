import pytest

import services.studio._text_to_workflow.tests.conftest as conftest
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation.workflow_generation_postprocess_component import FIXYAML_TASK

workflow_generation_fixyaml_fixtures_path = conftest._fixtures_path / "workflow_generation_fixtures" / "fixyaml"

fixyaml_testcases = []
for path in sorted(workflow_generation_fixyaml_fixtures_path.glob("*.yaml")):
    fixyaml_testcases.append(yaml_load(path))


@pytest.mark.asyncio
@pytest.mark.parametrize("fix_yaml_testcase", fixyaml_testcases)
async def test_fix_yaml(fix_yaml_testcase):
    mode = fix_yaml_testcase["mode"]
    workflow_str = fix_yaml_testcase["workflow"]
    errors = fix_yaml_testcase["errors"]
    model = ModelManager().get_llm_model("workflow_generation_gemini_model", ConsumingFeatureType.WORKFLOW_GENERATION)
    predicted_worklow = (await FIXYAML_TASK.run(model, {"workflow": workflow_str, "errors": errors}, mode))["fixed_workflow"]
    expected_workflow = fix_yaml_testcase["fixed_workflow"]
    assert predicted_worklow == expected_workflow
