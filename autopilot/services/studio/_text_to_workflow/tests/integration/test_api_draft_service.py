import json
from unittest.mock import patch

import numpy as np
import pytest

from services.studio._text_to_workflow.api_workflow.services.api_wf_draft_service import ApiWfDraftService
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.generate_http_requests_schema import generate_http_requests_schema
from services.studio._text_to_workflow.common.api_workflow.post_generation_processing_service import ApiWfPostGenerationProcessingService
from services.studio._text_to_workflow.common.api_workflow.post_generation_schema import (
    ApiWorkflowDraftResult,
    ConnectorIntegrationWithMetadata,
    PostGenApiWorkflow,
    PostGenForEach,
)
from services.studio._text_to_workflow.common.api_workflow.schema import (
    ApiWorkflow,
    ConnectorIntegration,
    ForEach,
)
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.common.schema import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from services.studio._text_to_workflow.models.model_manager import <PERSON><PERSON><PERSON><PERSON>
from services.studio._text_to_workflow.tests import conftest
from services.studio._text_to_workflow.tests.integration.utils.dynamic_activities_mocks import (
    load_api_workflow_datapoints,
    mock_dynamic_activities_config_map,
)
from services.studio._text_to_workflow.utils import yaml_utils as yu
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import ActivitiesProposal, APIActivityRetrievalResult
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import ActivityRetrievalGeneration, UndocumentedHttpRequest

_api_draft_fixtures_path = conftest._fixtures_path / "api_draft_fixtures"
_api_draft_query_samples_path = _api_draft_fixtures_path / "query_samples.yaml"

_query_samples = (
    "basic_api_workflow",
    "api_integration_activity_workflow",
    "response_activity_workflow",
    "basic_skip_to_the_good_bit_edit_scenario",
    "http_request_web_search_scenario",
)


@pytest.fixture(scope="module")
def api_draft_query_samples() -> dict:
    return yu.yaml_load(_api_draft_query_samples_path)


@pytest.fixture
def embedding_model():
    """Fixture to provide an embedding model."""
    return ModelManager().get_embeddings_model("activities_embedding_model")


async def _generate_workflow_draft(
    api_activities_retriever: APIActivitiesRetriever,
    retrieved_activities: list[str],
    inexistent_activities: list[str],
    existing_workflow_json: str | None,
    mode: ActivitiesGenerationMode,
    query: str,
    undocumentedHttpRequests: list[UndocumentedHttpRequest],
) -> ApiWorkflowDraftResult:
    """Generate a workflow draft using the draft service."""
    post_gen_processing_service = ApiWfPostGenerationProcessingService(api_activities_retriever)
    draft_service = ApiWfDraftService(api_activities_retriever, post_gen_processing_service)

    # Get connections data
    connections = get_connections_data()

    # Now also mock the augment_type_definitions_for_dynamic_activities method
    with patch.object(WorkflowGenerationDynamicActivitiesComponent, "augment_type_definitions_for_dynamic_activities") as mock_augment:
        # Set the return value to match the expected tuple format (jit_types, activities_config_map)
        mock_augment.return_value = ({}, mock_dynamic_activities_config_map())

        activity_retrieval_result: APIActivityRetrievalResult = APIActivityRetrievalResult(
            retrieved_activities=retrieved_activities,
            demonstrations=load_api_workflow_datapoints(),
            generation=ActivityRetrievalGeneration(
                plan="",
                ambiguities="",
                score=0,
                triggers=[],
                activities=[],
                inexistentActivities=inexistent_activities,
                inexistentTriggers=[],
                undocumentedHttpRequests=undocumentedHttpRequests,
            ),
            ignored_activities=set(),
            proposed_activities=[],
            prompt="",
            token_usage=TokenUsage(
                prompt_tokens=0,
                completion_tokens=0,
                total_tokens=0,
            ),
            unprocessed_retrieved_activities=[],
            query_embedding=np.array([]),
            connections_embedding=np.array([]),
            connections_by_key={},
            generation_details=ActivitiesProposal(
                query_proposal_activities=[],
                workflow_proposal_activities=[],
                query_proposal_triggers=[],
                workflow_proposal_triggers=[],
            ),
            raw_response="",
        )

        # if the test data has an existing workflow, we should use it
        existing_workflow = None
        if existing_workflow_json:
            raw_existing_workflow = json.loads(existing_workflow_json)
            existing_workflow = ApiWorkflow.model_validate(raw_existing_workflow)

        undocumented_http_requests = await generate_http_requests_schema(activity_retrieval_result.generation.undocumentedHttpRequests)
        # Generate workflow draft
        result = await draft_service.generate_workflow_draft(
            workflow=existing_workflow,
            mode=mode,
            query=query,
            connections=connections,
            expression_language="js",
            activity_retrieval_result=activity_retrieval_result,
            undocumented_http_requests=undocumented_http_requests,
        )

        return result


@pytest.mark.asyncio
@pytest.mark.parametrize("case", _query_samples)
async def test_api_draft_service(case: str, api_draft_query_samples: dict, api_activities_retriever: APIActivitiesRetriever):
    test_data: dict = api_draft_query_samples[case]

    # Generate workflow draft
    result = await _generate_workflow_draft(
        api_activities_retriever=api_activities_retriever,
        retrieved_activities=test_data["retrieved_activities"],
        inexistent_activities=test_data.get("inexistent_activities", []),
        existing_workflow_json=test_data["existing_workflow"],
        mode=test_data["mode"],
        query=test_data["query"],
        undocumentedHttpRequests=[
            UndocumentedHttpRequest(
                name=item["name"],
                description=item["description"],
                provider=item["provider"],
            )
            for item in test_data.get("undocumented_http_requests", [])
        ],
    )

    # Check that the draft was generated successfully
    assert result is not None
    assert result.api_workflow_details is not None

    workflow = result.api_workflow_details.generated_workflow
    # Check that the raw workflow was generated
    assert workflow is not None

    workflow_activities = [activity.activity for activity in workflow.root.do]
    for expected_activity in test_data["expected_solution_activities"]:
        assert expected_activity in workflow_activities


@pytest.mark.asyncio
async def test_for_each_with_return_value_is_generated_correctly(api_activities_retriever: APIActivitiesRetriever):
    # Generate workflow draft
    result = await _generate_workflow_draft(
        api_activities_retriever=api_activities_retriever,
        retrieved_activities=[
            "UiPath.IntegrationService.Activities.Runtime.Activities.AsanaCreate_Task",
            "UiPath.IntegrationService.Activities.Runtime.Activities.FreshdeskSearch_Tickets",
        ],
        inexistent_activities=[],
        existing_workflow_json=None,
        mode="workflow",
        query="For each FreskDesk ticket opened today, create a corresponding task in Asana. Finally, use a Response activity to return the ids of the created tasks.",
        undocumentedHttpRequests=[],
    )

    assert isinstance(result.api_workflow_details.processed_workflow, PostGenApiWorkflow)
    root = result.api_workflow_details.processed_workflow.root.do

    # check the activities are generated correctly
    assert isinstance(root[0], ConnectorIntegrationWithMetadata)
    assert root[0].activity == "FreshdeskSearch_Tickets"
    assert isinstance(root[1], PostGenForEach)
    assert isinstance(root[1].do[0], ConnectorIntegrationWithMetadata)
    assert root[1].do[0].activity == "AsanaCreate_Task"

    # check that the output of the "ForEach" activity is accessed in the workflow through the "results" property
    assert f"$context.outputs.{root[1].id}.results" in result.raw_model_prediction


@pytest.mark.asyncio
async def test_same_category_activities_workflow(api_activities_retriever: APIActivitiesRetriever):
    # Generate workflow draft
    result = await _generate_workflow_draft(
        api_activities_retriever=api_activities_retriever,
        retrieved_activities=[
            "UiPath.IntegrationService.Activities.Runtime.Activities.GmailGet_Email_List",
            "UiPath.IntegrationService.Activities.Runtime.Activities.Google_DriveUpload_Files",
            "UiPath.IntegrationService.Activities.Runtime.Activities.GmailSend_Email",
            "UiPath.IntegrationService.Activities.Runtime.Activities.Google_DriveCreate_Folder",
            "UiPath.IntegrationService.Activities.Runtime.Activities.Microsoft_Outlook_365_HTTP_Request",
            "UiPath.IntegrationService.Activities.Runtime.Activities.MailerLite_HTTP_Request",
            "UiPath.IntegrationService.Activities.Runtime.Activities.Google_DriveGet_File_or_Folder_List",
            "UiPath.IntegrationService.Activities.Runtime.Activities.GmailDelete_Email",
            "UiPath.IntegrationService.Activities.Runtime.Activities.Gmail_HTTP_Request",
            "UiPath.IntegrationService.Activities.Runtime.Activities.Microsoft_Outlook_365Get_Email_List",
            "UiPath.IntegrationService.Activities.Runtime.Activities.MailerLiteCreate_or_Update_Subscriber",
        ],
        inexistent_activities=[],
        existing_workflow_json=None,
        mode="workflow",
        query="Create an API workflow to search in my Gmails and make a list of my subscription emails. For each email create a folder in drive",
        undocumentedHttpRequests=[],
    )

    assert result is not None
    assert result.api_workflow_details is not None

    workflow = result.api_workflow_details.generated_workflow
    # Check that the raw workflow was generated
    assert workflow is not None

    assert workflow.root.do[0].activity == "GmailGet_Email_List"

    assert workflow.root.do[1].activity == "ForEach"
    assert isinstance(workflow.root.do[1], ForEach)
    assert workflow.root.do[1].do is not None
    assert isinstance(workflow.root.do[1].do[0], ConnectorIntegration)
    assert workflow.root.do[1].do[0].activity == "Google_DriveCreate_Folder"
