import base64

import pytest

from services.studio._text_to_workflow.bpmn_generation import bpmn_generation_endpoint
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnAssistantRequest,
    BpmnTelemetryRequest,
    GenerateBpmnChatRequest,
    GenerateBpmnRequest,
    TelemetryType,
    TelemetryUserAction,
    Tool,
)
from services.studio._text_to_workflow.tests import conftest

_bpmn_fixtures_path = conftest._fixtures_path / "bpmn_fixtures"
CURRENT_BPMN = """<?xml version="1.0" encoding="UTF-8"?>
                            <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="cd63ed2b">
                            <bpmn:process id="Process_1" isExecutable="false">
                                <bpmn:startEvent id="Event_start">
                                <bpmn:extensionElements>
                                    <uipath:entryPointId value="234f4618-b4da-458c-8fd5-645b12886704"/>
                                </bpmn:extensionElements>
                                <bpmn:outgoing>edge_9zW1Ad</bpmn:outgoing>
                                </bpmn:startEvent>
                                <bpmn:task id="Activity_GL0jrf" name="Parse documents">
                                <bpmn:incoming>edge_9zW1Ad</bpmn:incoming>
                                <bpmn:outgoing>edge_G2xDF4</bpmn:outgoing>
                                </bpmn:task>
                                <bpmn:endEvent id="Event_EFZ6qv">
                                <bpmn:incoming>edge_G2xDF4</bpmn:incoming>
                                </bpmn:endEvent>
                                <bpmn:sequenceFlow id="edge_9zW1Ad" sourceRef="Event_start" targetRef="Activity_GL0jrf"/>
                                <bpmn:sequenceFlow id="edge_G2xDF4" sourceRef="Activity_GL0jrf" targetRef="Event_EFZ6qv"/>
                            </bpmn:process>
                            </bpmn:definitions>"""


@pytest.mark.asyncio
async def test_bpmn_generation():
    request: GenerateBpmnRequest = {
        "userRequest": "Send an email if there is a lead in salesforce",
        "currentBpmn": None,
    }

    out = (await bpmn_generation_endpoint.generate_bpmn(request))["result"]
    assert out.startswith('<?xml version="1.0" encoding="UTF-8" standalone="no"?>\n<definitions\n')


@pytest.mark.asyncio
async def test_bpmn_generation_junk_filter():
    request: GenerateBpmnRequest = {
        "userRequest": "Tell me a joke",
        "currentBpmn": None,
    }

    out = (await bpmn_generation_endpoint.generate_bpmn(request))["prompt_class"]
    assert out == "JUNK"


@pytest.mark.asyncio
async def test_bpmn_generation_chat():
    request: GenerateBpmnChatRequest = {
        "userRequest": "Create a workflow for onboarding new employees",
        "currentBpmn": None,
    }

    out = await bpmn_generation_endpoint.generate_bpmn_chat(request, validate=False)

    assert out.get("explanation")
    assert out.get("add")


@pytest.mark.asyncio
async def test_bpmn_generation_chat_with_validation():
    request: GenerateBpmnChatRequest = {
        "userRequest": "Create a workflow for onboarding new employees",
        "currentBpmn": None,
    }

    out = await bpmn_generation_endpoint.generate_bpmn_chat(request, validate=True)

    assert out.get("explanation")
    assert out.get("add")


@pytest.mark.asyncio
async def test_bpmn_generation_assistant():
    request: GenerateBpmnChatRequest = {
        "userRequest": "Create a workflow for handling customer complaints",
        "currentBpmn": None,
    }

    out = await bpmn_generation_endpoint.generate_bpmn_assistant(request, validate=False)

    assert out.get("valid")
    assert out.get("results")[0].get("tool") == Tool.EDIT_BPMN
    assert out.get("results")[0].get("explanation")


@pytest.mark.asyncio
async def test_bpmn_generation_assistant_with_validation():
    request: GenerateBpmnChatRequest = {
        "userRequest": "Rename Parse documents to Prepare and parse documents",
        "currentBpmn": CURRENT_BPMN,
    }

    out = await bpmn_generation_endpoint.generate_bpmn_assistant(request, validate=True)

    assert out.get("valid")
    assert out.get("results")[0].get("tool") == Tool.EDIT_BPMN
    assert out.get("results")[0].get("explanation")


@pytest.mark.asyncio
async def test_bpmn_generation_telemetry():
    request: BpmnTelemetryRequest = {
        "sourceType": TelemetryType.BPMN,
        "userAction": [TelemetryUserAction.ACCEPT],
        "request": "Split the 'Perform Quality Check' task into two separate user tasks: 'Initial Visual Inspection' followed by 'Detailed Technical Inspection",
        "response": """{
                            "valid": true,
                            "results": [
                                {
                                    "tool": "edit-bpmn",
                                    "explanation": "I've split the 'Perform Quality Check' task into two sequential user tasks: 'Initial Visual Inspection' followed by 'Detailed Technical Inspection', maintaining the proper flow connections.",
                                    "title": "Split Quality Check Task",
                                    "add": [
                                        {
                                            "type": "bpmn:serviceTask",
                                            "id": "Task_visual_inspection",
                                            "parentId": "Process_1",
                                            "name": "Initial Visual Inspection"
                                        },
                                        {
                                            "type": "bpmn:serviceTask",
                                            "id": "Task_technical_inspection",
                                            "parentId": "Process_1",
                                            "name": "Detailed Technical Inspection"
                                        },
                                        {
                                            "type": "bpmn:sequenceFlow",
                                            "id": "Flow_visual_technical",
                                            "source": "Task_visual_inspection",
                                            "target": "Task_technical_inspection",
                                            "parentId": "Process_1"
                                        },
                                        {
                                            "type": "bpmn:sequenceFlow",
                                            "id": "Flow_receive_visual",
                                            "source": "Task_receive_goods",
                                            "target": "Task_visual_inspection",
                                            "parentId": "Process_1"
                                        },
                                        {
                                            "type": "bpmn:sequenceFlow",
                                            "id": "Flow_technical_decision",
                                            "source": "Task_technical_inspection",
                                            "target": "Gateway_quality_decision",
                                            "parentId": "Process_1"
                                        }
                                    ],
                                    "update": [],
                                    "delete": [
                                        {
                                            "type": "bpmn:userTask",
                                            "id": "Task_quality_check"
                                        },
                                        {
                                            "type": "bpmn:sequenceFlow",
                                            "id": "Flow_receive_quality"
                                        },
                                        {
                                            "type": "bpmn:sequenceFlow",
                                            "id": "Flow_quality_decision"
                                        }
                                    ]
                                }
                            ]
                        }""",
        "currentBpmn": """<?xml version="1.0" encoding="UTF-8"?>
                            <bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:uipath="http://uipath.org/schema/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="sample-diagram" targetNamespace="http://www.omg.org/spec/BPMN/20100524/MODEL" exporter="UiPath Studio Web (https://uipath.com)" exporterVersion="505a97d2">
                            <bpmn:process id="Process_1" isExecutable="false">
                                <bpmn:startEvent id="Event_start">
                                <bpmn:extensionElements>
                                    <uipath:entryPointId value="2afffd27-bee2-4e72-a8ab-940f78467914"/>
                                </bpmn:extensionElements>
                                <bpmn:outgoing>Flow_start_requisition</bpmn:outgoing>
                                </bpmn:startEvent>
                                <bpmn:userTask id="Task_create_requisition" name="Create Purchase Requisition">
                                <bpmn:incoming>Flow_start_requisition</bpmn:incoming>
                                <bpmn:outgoing>Flow_requisition_approval</bpmn:outgoing>
                                </bpmn:userTask>
                                <bpmn:exclusiveGateway id="Gateway_approval_decision" name="Requisition Approved?">
                                <bpmn:incoming>Flow_requisition_approval</bpmn:incoming>
                                <bpmn:outgoing>Flow_approval_yes</bpmn:outgoing>
                                <bpmn:outgoing>Flow_approval_no</bpmn:outgoing>
                                </bpmn:exclusiveGateway>
                                <bpmn:userTask id="Task_create_po" name="Create Purchase Order">
                                <bpmn:incoming>Flow_approval_yes</bpmn:incoming>
                                <bpmn:outgoing>Flow_po_send</bpmn:outgoing>
                                </bpmn:userTask>
                                <bpmn:sendTask id="Task_send_po" name="Send PO to Supplier">
                                <bpmn:incoming>Flow_po_send</bpmn:incoming>
                                <bpmn:outgoing>Flow_send_receive</bpmn:outgoing>
                                </bpmn:sendTask>
                                <bpmn:userTask id="Task_receive_goods" name="Receive Goods">
                                <bpmn:incoming>Flow_send_receive</bpmn:incoming>
                                <bpmn:outgoing>Flow_receive_quality</bpmn:outgoing>
                                </bpmn:userTask>
                                <bpmn:userTask id="Task_quality_check" name="Perform Quality Check">
                                <bpmn:incoming>Flow_receive_quality</bpmn:incoming>
                                <bpmn:outgoing>Flow_quality_decision</bpmn:outgoing>
                                </bpmn:userTask>
                                <bpmn:exclusiveGateway id="Gateway_quality_decision" name="Quality Check Passed?">
                                <bpmn:incoming>Flow_quality_decision</bpmn:incoming>
                                <bpmn:outgoing>Flow_quality_fail</bpmn:outgoing>
                                <bpmn:outgoing>Flow_quality_pass</bpmn:outgoing>
                                </bpmn:exclusiveGateway>
                                <bpmn:userTask id="Task_return_goods" name="Return Goods to Supplier">
                                <bpmn:incoming>Flow_quality_fail</bpmn:incoming>
                                <bpmn:outgoing>Flow_return_end</bpmn:outgoing>
                                </bpmn:userTask>
                                <bpmn:endEvent id="Event_return_end" name="Goods Returned">
                                <bpmn:incoming>Flow_return_end</bpmn:incoming>
                                </bpmn:endEvent>
                                <bpmn:userTask id="Task_notify_rejection" name="Notify Requisition Rejection">
                                <bpmn:incoming>Flow_approval_no</bpmn:incoming>
                                <bpmn:outgoing>Flow_rejection_end</bpmn:outgoing>
                                </bpmn:userTask>
                                <bpmn:endEvent id="Event_rejection_end" name="Requisition Rejected">
                                <bpmn:incoming>Flow_rejection_end</bpmn:incoming>
                                </bpmn:endEvent>
                                <bpmn:receiveTask id="Task_receive_invoice" name="Receive Invoice">
                                <bpmn:incoming>Flow_quality_pass</bpmn:incoming>
                                <bpmn:incoming>Flow_dispute_receive</bpmn:incoming>
                                <bpmn:outgoing>Flow_invoice_verify</bpmn:outgoing>
                                </bpmn:receiveTask>
                                <bpmn:userTask id="Task_verify_invoice" name="Verify Invoice">
                                <bpmn:incoming>Flow_invoice_verify</bpmn:incoming>
                                <bpmn:outgoing>Flow_verify_decision</bpmn:outgoing>
                                </bpmn:userTask>
                                <bpmn:exclusiveGateway id="Gateway_invoice_decision" name="Invoice Correct?">
                                <bpmn:incoming>Flow_verify_decision</bpmn:incoming>
                                <bpmn:outgoing>Flow_invoice_no</bpmn:outgoing>
                                <bpmn:outgoing>Flow_invoice_yes</bpmn:outgoing>
                                </bpmn:exclusiveGateway>
                                <bpmn:userTask id="Task_dispute_invoice" name="Dispute Invoice with Supplier">
                                <bpmn:incoming>Flow_invoice_no</bpmn:incoming>
                                <bpmn:outgoing>Flow_dispute_receive</bpmn:outgoing>
                                </bpmn:userTask>
                                <bpmn:userTask id="Task_process_payment" name="Process Payment">
                                <bpmn:incoming>Flow_invoice_yes</bpmn:incoming>
                                <bpmn:outgoing>Flow_payment_end</bpmn:outgoing>
                                </bpmn:userTask>
                                <bpmn:endEvent id="Event_process_end" name="Payment Complete">
                                <bpmn:incoming>Flow_payment_end</bpmn:incoming>
                                </bpmn:endEvent>
                                <bpmn:sequenceFlow id="Flow_start_requisition" sourceRef="Event_start" targetRef="Task_create_requisition"/>
                                <bpmn:sequenceFlow id="Flow_requisition_approval" sourceRef="Task_create_requisition" targetRef="Gateway_approval_decision"/>
                                <bpmn:sequenceFlow id="Flow_approval_yes" name="Yes" sourceRef="Gateway_approval_decision" targetRef="Task_create_po"/>
                                <bpmn:sequenceFlow id="Flow_approval_no" name="No" sourceRef="Gateway_approval_decision" targetRef="Task_notify_rejection"/>
                                <bpmn:sequenceFlow id="Flow_po_send" sourceRef="Task_create_po" targetRef="Task_send_po"/>
                                <bpmn:sequenceFlow id="Flow_send_receive" sourceRef="Task_send_po" targetRef="Task_receive_goods"/>
                                <bpmn:sequenceFlow id="Flow_receive_quality" sourceRef="Task_receive_goods" targetRef="Task_quality_check"/>
                                <bpmn:sequenceFlow id="Flow_quality_decision" sourceRef="Task_quality_check" targetRef="Gateway_quality_decision"/>
                                <bpmn:sequenceFlow id="Flow_quality_fail" name="No" sourceRef="Gateway_quality_decision" targetRef="Task_return_goods"/>
                                <bpmn:sequenceFlow id="Flow_quality_pass" name="Yes" sourceRef="Gateway_quality_decision" targetRef="Task_receive_invoice"/>
                                <bpmn:sequenceFlow id="Flow_return_end" sourceRef="Task_return_goods" targetRef="Event_return_end"/>
                                <bpmn:sequenceFlow id="Flow_rejection_end" sourceRef="Task_notify_rejection" targetRef="Event_rejection_end"/>
                                <bpmn:sequenceFlow id="Flow_invoice_verify" sourceRef="Task_receive_invoice" targetRef="Task_verify_invoice"/>
                                <bpmn:sequenceFlow id="Flow_verify_decision" sourceRef="Task_verify_invoice" targetRef="Gateway_invoice_decision"/>
                                <bpmn:sequenceFlow id="Flow_invoice_no" name="No" sourceRef="Gateway_invoice_decision" targetRef="Task_dispute_invoice"/>
                                <bpmn:sequenceFlow id="Flow_invoice_yes" name="Yes" sourceRef="Gateway_invoice_decision" targetRef="Task_process_payment"/>
                                <bpmn:sequenceFlow id="Flow_dispute_receive" sourceRef="Task_dispute_invoice" targetRef="Task_receive_invoice"/>
                                <bpmn:sequenceFlow id="Flow_payment_end" sourceRef="Task_process_payment" targetRef="Event_process_end"/>
                            </bpmn:process>
                            </bpmn:definitions>""",
    }

    response = await bpmn_generation_endpoint.generate_bpmn_business_telemetry(request)

    assert response is None  # Assuming the function does not return a value


@pytest.mark.asyncio
async def test_bpmn_generation_qa():
    request: BpmnAssistantRequest = {
        "userRequest": "What can you do?",
        "currentBpmn": None,
    }
    response = await bpmn_generation_endpoint.generate_bpmn_assistant(request)
    assert response["valid"]
    result = response["results"][0]
    assert result["tool"] == Tool.QA
    assert result["explanation"].startswith(
        "I can help to streamline the creation and implementation of BPMN models using natural language, including the following capabilities: creating and editing BPMN models; general Q&A about UiPath, RPA, BPMN; explain, validate, analyze, recommend optimization for the current model; suggestions for configuring activities such as RPA workflow, agent, connectors."
    )


@pytest.mark.skip(reason="max_retries inputs are not permitted in LLMBedrockModel")
async def test_bpmn_generation_from_image():
    with open(_bpmn_fixtures_path / "bpmn_flow.png", "rb") as file:
        file_content = file.read()
        base64_encoded = base64.b64encode(file_content)
        base64_string = base64_encoded.decode("utf-8")

    request: BpmnAssistantRequest = {
        "userRequest": "Convert the image to BPMN.",
        "image": ("data:image/png;base64," + base64_string).encode("utf-8"),
        "currentBpmn": None,
    }
    response = await bpmn_generation_endpoint.generate_bpmn_assistant(request)
    assert response["valid"]
    result = response["results"][0]
    assert result["tool"] == Tool.CONVERT_IMAGE
    assert len(result["update"]) == 0
    assert len(result["delete"]) == 0
    assert len(result["add"]) > 1


@pytest.mark.asyncio
async def test_bpmn_generation_patch():
    request: BpmnAssistantRequest = {
        "userRequest": "Generate a purchase to pay process",
        "currentBpmn": None,
        # "modelTypeOverride": {
        #     "model_type": ModelType.Anthropic,
        #     "tool": Tool.EDIT_BPMN,
        # },
    }
    response = await bpmn_generation_endpoint.generate_bpmn_assistant(request)
    assert response["valid"]
    result = response["results"][0]
    assert result["tool"] == Tool.EDIT_BPMN
    assert len(result["update"]) == 0
    assert len(result["delete"]) == 0
    assert len(result["add"]) > 1


@pytest.mark.asyncio
async def test_bpmn_generation_patch_with_existing_bpmn():
    request: BpmnAssistantRequest = {
        "userRequest": "Generate invoice processing workflow",
        "currentBpmn": CURRENT_BPMN,
        # "modelTypeOverride": {
        #     "model_type": ModelType.Anthropic,
        #     "tool": Tool.EDIT_BPMN,
        # },
    }
    response = await bpmn_generation_endpoint.generate_bpmn_assistant(request)
    assert response["valid"]
    result = response["results"][0]
    assert result["tool"] == Tool.EDIT_BPMN
    assert len(result["update"]) == 0
    assert len(result["add"]) > 1
    assert len(result["delete"]) > 0


@pytest.mark.skip(reason="max_retries inputs are not permitted in LLMBedrockModel")
async def test_bpmn_generation_extension():
    request: BpmnAssistantRequest = {
        "userRequest": "Find an agent for Parse documents",
        "currentBpmn": CURRENT_BPMN,
        "extensionDataOverride": {
            "processes": [
                {"id": "1", "name": "Process documents", "type": "Orchestrator.StartAgentJob"},
                {"id": "2", "name": "Documents process", "type": "Orchestrator.StartJob"},
                {"id": "3", "name": "Reimburse", "type": "Orchestrator.StartAgentJob"},
            ],
        },
    }
    response = await bpmn_generation_endpoint.generate_bpmn_assistant(request)
    assert response["valid"]
    result = response["results"][0]
    assert result["tool"] == Tool.EXTENSION
    task_extensions = result["update"]
    assert len(task_extensions) == 1
    assert task_extensions[0].id == "Activity_GL0jrf"
    assert task_extensions[0].type == "bpmn:serviceTask"

    suggestions = task_extensions[0].data.suggestions
    assert len(suggestions) == 1
    assert suggestions[0].id == "1"
    assert suggestions[0].name == "Process documents"
    assert suggestions[0].type == "Orchestrator.StartAgentJob"


@pytest.mark.asyncio
async def test_bpmn_generation_telemetry_multiple_choice_extensions():
    request: BpmnTelemetryRequest = {
        "sourceType": TelemetryType.EXTENSIONS,
        "userAction": [TelemetryUserAction.ACCEPT],
        "request": "User requested an email connector. Displayed options for selection.",
        "response": """{
            \"valid\": true,
            \"results\": [
                {
                    \"tool\": \"edit-bpmn\",
                    \"explanation\": \"User selected the Gmail connector from the displayed email options.\",
                    \"title\": \"Connector Selection\",
                    \"add\": [],
                    \"update\": [],
                    \"delete\": []
                }
            ]
        }""",
        "currentBpmn": """<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="Start"/>
    <bpmn:userTask id="UserTask_1" name="Select Email Connector">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_1" name="End">
      <bpmn:incoming>Flow_2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="UserTask_1"/>
    <bpmn:sequenceFlow id="Flow_2" sourceRef="UserTask_1" targetRef="EndEvent_1"/>
  </bpmn:process>
</bpmn:definitions>""",
        "customData": {"optionsDisplayed": ["Gmail", "Outlook", "Yahoo Mail"], "optionSelected": "Gmail"},
    }

    response = await bpmn_generation_endpoint.generate_bpmn_business_telemetry(request)
    assert response is None  # The function should complete without error
