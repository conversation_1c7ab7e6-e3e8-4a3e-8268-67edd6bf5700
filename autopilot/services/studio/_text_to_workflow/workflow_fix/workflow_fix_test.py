import argparse
import collections
import copy
import pathlib
import time
import traceback
from typing import Dict, List, Union

import openai
import tqdm

from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.common.state_store import clear_retrievers_cache
from services.studio._text_to_workflow.utils import paths
from services.studio._text_to_workflow.utils.request_utils import set_request_context
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.utils.workflow_utils import get_pled, get_tld_rpa
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_fix.dataset_creation.create_from_wfgen import refresh_token
from services.studio._text_to_workflow.workflow_fix.workflow_fix_schema import (
    WorkflowFixDatapoint,
    WorkflowFixFlairType,
    WorkflowFixPrediction,
    WorkflowFixResponse,
)
from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WorkflowFixTask


def get_eval_prediction_path(name: str) -> pathlib.Path:
    return paths.get_workflow_fix_dataset_path() / "predictions" / name


async def initialize() -> WorkflowFixTask:
    await refresh_token()
    set_request_context(get_testing_request_context())
    return await WorkflowFixTask().load()


def load_datapoints(subset: str | None = None) -> Dict[pathlib.Path, WorkflowFixDatapoint]:
    dataset_path = paths.get_workflow_fix_dataset_path()
    if subset is not None:
        dataset_path = dataset_path / subset
    return {p: {"name": p.stem} | yaml_load(p) for p in dataset_path.rglob("*.yaml")}


async def wrapped_predict(
    workflow_fix_task: WorkflowFixTask,
    workflow_as_str: str,
    errors: List[str],
    target_framework: TargetFramework,
    flair_type: WorkflowFixFlairType = WorkflowFixFlairType.Default,
    identifier: str | None = None,
) -> Union[WorkflowFixResponse, None]:
    response = {"exceptions": []}
    for _ in range(3):
        try:
            connections = get_connections_data()
            start = time.time()
            endpoint_response = await workflow_fix_task.run_fix_workflow(
                workflow_as_str,
                errors,
                "",
                connections,
                target_framework,
                None,
                flair_type,
                identifier=identifier,
            )
            response.update(endpoint_response)
            response["latency"] = time.time() - start
            return response
        except openai.AuthenticationError:
            print("Attempting to refresh token")
            await refresh_token()
            continue
        except Exception as e:
            print(f"Failed to fix {e}")
            traceback.print_exc()
            response["exceptions"].append({"error": str(e), "traceback": traceback.format_exc(), "latency": time.time() - start})
            continue
    print("No output")
    return None


async def gather_predictions(
    workflow_fix_task: WorkflowFixTask,
    datapoints: Dict[pathlib.Path, WorkflowFixDatapoint],
    flair: WorkflowFixFlairType = WorkflowFixFlairType.SoftUniDiff,
) -> Dict[pathlib.Path, WorkflowFixPrediction]:
    predictions = {}
    datapoints_so_far = {}  # for intermediate evaluations
    for path, datapoint in tqdm.tqdm(datapoints.items(), desc="Gathering predictions"):
        # if path.as_posix() != "/workspace/data/Autopilot.Samples/Dataset/FixActivities/train/Design_MissingPropertySetter.yaml":  # special case - hard to resolve
        #     continue
        datapoints_so_far[path] = datapoint
        workflow_as_str = yaml_dump(datapoint["process_existing"])
        start = time.time()
        response = await wrapped_predict(
            workflow_fix_task,
            workflow_as_str,
            datapoint["errors"],
            datapoint.get("target_framework", "Windows"),
            flair,
            identifier=path.stem,
        )
        if response:
            prediction_result: WorkflowFixPrediction = response  # type: ignore
            prediction_result["latency"] = time.time() - start
            if "fixedWorkflow" in response:
                prediction_result["fixedWorkflow"] = yaml_load(response["fixedWorkflow"])
            predictions[path] = prediction_result
        else:
            print(f"WARNING! Failed to predict {path}")
        if len(datapoints_so_far) % 5 == 0:
            calculate_scores(datapoints_so_far, predictions)
    return predictions


def dump_predictions(
    predictions: Dict[pathlib.Path, WorkflowFixPrediction],
    datapoints: Dict[pathlib.Path, WorkflowFixDatapoint],
    name: str,
) -> None:
    dump_location = get_eval_prediction_path(name)
    # if dump_location.exists():
    #     dump_location = paths.get_workflow_fix_dataset_path() / "predictions" / f"{name}_{uuid.uuid4()[:10]}"
    dump_location.mkdir(parents=True, exist_ok=True)
    for path, prediction in predictions.items():
        datapoint_with_prediction = copy.deepcopy(datapoints[path])
        datapoint_with_prediction["prediction"] = prediction
        prediction_path = dump_location / f"{datapoint_with_prediction['name']}.yaml"
        yaml_dump(datapoint_with_prediction, prediction_path)


def calculate_scores(datapoints: Dict[pathlib.Path, WorkflowFixDatapoint], predictions: Dict[pathlib.Path, WorkflowFixPrediction]) -> Dict[str, list]:
    scores = collections.defaultdict(list)
    for path, datapoint in datapoints.items():
        existing_workflow = datapoint["process_existing"]
        ground_truth = datapoint["process_fixed"]
        prediction = predictions.get(path)
        if prediction is not None and "fixedWorkflow" in prediction:
            fixed_workflow = prediction["fixedWorkflow"]
            # scores.append(get_pled(ground_truth, fixed_workflow))
            scores["triangle_lev"].append(get_tld_rpa(existing_workflow, ground_truth, fixed_workflow))
            # scores["ted"].append(get_ted(existing_workflow, fixed_workflow))
            scores["pled"].append(get_pled(existing_workflow, fixed_workflow))
            scores["latency"].append(prediction["latency"])
        else:
            scores["triangle_lev"].append(0)
            # scores["ted"].append(0)
            scores["pled"].append(0)
    print(f"Evaluation on {len(datapoints)} datapoints")
    for metric_name, values in scores.items():
        avg = sum(values) / len(values)
        median = sorted(values)[len(values) // 2]
        print(f"{metric_name}: avg {avg} median {median}")
    return scores


async def main(args) -> None:
    datapoints = load_datapoints(args.subset)
    if args.evaluate_only:
        # datapoint_name_to_path = {k.stem: k for k, v in datapoints.items()}
        datapoint_name_to_path = {v["name"]: k for k, v in datapoints.items()}
        datapoints_with_predictions = {datapoint_name_to_path[p.stem]: yaml_load(p) for p in get_eval_prediction_path(args.name).rglob("*.yaml")}
        predictions = {k: v["prediction"] for k, v in datapoints_with_predictions.items()}
    else:
        if args.build:
            # clearing the cache will force the retriever to be rebuilt
            clear_retrievers_cache()
        workflow_fix_task = await initialize()
        predictions = await gather_predictions(workflow_fix_task, datapoints, args.flair)
        dump_predictions(predictions, datapoints, args.name)

    _scores_results = calculate_scores(datapoints, predictions)

    if args.notify_slack:
        try:
            from experimental.utils.slack import send_slack_notification

            send_slack_notification(args)
        except Exception as e:
            print(f"Failed to send slack notification: {e}")


if __name__ == "__main__":
    import asyncio

    parser = argparse.ArgumentParser()
    subparsers = parser.add_subparsers(dest="command", required=True)

    # Evaluate command
    evaluate_parser = subparsers.add_parser("evaluate")
    evaluate_parser.add_argument("--name", type=str, default="default")
    evaluate_parser.add_argument("--subset", type=str, default="train")
    evaluate_parser.add_argument("--flair", type=WorkflowFixFlairType, default=WorkflowFixFlairType.MergeHybrid, choices=[e for e in WorkflowFixFlairType])
    evaluate_parser.add_argument("--build", action="store_true", help="build retriever prior to evaluation")
    evaluate_parser.add_argument("--evaluate-only", action="store_true", help="skip prediction and only evaluate")
    evaluate_parser.add_argument("--notify-slack", action="store_true", help="send results to Slack after evaluation")

    # Centralize command
    centralize_parser = subparsers.add_parser("centralize")
    centralize_parser.add_argument("--name", type=str, default="default")
    centralize_parser.add_argument("--subset", type=str, default="train")
    centralize_parser.add_argument("--notify-slack", action="store_true", help="send results to Slack after centralization")

    args = parser.parse_args()

    asyncio.run(main(args))
