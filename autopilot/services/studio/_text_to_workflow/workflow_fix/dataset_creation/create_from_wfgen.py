import asyncio
import datetime
import os
import pathlib

import dotenv
import openai
import tqdm

from services.studio._text_to_workflow.common import connections_loader
from services.studio._text_to_workflow.core import settings
from services.studio._text_to_workflow.utils import paths, request_schema, request_utils
from services.studio._text_to_workflow.utils.testing import get_testing_request_context
from services.studio._text_to_workflow.utils.uipath_cloud_platform import is_token_valid
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_fix.workflow_fix_task import WorkflowFixTask


def get_fix_workflow_dataset_path() -> pathlib.Path:
    return pathlib.Path("/workspace/data/workdir/fix_workflow/dataset-20241216-wfgen-only-gpt4o")


async def gather_predictions(wfgen_identifier_to_framework):
    model_options: request_schema.ModelOptions = {
        "model_name": None,
        "seed": "42",
    }

    connections = connections_loader.get_connections_data()

    workflow_fix_task = await WorkflowFixTask().load()
    # fix_workflow_dataset_path = pathlib.Path("/workspace/data/workdir/fix_workflow/Dataset")
    # fix_workflow_dataset_path = pathlib.Path("/workspace/data/workdir/fix_workflow/dataset-20241213-wfgen-only")
    fix_workflow_dataset_path = get_fix_workflow_dataset_path()
    print(sorted(fix_workflow_dataset_path.rglob("*.yaml"))[0])
    for path in tqdm.tqdm(sorted(fix_workflow_dataset_path.rglob("*.yaml"))):
        datapoint = yaml_load(path)
        workflow = datapoint["workflow"]
        workflow_as_str = yaml_dump(workflow)
        errors = datapoint["errors"]["errors"]
        wfgen_identifier = path.parent.name
        if wfgen_identifier not in wfgen_identifier_to_framework:  # TODO: check the one missing
            print(f"Could not find framework for {wfgen_identifier}")
            continue
        if datapoint.get("predictions"):  # already predicted
            print(f"Already processed {path}")
            continue
        target_framework = wfgen_identifier_to_framework[wfgen_identifier]

        failed = True
        for _ in range(3):
            try:
                result = await workflow_fix_task.run_fix_workflow(workflow_as_str, errors, "", connections, target_framework, model_options)
                failed = False
            except openai.AuthenticationError:
                print("Attempting to refresh token")
                await refresh_token()
                continue
            except Exception as e:
                print(f"Failed to fix {e} {path}")
                failed = True
                break
            break
        if failed:
            continue
        result["timestamp"] = datetime.datetime.now().isoformat()
        result["usage"] = result["usage"]
        datapoint["predictions"] = datapoint.get("predictions", []) + [result]
        yaml_dump(datapoint, path)
        # break


def create_wfgen_identifier_mapping():
    wfgen_identifier_to_framework = {}
    for framework in ["Portable", "Windows"]:
        for path in paths.get_workflow_generation_dataset_path(framework).rglob("*.yaml"):
            if path.stem in {"metadata", "subsets"}:
                continue
            wfgen_identifier_to_framework[path.stem] = framework
    return wfgen_identifier_to_framework


def set_new_token(token: str):
    """Persist an UIPATH_TOKEN across the codebase"""
    settings.UIPATH_TOKEN = token
    os.environ["UIPATH_TOKEN"] = token  # type: ignore

    # find dotenv to update
    # env_dir = pathlib.Path(__file__).resolve().parent
    env_dir = pathlib.Path(".").resolve().parent
    root_dir = pathlib.Path(os.path.abspath(os.sep))
    while env_dir != root_dir:
        if (env_path := env_dir / ".env").exists():
            dotenv.set_key(str(env_path), "UIPATH_TOKEN", token)  # persist new token
            break
        env_dir = env_dir.parent

    request_context = get_testing_request_context(client_name="Autopilot Experimentation")
    request_utils.set_request_context(request_context)


async def refresh_token() -> str:
    """Attempts to refresh and persist an expired token. If token is valid, returns existing."""
    token = settings.UIPATH_TOKEN
    if os.getenv("UIPATH_CLIENT_ID") is not None:
        if token is None or not is_token_valid(token):
            for _ in range(5):
                from experimental.autopilot_dataset import studio_token_helper

                token = await studio_token_helper.get_studio_token()
                if token is not None and token != "":
                    break
                print("Retrying to get the token...")
            else:
                raise Exception("Failed to get the token")
            set_new_token(token)
    if token is None:
        raise Exception("Failed to get the token")
    return token


if __name__ == "__main__":
    wfgen_mapping = create_wfgen_identifier_mapping()
    asyncio.run(gather_predictions(wfgen_mapping))
