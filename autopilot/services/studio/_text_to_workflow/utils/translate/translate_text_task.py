import pathlib
import re
from typing import List

import langchain.cache
import langchain.callbacks
import langchain.chains
import langchain.chat_models
import langchain.embeddings
import langchain.prompts
import langchain.schema
from pydantic import BaseModel, Field
from yaml import Y<PERSON><PERSON>rror

from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import telemetry_utils
from services.studio._text_to_workflow.utils.telemetry_utils import log_execution_time
from services.studio._text_to_workflow.utils.translate.translate_text_schema import MultiTextTranslationRequest, TextTranslationRequest
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

LOGGER = telemetry_utils.AppInsightsLogger()


class MultiTranslationOutput(BaseModel):
    outputs: List[str] = Field(description="list of translated strings")


class TranslateTextTask:
    def __init__(self, config_name: str) -> None:
        self.config_path = (pathlib.Path(__file__).parent / config_name).absolute()
        self.config = yaml_load(self.config_path)

    @log_execution_time("TranslateText")
    async def translate_single_str(self, request: TextTranslationRequest) -> str:
        inputs = {
            "target_language": request["target_language"],
            "input_string": request["input_string"],
        }

        system_message_template = langchain.prompts.SystemMessagePromptTemplate.from_template(self.config["prompt_single_str"]["system_msg"])
        user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt_single_str"]["user_msg_template"])

        chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages([system_message_template, user_message_template])

        model = ModelManager().get_llm_model("translation_model", request["feature"])

        try:
            chain = chat_prompt | model
            return (await chain.ainvoke(inputs)).content
        except Exception as e:
            LOGGER.exception(f"Failed to translate single string. Error: {e}")
            return inputs["input_string"]

    @log_execution_time("TranslateText")
    async def translate_multi_str(self, request: MultiTextTranslationRequest) -> list[str]:
        inputs = {
            "target_language": request["target_language"],
            "input_strings": request["input_strings"],
        }

        system_message_template = langchain.prompts.SystemMessagePromptTemplate.from_template(self.config["prompt_multi_str"]["system_msg"])
        user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt_multi_str"]["user_msg_template"])

        model = ModelManager().get_llm_model("translation_model", request["feature"])

        example_user_message = user_message_template.format(**self.config["prompt_multi_str"]["demo_request"])
        example_assistant_message = langchain.prompts.AIMessagePromptTemplate.from_template(
            yaml_dump(self.config["prompt_multi_str"]["demo_response"], allow_unicode=True)
        )

        chat_prompt = langchain.prompts.ChatPromptTemplate.from_messages(
            [
                system_message_template,
                example_user_message,
                example_assistant_message,
                user_message_template,
            ]
        )

        try:
            chain = chat_prompt | model
            result = await chain.ainvoke(inputs)
            response = self._load_completion_yaml(str(result.content))
        except Exception as e:
            LOGGER.exception(f"Failed to translate multiple strings. Error: {e}")
            return inputs["input_strings"]

        return response

    def _load_completion_yaml(self, output: str) -> list[str]:
        if output.startswith("```yaml"):
            match = re.match(r"```yaml(.*)```", output, re.DOTALL)
        elif output.startswith("```"):
            match = re.match(r"```(.*)```", output, re.DOTALL)
        else:
            match = re.match(r"(.*)", output, re.DOTALL)
        if not match:
            return []

        process_str = match.group(1)
        try:
            loaded = yaml_load(process_str)
        except YAMLError:
            return []

        return loaded


if __name__ == "__main__":
    import asyncio

    task = TranslateTextTask("translate_prompt.yaml")
    request = {
        "target_language": "Romanian",
        "input_strings": ["Hello world!, I am a robot"],
        "feature": "Autopilot Action",
    }
    response = asyncio.run(task.translate_multi_str(request))
    print(response)
