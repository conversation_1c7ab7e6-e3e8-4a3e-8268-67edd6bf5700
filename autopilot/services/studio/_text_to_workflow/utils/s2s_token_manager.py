import time
from threading import Lock

import requests

from services.studio._text_to_workflow.core import settings


class TokenManager:
    _instance = None
    _lock = Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(<PERSON>kenMana<PERSON>, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, "initialized"):  # Ensure __init__ is only called once
            self.token_url = f"{settings.CLOUD_URL_BASE}/identity_/connect/token"
            self.client_id = settings.S2S_CLIENT_ID
            self.client_secret = settings.S2S_CLIENT_SECRET
            self.token = None
            self.expiry_time = 0  # Epoch timestamp of when the token expires
            self.initialized = True

    def _fetch_token(self):
        """
        Fetches a new access token from the token URL using client credentials.

        This method makes a POST request to the token URL with the client ID and client secret
        to obtain a new access token. If the request is successful, the token and its expiry time
        are stored. The token is set to refresh 1 minute before its actual expiry time.

        Raises:
            Exception: If the request to fetch the token fails, an exception is raised with the
                       status code and response text.

        """
        # Make the request to get a new token
        response = requests.post(
            self.token_url,
            data={
                "grant_type": "client_credentials",
                "client_id": self.client_id,
                "client_secret": self.client_secret,
            },
        )

        if response.status_code == 200:
            data = response.json()
            self.token = data["access_token"]
            expires_in = data["expires_in"]  # Time in seconds until the token expires
            self.expiry_time = time.time() + expires_in - 300  # Refresh 5 minutes before expiry
        else:
            raise Exception(f"Failed to fetch token: {response.status_code} - {response.text}")

    def get_token(self):
        """
        Retrieve the current token. If the token is None or expired, fetch a new token.

        Returns:
            str: The current valid token.
        """
        if self.token is None or time.time() >= self.expiry_time:
            self._fetch_token()
        return self.token


s2s_token_manager = TokenManager()

if __name__ == "__main__":
    token_manager = TokenManager()
    print(token_manager.get_token())
