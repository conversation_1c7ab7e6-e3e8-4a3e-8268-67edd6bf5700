import functools
import uuid

import jwt

from services.studio._text_to_workflow.core import constants
from services.studio._text_to_workflow.core.dev_config import dev_settings
from services.studio._text_to_workflow.schemas.common import RequestContext


def get_testing_request_context(localization="en", client_name="Evaluation") -> RequestContext:
    data = {
        "request_id": "123",
        "tenant_id": dev_settings.TEST_TENANT_ID,
        "organization_id": dev_settings.TEST_ORGANIZATION_ID,
        "user_id": dev_settings.TEST_USER_ID,
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "Test",
        "client_name": client_name,
        "localization": localization,
        "client_version": "1.0.0",
    }
    return RequestContext(**data)


@functools.cache
def get_testing_client_jwt_token():
    payload = {
        constants.USER_ID_CLAIM: str(uuid.uuid4()),
        constants.ORGANIZATION_ID_CLAIM: dev_settings.TEST_ORGANIZATION_ID,
        constants.SUB_TYPE_CLAIM: constants.SUB_TYPE_SERVICE,
    }

    return "Bearer " + jwt.encode(payload, "test_secret", algorithm="HS256")
