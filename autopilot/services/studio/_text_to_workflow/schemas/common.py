from typing import Any, Dict, List, Optional

from pydantic import BaseModel

from services.studio._text_to_workflow.utils.request_schema import ModelOptions


class RequestContext(BaseModel):
    request_id: Optional[str] = None
    tenant_id: Optional[str] = None
    organization_id: Optional[str] = None
    correlation_id: Optional[str] = None

    user_id: Optional[str] = None
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None

    client_name: Optional[str] = None
    client_version: Optional[str] = None
    s2s_iprange: Optional[str] = None

    raw_jwt: Optional[str] = None

    # Force a model across all use cases, or force a different model for each use case
    model: Optional[str | dict[str, ModelOptions]] = None
    localization: Optional[str] = None

    spans: List[Dict[str, Any]] = []
    skip_whitelist: Optional[bool] = None

    license_type: Optional[str] = None
    studio_project_id: Optional[str] = None

    # used to track multiple llm requests for the same action
    request_action_id: Optional[str] = None

    # used to track the session id for the UIAtask
    ui_task_session_id: Optional[str] = None

    # used when we need to passthrough the product header
    # right now it will be used for QA DOM endpoints, to differentiate between Semantic Activities and Healing Agent
    requesting_product_header: Optional[str] = None

    # used to pass the computer vision user token to the computer vision service when the call is s2s
    # right now CV does not support S2S authentication, but for several endpoints the requests made to us uses S2S
    computer_vision_user_token: Optional[str] = None

    # LLM GW BYO specific headers
    llm_gw_allow_only_byo: Optional[str] = None
    llm_gw_operation_code: Optional[str] = None

    @property
    def user_data(self) -> Dict[str, Any]:
        return {
            "email": self.email,
            "first_name": self.first_name,
            "last_name": self.last_name,
        }


class Variable(BaseModel):
    name: str
    type: str
