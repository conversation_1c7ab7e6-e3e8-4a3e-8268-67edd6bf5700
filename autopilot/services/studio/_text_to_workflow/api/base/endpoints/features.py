from fastapi import APIRouter

from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.utils.request_schema import FeatureFlagsRequest, FeatureFlagsResponse

router = APIRouter()


@router.post("/enabled-features", response_model=FeatureFlagsResponse)
async def get_feature_flags(
    request: FeatureFlagsRequest,
) -> FeatureFlagsResponse:
    """
    Get the enabled features of the service.

    Returns:
        FeatureFlagsResponse: A JSON response containing the enabled features

    """
    if settings.IS_PROD and request["licenseType"] == "Enterprise":
        return {"featureFlags": {"enableConfigureActivity": False}}
    return {"featureFlags": {"enableConfigureActivity": True}}
