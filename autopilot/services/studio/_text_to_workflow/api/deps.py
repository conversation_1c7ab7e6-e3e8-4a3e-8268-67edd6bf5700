from typing import Any, Optional

from fastapi import Depends, HTTPException, Request
from fastapi.security import <PERSON><PERSON>eyHeader
from jwt import PyJWTError, decode

from services.studio._text_to_workflow.core import constants as c
from services.studio._text_to_workflow.schemas import RequestContext

api_key_header = APIKeyHeader(name=c.AUTH_HEADER, scheme_name=c.AUTH_HEADER, auto_error=False)


def get_auth_token(authorization: Optional[str] = Depends(api_key_header)) -> str:
    """
    Extract and validate the authentication token from the Authorization header.

    This function checks for the presence of an Authorization header, ensures it
    uses the Bearer scheme, and extracts the token.

    Args:
        authorization (Optional[str]): The Authorization header value.

    Returns:
        str: The extracted authentication token.

    Raises:
        HTTPException: 401 status code
    """
    if authorization is None:
        raise HTTPException(status_code=401, detail="Authorization header is missing")

    scheme, _, token = authorization.partition(" ")
    if scheme.lower() != "bearer":
        raise HTTPException(status_code=401, detail="Invalid authentication scheme")

    if not token:
        raise HTTPException(status_code=401, detail="Token is missing")

    return token


def get_decoded_token(token: str = Depends(get_auth_token)) -> Any:
    """
    Decode and validate the JWT token.

    This function decodes the provided JWT token without verifying its signature.
    It checks for the presence of required claims (user ID and organization ID).

    Args:
        token (str): The JWT token to decode. Defaults to the token extracted by get_auth_token.

    Returns:
        dict: The decoded token payload.

    Raises:
        HTTPException: 401 status code
    """
    try:
        decoded = decode(token, options={"verify_signature": False})

        # both exceptions should return 401
        if c.SUB_TYPE_CLAIM not in decoded:
            raise Exception("Invalid token")
        if decoded[c.SUB_TYPE_CLAIM] == c.SUB_TYPE_SERVICE:
            return decoded
        if c.USER_ID_CLAIM not in decoded or c.ORGANIZATION_ID_CLAIM not in decoded:
            raise Exception("Invalid user token")
        return decoded
    except PyJWTError as e:
        raise HTTPException(status_code=401, detail=f"Invalid token: {e}")


def get_context(
    raw_jwt: str,
    tenant_id: Optional[str] = None,
    internal_tenant_id: Optional[str] = None,
    client_name: Optional[str] = None,
    client_version: Optional[str] = None,
    s2s_iprange: Optional[str] = None,
    cor_id_v1: Optional[str] = None,
    cor_id_v2: Optional[str] = None,
    request_id: Optional[str] = None,
    model: Optional[str] = None,
    planning_model: Optional[str] = None,
    planning_model_custom_url: Optional[str] = None,
    generation_model: Optional[str] = None,
    localization: Optional[str] = None,
    skip_whitelist: Optional[bool] = None,
    internal_account_id: Optional[str] = None,
    license_type: Optional[str] = None,
    studio_project_id: Optional[str] = None,
    llm_gateway_requesting_product: Optional[str] = None,
    ui_task_session_id: Optional[str] = None,
    computer_vision_user_token: Optional[str] = None,
    user_id: Optional[str] = None,
    llm_gw_allow_only_byo: Optional[str] = None,
    llm_gw_operation_code: Optional[str] = None,
) -> RequestContext:
    """
    Create RequestContext object.
    Most of the details are extracted from a JWT token specific to a user.

    Args:
        raw_jwt (Optional[str]): The raw JWT token.
        tenant_id (Optional[str]): The tenant ID.
        internal_tenant_id (Optional[str]): The internal tenant ID.
        client_name (Optional[str]): The name of the client.
        client_version (Optional[str]): The version of the client.
        s2s_iprange (Optional[str]): The server-to-server IP range.
        cor_id_v1 (Optional[str]): The correlation ID (version 1).
        cor_id_v2 (Optional[str]): The correlation ID (version 2).
        request_id (Optional[str]): The request ID.
        model (Optional[str]): The model for the workflow generation.
        planning_model (Optional[str]): The planning model for the workflow generation.
        planning_model_custom_url (Optional[str]): The custom URL for the planning model.
        generation_model (Optional[str]): The generation model for the workflow generation.
        localization (Optional[str]): The localization for the workflow generation.

    Returns:
        dict: A dictionary containing all the gathered context information.
    """
    user_token_data = get_decoded_token(get_auth_token(raw_jwt))

    data = {
        "request_id": request_id,
        "tenant_id": tenant_id if tenant_id else internal_tenant_id,
        "organization_id": internal_account_id if user_token_data[c.SUB_TYPE_CLAIM] == c.SUB_TYPE_SERVICE else user_token_data[c.ORGANIZATION_ID_CLAIM],
        "correlation_id": cor_id_v1 if cor_id_v1 else cor_id_v2,
        "user_id": user_id if user_token_data[c.SUB_TYPE_CLAIM] == c.SUB_TYPE_SERVICE else user_token_data.get(c.USER_ID_CLAIM),
        "email": user_token_data.get(c.EMAIL_CLAIM, ""),
        "first_name": user_token_data.get(c.FIRST_NAME_CLAIM, ""),
        "last_name": user_token_data.get(c.LAST_NAME_CLAIM, ""),
        "client_name": client_name,
        "client_version": client_version,
        "s2s_iprange": s2s_iprange,
        "raw_jwt": raw_jwt,
        "model": model,
        "planning_model": planning_model,
        "planning_model_custom_url": planning_model_custom_url,
        "generation_model": generation_model,
        "localization": localization,
        "skip_whitelist": skip_whitelist,
        "license_type": license_type,
        "studio_project_id": studio_project_id,
        "requesting_product_header": llm_gateway_requesting_product,
        "ui_task_session_id": ui_task_session_id,
        "computer_vision_user_token": computer_vision_user_token,
        "llm_gw_allow_only_byo": llm_gw_allow_only_byo,
        "llm_gw_operation_code": llm_gw_operation_code,
    }

    return RequestContext(**data)


def get_context_from_request(request: Request) -> RequestContext:
    """
    Create RequestContext object from a FastAPI Request object.

    Args:
        request (Request): The request object.

    Returns:
        dict: A dictionary containing all the gathered context information.
    """
    return get_context(
        request.headers.get(c.AUTH_HEADER),  # type: ignore
        request.headers.get(c.TENANT_ID_HEADER),
        request.headers.get(c.INTERNAL_TENANT_ID_HEADER),
        request.headers.get(c.CLIENT_NAME_HEADER),
        request.headers.get(c.CLIENT_VERSION_HEADER),
        request.headers.get(c.S2S_IPRANGE_HEADER),
        request.headers.get(c.CORRELATION_ID_V1),
        request.headers.get(c.CORRELATION_ID_V2),
        request.headers.get(c.REQUEST_ID),
        request.query_params.get(c.MODEL_PARAM),
        request.query_params.get(c.PLANNING_MODEL_PARAM) or request.headers.get(c.PLANNING_MODEL_PARAM),
        request.headers.get(c.PLANNING_MODEL_CUSTOM_URL_PARAM),
        request.query_params.get(c.GENERATION_MODEL_PARAM) or request.headers.get(c.GENERATION_MODEL_PARAM),
        request.headers.get(c.LOCALIZATION_PARAM),
        bool(request.headers.get(c.SKIP_WHITELIST_HEADER)),
        request.headers.get(c.INTERNAL_ORG_ID_HEADER),
        request.headers.get(c.LICENSE_TYPE),
        request.headers.get(c.STUDIO_PROJECT_ID),
        request.headers.get(c.LLM_GATEWAY_REQUESTING_PRODUCT_HEADER),
        request.headers.get(c.UI_TASK_SESSION_ID),
        request.headers.get(c.COMPUTER_VISION_USER_TOKEN_HEADER),
        request.headers.get(c.USER_ID_HEADER),
        request.headers.get(c.LLM_GATEWAY_ALLOW_ONLY_BYO_HEADER),
        request.headers.get(c.LLM_GATEWAY_OPERATION_CODE_HEADER),
    )
