# TOKEN Claims
USER_ID_CLAIM = "sub"
ORGANIZATION_ID_CLAIM = "prt_id"
EMAIL_CLAIM = "email"
FIRST_NAME_CLAIM = "first_name"
LAST_NAME_CLAIM = "last_name"
SUB_TYPE_CLAIM = "sub_type"
SUB_TYPE_SERVICE = "service.internal"
SUB_TYPE_USER = "user"
SUB_TYPE_ROBOT = "robot.user"

# Headers
AUTH_HEADER = "Authorization"
TENANT_ID_HEADER = "X-Uipath-Tenantid"
# Internal headers - the officially supported ones
INTERNAL_ORG_ID_HEADER = "X-UiPath-Internal-AccountId"
INTERNAL_TENANT_ID_HEADER = "X-UiPath-Internal-TenantId"
CLIENT_NAME_HEADER = "X-UiPath-ClientName"
CLIENT_VERSION_HEADER = "X-UiPath-ClientVersion"
S2S_IPRANGE_HEADER = "X-UiPath-S2S-IpRange"
CORRELATION_ID_V1 = "x-uipath-correlationid"
CORRELATION_ID_V2 = "x-uipath-correlation-id"
REQUEST_ID = "request-id"
LOCALIZATION_PARAM = "X-Uipath-Localization"
SKIP_WHITELIST_HEADER = "X-UiPath-SkipWhiteList"
LICENSE_TYPE = "X-UiPath-License"
STUDIO_PROJECT_ID = "X-UiPath-StudioProjectId"
LLM_GATEWAY_REQUESTING_PRODUCT_HEADER = "X-UiPath-LlmGateway-RequestingProduct"
UI_TASK_SESSION_ID = "x-uipath-wingman-prompt-id"

# Used to pass the user id to the LLM Gateway, when called via S2S
USER_ID_HEADER = "X-UiPath-Autopilot-UserId"  # still not 100% on the name

# used to populate the computer vision user token in the request context
COMPUTER_VISION_USER_TOKEN_HEADER = "X-UIPATH-CV-USER-TOKEN"

# LLM GW BYO specific headers
LLM_GATEWAY_ALLOW_ONLY_BYO_HEADER = "X-UiPath-LlmGateway-AllowOnlyByoExecution"
LLM_GATEWAY_OPERATION_CODE_HEADER = "X-UiPath-LlmGateway-OperationCode"

# Query parameters
SEED_PARAM = "seed"
MODEL_PARAM = "model"
PLANNING_MODEL_PARAM = "planning_model"
PLANNING_MODEL_CUSTOM_URL_PARAM = "planning_model_custom_url"
GENERATION_MODEL_PARAM = "generation_model"
