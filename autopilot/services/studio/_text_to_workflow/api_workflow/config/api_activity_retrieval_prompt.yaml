demonstration_ranking:
  planning_demonstrations_limit: 30
  demonstrations_limit: 4
  force_windows_demonstrations: 2
  embedding_retrieval_docs: 30
  reranking_enable: true
  reranking_docs: 30
  mmr_enable: true
  mmr_similarities: iou_retrieved_activities  # choices: embeddings, reranking, pled, plan_tree_edit_distance, iou_activities, iou_retrieved_activities
  mmr_similarities_action: build # choices: build, load, disable; only used at dataset build time
  mmr_docs: 30
  mmr_diversity: 1.0  # 1.0 if you use reranking, 0.2 if not
  sorting_order: descending  # choices: ascending, descending
post_processing:
  plan_step_activities: 1 # how many activities to try extract for each plan step
  plan_step_triggers: 1
  anticipated_inexistent_activities: 2 # how many matches for each anticipated inexistent activities to keep
  anticipated_inexistent_triggers: 2
prompt:
  demonstration_filtering:
    planing_demonstrations_limit: 6
    wf_min_trigger_count: 2 # for workflow generation mode, we want at least 2 demonstrations with triggers
  proposed_activities_selection:
    main_query_activities_percentage: 30 # the percentage of activities that should be retrieved from the whole query embedding, the rest will be retrieved from each line of the query embeddings
    query_triggers_limit: 15  # how many related triggers do we want to extract for the query
    query_activities_limit: 150  # how many related activities do we want to extract for the query
    wf_step_activites_limit: 2  # how many related activities do we want to extract for each activity in the existing workflow
    wf_step_activities_limit_for_edit: 10   # how many related activities do we want to extract for each activity when rewriting or editing the existing workflow
  prompt_entity_types: activities
  system_msg: |-
    {intro}
    You will be provided with a JSON list with all the available Integration Activities used to build UiPath Serverless Workflows. Each activity has an id, application, display name and description. They will appear as JSON objects.
    The user will provide a query along a subset of Integration Activities ids out of the full list that might be used to build an automation that solves the query.
    
    {output_format}
    
    {general_requirements}

    # Integration Activities Details:
    - Each activity has an "application" field that indicates the application that the activity will interact with.
    - The purpose of the activity is indicated by the "display_name" and "description" fields.
    - There are 3 types of Integration Activities:
    1. Specialized Integration Activities, which are used for a specific single CRUD operation on a specific entity.
      - Example: Activity with application "Salesforce" and name "Get Account" is an activity used to get an account object from Salesforce.
    2. Query Activities that can perform queries in a Query Language specific to an application.
      - Query activities can retrieve any type of object from the application, not just the ones that can be retrieved with a specialized integration activity. 
      - Query activities MUST BE SELECTED when the query requires the search or retrieval of objects from the activity's corresponding application.
      - Example: Activity with application "Salesforce" and name "Search using SOQL" is an activity used to retrieve records by executing 'SELECT' queries in the Salesforce Object Query Language (SOQL)
      - Example: Activity with application "OracleNetSuite" and name "Execute SuiteQL Query" is an activity used to execute SELECT queries on the Oracle NetSuite database.
    3. API Integration Activities that perform pre-authorized HTTP calls to a specific application API. These must be used only when a more specific Integration Operation for that application is not available.
      - The name of these activities follow the format: "<APPLICATIONNAME> HTTP Request". 
      - IMPORTANT: Only use these activities when an operation on an <APPLICATIONNAME> entity is mentioned in the query and no Specialized Integration Activity is available for that operation/object OR if the query specifically asks for an API call or HTTP request for that application.
        - Example: "Salesforce HTTP Request" is an activity used to perform HTTP requests to interact with Salesforce and should be used whenever a more specific Salesforce Integration Activity is not available for a specific application entity.
        - Example: "Oracle NetSuite HTTP Request" is an activity used to Perform HTTP requests to interact with Oracle NetSuite and should be used whenever a more specific Oracle NetSuite Integration Activity is not available.
      - Only include an API Integration Activity if the application name is directly mentioned in the query or heavily implied.
      - IMPORTANT: DO NOT USE MORE THAN 5 API Integration Activities (e.g. activities with names ending in "HTTP Request" like "Salesforce HTTP Request").

    # Relevant Integration Activities Requirements:
    - Your goal is to select all potentially relevant activities and exclude the ones clearly irrelevant to the query.
    {activity_selection_guidelines_1}
    - Attention: If you already added an activity id, you must not add it again.
    {activity_selection_guidelines_2}
    - When asked to "generate" or "summarize" something from natural language or using an LLM, make sure to include Content Generation or Chat Completion activities as well in the proposed activities list.
      - Example: Activity with application "Perplexity" and name "Chat Completion" is used to generate natural language responses using Perplexity.
      - Example: Activity with application "DeepSeek" and name "Chat Completion" is used to generate natural language responses using DeepSeek models based on a given prompt and inputs. Add this activity when DeepSeek completion is mentioned in the query.
      - Example: Activities named "Web Summary" are used to summarize information using large language models based on a given prompt and inputs. Add this activity when summarizing any type of information is mentioned in the query.
    - When asked to "create" something, make sure to include activities that are relevant to the query, like write/send/invite activities for your type of entity because you will probably be asked to fill in the details into what you created.
    - When asked to "search" for something, make sure to include the "Search" or "List" activities for that entity or application, if available.
    - IMPORTANT: If the query requires the searching or retrieving of objects from an application with a query activity, you must include any available query activity for that application.
      - Example: For query "Check that a Netsuite invoice has been paid ...", you must include the "Execute SuiteQL Query" activity.
      - Example: For query "Search for an account in Salesforce with a specific name ...", you must include the "Search using SOQL" activity.
    - If you fail to provide activities that may be necessary for the plan, you will be heavily penalized.
    - Try to include all activities that might seem relevant for each action/step of the automation from both the user query and the plan.

    - Very Important: If there seem to be multiple activities useful even for a step which seems specific (but may be achieved in multiple ways), provide all of them. Take note of these examples:
         - Example: If a plan step says "Send a notification message in Microsoft Teams", you should provide all of "Send Channel Message", "Send Group Chat Message" and "Send Individual Chat Message" to the activities list (activity names are made-up here, to exemplify the point).
    - How to get related activities:
      - Analyze the query to identify the applications that are needed. If the application is specified in a query step/action, for that step you must include only the relevant activities from that. Example: Don't include Smartsheet activities if a query step clearly specifies that it needs Google Sheets.
        - Example: "Search/Get for <application>" -> you should include all activities related to search/get from that specific application that match the query.
      - If the application is not specified for a step/action, then you should infer the relevant activity based on the query or existing workflow. E.g. you can infer the application based on a previous step. If the previous step mentions an application then retrieve activities from the same application for the current step.
        - Example: "Check for an account on Salesforce, and create a new opportunity" -> you should infer that activities from Salesforce should be included for both steps.
      - If the application is not specified for a step/action and cannot be inferred, you may include related activities from any application.
        - Example: "Generate a summary" -> you should include all activities related to summarization from any application.
    {activity_list_structuring}
    - Do not blindly increment the id of the last activity in the list if the next activity is not useful for solving the user query, e.g. this is completely wrong: [0,1,2,3,4,5,6,7,...,100].
    - The response activity list must only contain the ids of the proposed activities. DO NOT ADD THE ENTIRE ACTIVITY DEFINITIONS.

    {ambiguities_requirements}

    {score_requirements}
    # Undocumented HTTP Requests Requirements:
    - The undocumentedHttpRequests must be a list of API Requests that are needed to solve the user query, but they don't correspond to any of the proposed activities. Use the ambiguities in the query and the activities that you have retrieved to generate the most relevant HTTP request operations. You MUST generate these operations for all the operations that you would need to solve the user query.
    - You must not generate more than 5 of such requests.
    - If a query requires using an unknown application, you must add an Undocumented HTTP Request for it.
    - If a query explicitly mentions using an HTTP Request, you must add an Undocumented HTTP Request for it.
    - For each ambiguity regarding the structure of the URL, endpoints, or parameters for a HTTP request, you must add an Undocumented HTTP Request.
    - If an Undocumented Http Request application also has some activities corresponding to it, make sure the application name is exactly as it appears in the proposed activities input list.
    - Even if you generate an Undocumented HTTP Request, if the Generic HTTP Request activity for that specific application is available, you must retrieve it and use it.
    - Even if you generate an Undocumented HTTP Request, if the necessary activity for the task is available, you must retrieve it and use it.
  
    # Inexistent Activity Type Name Requirements:
    - The inexistentActivities must be a list of fictional activity type names that you would have liked to have retrieved, but which do not exist.
    - However, if you think you did a good job and fulfilled the activities for the plan well, inexistentActivities should be empty.
    - The inexistentActivities must be lists of strings.
    - You must make use of Undocumented HTTP Requests to generate the most relevant inexistent activity type names. You must have as many inexistent activities as you have undocumented HTTP Requests.
    - Example: 
      ```
        Query: "Download the email from Gmail and upload it to Google Drive and OneDrive."
        Retrieval Critique: "I found the OneDrive upload activity, but I did not find the Google Drive upload activity."
        Anticipated But Inexistent Activity Type Names: ["Google Drive Upload Files"]
      ```
    # Plan Requirements:
    - IMPORTANT: Even if the activity or application necessary for a plan step is not available, generate the plan step anyway.
    - IMPORTANT: The plan should never be empty. It must always have at least one step.
    - It's important to include "If" and "For each" steps where necessary in the pseudocode.
    {plan_requirements}
        Query: "Download the invoice attachment from Gmail and upload it to Salesforce as a related document."
        Bad Example: "1. Download the invoice from Gmail.\n2. Upload the document to Salesforce."
        Good Example: "1. Use Gmail API to retrieve emails with invoice attachments.\n2. Extract and download the invoice attachment from the email.\n3. Use Salesforce API to upload the document and associate it with the appropriate record in Salesforce."

    {footer}

  user_msg_template: |-
    QUERY:
    {query}
  demonstration_template: |-
    ```
    QUERY: {query}
    PLAN: {plan}
    ```