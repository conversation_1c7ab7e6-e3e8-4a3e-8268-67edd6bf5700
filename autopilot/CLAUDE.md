# UiPath Autopilot Generation Service

## Overview

The **UiPath Autopilot Generation Service** (code name: **Wingman**) is a sophisticated AI-powered service designed to assist developers with automated workflow generation. This FastAPI-based service leverages multiple Large Language Models (LLMs) to generate various UiPath artifacts including RPA workflows, API integrations, BPMN diagrams, code snippets, and expressions.

## Service Architecture

### Core Technologies
- **FastAPI** (v0.115.7+) - Modern web framework for building APIs  
- **Python 3.11+** - Core runtime environment
- **Azure AI Services** - Cloud AI integration and telemetry
- **Redis** (v5.2.1) - Caching and session management
- **Vector Embeddings** - Semantic search and retrieval

### LLM Providers
- **OpenAI** (GPT models)
- **Anthropic** (Claude models) 
- **Google** (Vertex AI models)
- **Azure OpenAI** - Enterprise AI services

### Service Entry Points
- **Main Service**: `services/studio/service.py` (Port 5002)
- **CLI Tools**: `main.py` (Dataset management via Typer)

## Generation Artifacts & Inference Pipelines

The service generates **5 primary artifact types**, each with specialized inference pipelines:

### 1. RPA Workflow Generation
**Location**: `services/studio/_text_to_workflow/workflow_generation/`

**What it generates**: Complete UiPath RPA workflows in YAML format with triggers, activities, and process flows.

**Inference Pipeline**:
```
User Request → Request Validation → Translation (if needed) → 
Planning & Activity Retrieval → Demonstration Retrieval → 
Metadata Retrieval → LLM Generation → Post-processing → 
YAML Validation → Dynamic Activity Config → UI Automation Expansion
```

**Key Components**:
- `WorkflowGenerationTask` - Main orchestrator
- `WorkflowGenerationActivityRetrievalService` - Activity/trigger retrieval  
- `WorkflowGenerationPostProcessComponent` - Validation and cleanup
- Embeddings-based demonstration retrieval system

**Models Used**: Adaptive model selection (standard → large for complex workflows)

### 2. API Workflow Generation  
**Location**: `services/studio/_text_to_workflow/api_workflow/`

**What it generates**: API-based workflows with JavaScript/JQ expressions for data transformation and API integrations.

**Inference Pipeline**:
```
User Request → Request Processing → Activity Retrieval → 
Draft Generation (Create/Edit) → Post-generation Processing → 
Validation → Connection Config → Summarization
```

**Key Features**:
- Supports edit mode with patch-based updates
- Retry mechanism with fallback to full rewrite
- JavaScript and JQ expression handling
- Connection configuration management

**Key Components**:
- `APIWorkflowTask` - Main coordinator
- `ApiWfDraftService` - Core generation logic
- `ApiWfPostGenerationProcessingService` - Validation and error handling

### 3. BPMN Generation
**Location**: `services/studio/_text_to_workflow/bpmn_generation/`

**What it generates**: Business Process Model and Notation (BPMN) diagrams in XML format with process flows, gateways, and tasks.

**Inference Pipeline**:
```
Multi-modal Input (Text/Image/Document) → Request Routing → 
Processing by Type → Generation/Edit/Extension → 
XML Schema Validation → UiPath Extensions → Chat History Integration
```

**Multi-modal Support**:
- **Text**: Natural language to BPMN XML
- **Image**: Process diagrams to BPMN XML  
- **Document**: PDF/Word/Visio extraction to BPMN

**Key Components**:
- `BpmnGenerationTask` - Core generation engine
- `BpmnRouterTask` - Request routing and tool selection
- `BusinessProcessAgent` - Multi-agent workflow support
- Gateway pattern support (exclusive, parallel, inclusive)

### 4. Code Generation
**Location**: `services/studio/_text_to_workflow/code_generation/`

**What it generates**: C# code snippets, methods, and classes based on natural language requirements.

**Inference Pipeline**:
```
User Request → Planning Phase (Pseudocode) → 
Method Retrieval (Embeddings) → Type Definition Building → 
Code Generation → Post-processing → Extraction & Formatting
```

**Key Features**:
- Planning-first approach with pseudocode generation
- .NET method and type retrieval via embeddings
- Demonstration-based learning from examples
- Fix-code functionality for error correction

**Key Components**:
- `CodeGenerationTask` - Main orchestration
- `CodeGenerationRetriever` - Method and type retrieval
- `CodeGenerationDemoRetriever` - Example-based learning

### 5. Expression Generation
**Location**: `services/studio/_text_to_workflow/expression_generation/`

**What it generates**: Single-line expressions in multiple languages (C#, VB.NET, JavaScript, JQ) for data transformation and logic.

**Inference Pipeline**:
```
User Request → Language Detection → Task Selection → 
Context Preparation → Generation → Validation & Constraints → 
Retry with Fix (if needed)
```

**Supported Languages**:
- **C#** - .NET expressions
- **VB.NET** - Visual Basic expressions  
- **JavaScript** - API workflow expressions
- **JQ** - JSON transformation expressions

**Specialized Tasks**:
- `ExpressionGenerationTask` - Standard expressions
- `BPMNExpressionGenerationTask` - BPMN-specific expressions
- `ApiWorkflowExpressionGenerationTask` - API workflow expressions
- `JsInvokeExpressionGenerationTask` - JavaScript function expressions

## Shared Infrastructure

### Model Management
**Location**: `services/studio/_text_to_workflow/models/`

- **ModelManager**: Handles different LLM models and providers
- **Dynamic Selection**: Chooses appropriate model based on complexity
- **Fallback Logic**: Escalates to larger models when needed

### Embeddings & Retrieval System
**Locations**: 
- `services/studio/_text_to_workflow/common/embeddingsdb.py`
- `services/embeddings/` (Dedicated embeddings service)

**Capabilities**:
- Activity/method/demonstration retrieval using vector embeddings
- Similarity-based matching for examples and components  
- Reranking and diversity optimization (MMR)
- Cross-encoder reranking for improved relevance

### Post-processing Services
**Location**: `services/studio/_text_to_workflow/common/`

**Services**:
- YAML/JSON validation and parsing
- Error detection and correction pipelines
- Dynamic configuration application
- Translation support (auto-detect → English → localized output)

## API Structure

### Endpoint Organization
- **Base Router** (`/api/base/`) - Core functionality
- **V1 Router** (`/api/v1/`) - Version 1 API endpoints
- **V2 Router** (`/api/v2/`) - Version 2 API endpoints

### Core Endpoints
1. **Workflow Generation** - `/workflow-generation`
2. **Code Generation** - `/code-generation`  
3. **API Workflow** - `/api-workflow`
4. **BPMN Generation** - `/bpmn-generation`
5. **Expression Generation** - `/expression-generation`
6. **Activity Summary** - `/activity-summary`
7. **UI Automation** - `/ui-automation`
8. **Test Data Generation** - `/testdata-generation`
9. **Workflow Fix** - `/workflow-fix`
10. **Workflow Assistant** - `/assistants/workflow` & `/assistants/api-workflow`

## Key Architectural Patterns

### 1. Multi-step Reasoning
Each pipeline uses planning → execution phases with intermediate validation.

### 2. Context-aware Generation  
Maintains chat history and leverages existing artifacts for contextual generation.

### 3. Error Correction Loops
Automatic retry mechanisms with specialized fix-generation pipelines.

### 4. Multi-modal Processing
Support for text, images, and documents as input sources.

### 5. Domain-specific Validation
- XML schema validation for BPMN
- YAML structure validation for workflows  
- Code compilation checks
- Expression syntax validation

### 6. Demonstration Learning
Curated examples and embeddings-based retrieval for few-shot learning.

## Development & Testing

### Setup Commands
```bash
# Install dependencies
pip install -e .

# Run tests  
./test.sh

# Start service (development)
./start.sh

# Run evaluations
./eval.sh
```

### Key Configuration Files
- `pyproject.toml` - Project dependencies and configuration
- `services/studio/service.py` - Main service configuration
- `services/studio/_text_to_workflow/core/config.py` - Core settings

### Testing Structure
- **Unit Tests**: `services/studio/_text_to_workflow/tests/unit/`
- **Integration Tests**: `services/studio/_text_to_workflow/tests/integration/`
- **Evaluation Scripts**: `evaluation/`

## Deployment & Operations

### Service Configuration
- **Port**: 5002 (default)
- **Environment**: Configurable via environment variables
- **Monitoring**: Azure Application Insights integration
- **Caching**: Redis for session management and performance

### Background Tasks
- Recurring embeddings refresh
- Process pool executor for compute-intensive tasks
- Proper lifecycle management (startup/shutdown)

### Error Handling
- Global exception handlers with structured responses
- Request validation error handling  
- Comprehensive logging and telemetry

## Integration Points

### UiPath Studio Integration
- Designed for seamless integration with UiPath Studio
- Supports UiPath-specific activity definitions and triggers
- Handles UiPath Cloud Platform authentication

### External Services
- **Azure AI Services** - Model hosting and AI capabilities
- **Azure Storage** - Artifact and dataset storage  
- **Redis** - Session and cache management
- **Various LLM Providers** - OpenAI, Anthropic, Google

## Dataset & Training

### Dataset Management
**Location**: `dataset/`, `experimental/autopilot_dataset/`

- Automated dataset generation from UiPath Studio projects
- Conversion utilities for different artifact types
- Online client for UiPath Studio Web integration

### Evaluation Framework  
**Location**: `evaluation/`

- Comprehensive evaluation suite for all artifact types
- Tree edit distance metrics for workflow comparison
- Activity retrieval evaluation
- Performance benchmarking across models

### Experimental Features
**Location**: `experimental/`

- Fine-tuning experiments for domain-specific models
- Agent-based evaluation frameworks
- Advanced prompt engineering research

## Workflow Assistant System

### Overview
**Location**: `services/studio/_text_to_workflow/assistants/`

The **Workflow Assistant System** provides an intelligent, conversational interface for creating, editing, and managing both RPA and API workflows. It serves as a comprehensive AI-powered assistant that can understand user requests, analyze existing workflows, and perform sophisticated workflow operations through natural language interaction.

### Architecture

#### Core Components
1. **Workflow Assistant** (`workflow_assistant/`) - Handles RPA workflow operations
2. **API Workflow Assistant** (`workflow_assistant/`) - Manages API workflow operations  
3. **Attachment Processing** (`attachments/`) - Processes file attachments for context
4. **Assistant Services** (`services/`) - Shared services and utilities

### Key Capabilities

#### **Supported Scenarios**

**For RPA Workflows**:
- `generate-workflow` - Creates new RPA workflows from scratch
- `edit-workflow` - Modifies existing workflows with targeted changes
- `rewrite-workflow` - Substantially rebuilds workflows
- `fix-workflow` - Repairs workflow errors and issues
- `analyze-workflow` - Provides insights and analysis

**For API Workflows**:
- `generate-workflow` - Creates new API workflows
- `edit-workflow` - Modifies existing API workflows
- `rewrite-workflow` - Rebuilds API workflows (handled as generate)
- `analyze-workflow` - Analyzes API workflow patterns

#### **Request Processing Pipeline**
```
User Request + Chat History + Attachments → 
Smart Routing (AI-powered scenario detection) → 
Context Analysis (workflow references, complexity scoring) → 
Specialized Handler Execution → 
Response Generation (workflow + metadata + errors)
```

#### **Advanced Features**

**Smart Context Understanding**:
- Analyzes chat history for workflow references (ID-based system)
- Processes file attachments (PDF, Excel, Word, etc.) via MarkItDown
- Maintains conversation state across interactions
- Handles multi-workflow references and complex scenarios

**Intelligent Routing**:
- AI-powered scenario detection with complexity scoring (1-5)
- Dynamic model selection (standard vs reasoning models)
- Workflow reference resolution from current designer or chat history
- Multi-language support with automatic detection and response

**Integration Points**:
- **RPA Workflows**: Integrates with `WorkflowGenerationDraftService`
- **API Workflows**: Uses `API_WORKFLOW_GENERATION_TASK`
- **Workflow Fix**: Leverages `WORKFLOW_FIX_TASK` for error resolution
- **Activity Retrieval**: Uses embedding-based activity matching

### API Endpoints
- **`POST /assistants/workflow`** - RPA workflow assistance with SSE streaming
- **`POST /assistants/api-workflow`** - API workflow assistance with SSE streaming

## UI Automation System

### Overview
**Location**: `services/studio/_text_to_workflow/ui_automation/`

The **UI Automation System** provides comprehensive computer vision and AI-powered UI interaction capabilities, enabling automated UI testing, screen understanding, and user interface automation through multiple cutting-edge AI models.

### Architecture

#### **Core Service Components**
- **`ui_automation_service.py`** - Main service classes: `UiAutomationService` & `UIAgentAutomationService`
- **`ui_automation_endpoint.py`** - FastAPI endpoint handlers and request routing
- **`service.py`** - Standalone service entry point with full UI automation capabilities

#### **Key Endpoints**

1. **`/agent-act-on-screen`** - Advanced UI agent action prediction and execution
2. **`/agent-screen-extract`** - Information extraction from screen content
3. **`/close-popup`** - Intelligent popup detection and automatic closing
4. **`/qa-screen`** - Screen element Q&A with semantic matching
5. **`/qa-dom`** - DOM-based element question answering
6. **`/get-element-description`** - Natural language element description generation
7. **`/learn-from-experience`** - Experience-based automation advice generation

### AI Models & Computer Vision

#### **Supported AI Models**
- **GPT-4o Variants**: `gpt-4o-2024-05-13`, `gpt-4o-2024-08-06`, `gpt-4o-2024-11-20`
- **GPT-4.1 Variants**: `gpt-4.1-nano-2025-04-14`, `gpt-4.1-mini-2025-04-14`, `gpt-4.1-2025-04-14`
- **Claude Models**: `claude-3-5-sonnet-********-v2:0`, `claude-3-7-sonnet-********-v1:0`
- **Gemini Models**: `gemini-2.0-flash-001`, `gemini-2.5-pro-exp-03-25`
- **Specialized Models**: `InternVL2_5-78B-MPO`, `Llama-4-Maverick-17B-128E-Instruct-FP8`

#### **Computer Use Integration**
- **OpenAI Computer Use** - Direct integration with OpenAI's computer use preview models
- **Claude Computer Use** - Advanced integration with Claude 3.7 Sonnet computer use capabilities
- **XGA Screen Resizing** - Automatic screen format conversion for optimal model performance
- **Coordinate Conversion** - Intelligent coordinate mapping between different screen resolutions

### Action Systems

#### **Autopilot Actions** (Basic UI Automation):
- `click(element_id)` - Element clicking
- `type_into(element_id, variable, default_value)` - Text input
- `get_text(element_id, variable)` - Text extraction
- `select(element_id, variable, default_value)` - Dropdown selection
- `finish()` - Task completion

#### **Agent Actions** (Advanced UI Automation):
- `click(element_id)` - Advanced clicking with context awareness
- `type_into(element_id, variable, value)` - Smart text input
- `select(element_id, variable, value)` - Intelligent selection
- `send_keys(element_id, keys)` - Special key combinations and shortcuts
- `navigate_back()` - Browser navigation control
- `wait_load_completed()` - Intelligent page loading detection
- `extract_info_from_screen(prompt, label)` - LLM-based information extraction
- `scroll(element_id, direction, number_of_scrolls)` - Precise scrolling control
- `finish(status)` - Advanced task completion with status reporting

### Advanced Capabilities

#### **Computer Vision Integration**
- **UiPath CV Client** - DOM extraction from screen images
- **OpenCV Processing** - Advanced image processing and analysis
- **Element Matching** - Bounding box to XML element matching with IoU thresholds
- **Multi-Source DOM** - Supports Driver, CV, and ObjectRepository sources

#### **Intelligent Features**
- **Caching System** - Redis-based caching for UI agent steps and model predictions
- **Reflection Models** - Multi-step reasoning and self-correction capabilities
- **Context-Aware Prompting** - Dynamic prompt building with conversation history
- **Multi-Modal Processing** - Combines visual, textual, and structural information

#### **Workflow Integration**
- **Action-to-Activity Mapping** - Converts UI actions to UiPath workflow activities
- **Variable Management** - Handles input/output variables across UI actions
- **Scope Expansion** - Transforms UI automation into full UiPath workflow scopes
- **Multi-Screen Support** - Manages applications with multiple windows/screens

### Deployment & Configuration

#### **Docker Configuration**
- **Base Image**: Python 3.10 slim with computer vision libraries
- **Dependencies**: Includes ffmpeg, libsm6, libxext6 for image processing
- **Multi-Service**: Supports both UI automation and computer vision services
- **Containerized Deployment**: Full container support for production environments

#### **Configuration Options**
- **Model Selection**: Configurable AI model switching
- **LLM Gateway Integration** - Enterprise-grade model access through LLM Gateway
- **Direct Model Access** - Optional direct connection to AI model endpoints
- **Environment Variables** - Extensive configuration via environment variables

---

*This service represents a comprehensive AI-powered development assistant specifically designed for the UiPath automation ecosystem, capable of generating end-to-end automation solutions spanning multiple artifact types and complexity levels, with advanced conversational interfaces and intelligent UI automation capabilities.*