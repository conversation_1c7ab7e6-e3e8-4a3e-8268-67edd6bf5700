import typing as t

from pydantic import ValidationError

from services.studio._text_to_workflow.common.api_workflow.schema import (
    ApiWorkflowDataPoint,
)
from services.studio._text_to_workflow.utils.json_utils import json_load_from_path
from services.studio._text_to_workflow.utils.paths import get_api_workflow_edit_dataset_path, get_api_workflow_retriever_dataset_path

ApiWorkflowDatasetType = t.Literal["generate", "edit"]


def debug_json_validation(json_data: dict, model_class, prefix: str = "root") -> None:
    """
    Debug helper to validate JSON data incrementally and find problematic fields.
    Args:
        json_data: The JSON data to validate
        model_class: The Pydantic model class to validate against
        prefix: Prefix for debugging output
    """
    print(f"\n🔍 Debugging validation for {prefix}")
    print(f"Keys in JSON: {list(json_data.keys())}")

    # Try to validate the full object first
    try:
        model_class.model_validate(json_data)
        print(f"✅ Full validation successful for {prefix}")
        return
    except ValidationError as e:
        print(f"❌ Full validation failed for {prefix}")
        print("Validation errors:")
        for error in e.errors():
            print(f"  - {' -> '.join(map(str, error['loc']))}: {error['type']} - {error['msg']}")

    # Check that each field is in the model
    print(f"\n🔬 Testing individual fields for {prefix}:")
    for key, value in json_data.items():
        try:
            # Create a minimal dict with just this field
            test_dict = {key: value}
            # Try to validate just this field by creating a partial model
            if hasattr(model_class, "model_fields") and key in model_class.model_fields:
                field_info = model_class.model_fields[key]
                print(f"  ✅ {key}: type={type(value).__name__}, expected={field_info.annotation}")
            else:
                print(f"  ⚠️  {key}: not in model fields")
        except Exception as e:
            print(f"  ❌ {key}: {type(e).__name__} - {str(e)}")

    # Try validation with subset of fields
    print(f"\n🧪 Testing with progressive field addition for {prefix}:")
    test_dict = {}
    for key, value in json_data.items():
        test_dict[key] = value
        try:
            model_class.model_validate(test_dict)
            print(f"  ✅ With {len(test_dict)} fields (up to '{key}'): OK")
        except ValidationError as e:
            print(f"  ❌ With {len(test_dict)} fields (up to '{key}'): {len(e.errors())} errors")
            # Show the first few errors
            for error in e.errors()[:3]:
                print(f"    - {' -> '.join(map(str, error['loc']))}: {error['type']}")


def load_api_workflow_datapoints(dataset_type: ApiWorkflowDatasetType = "generate") -> dict[str, ApiWorkflowDataPoint]:
    dataset_dir = get_api_workflow_edit_dataset_path() if dataset_type == "edit" else get_api_workflow_retriever_dataset_path()

    # Find all JSON files in the dataset directory
    api_wf_files = list(dataset_dir.rglob("*.json"))

    datapoints = {}
    for file_path in api_wf_files:
        # Load the JSON file
        data = json_load_from_path(file_path)

        # Convert to ApiWorkflowDataPoint
        try:
            datapoint = ApiWorkflowDataPoint.model_validate(data)
            datapoints[file_path.as_posix()] = datapoint
        except ValidationError as e:
            print(f"❌ {file_path.as_posix()}: {e}")
            debug_json_validation(data, ApiWorkflowDataPoint, file_path.as_posix())
            raise  # Re-raise to maintain original behavior

    return datapoints
