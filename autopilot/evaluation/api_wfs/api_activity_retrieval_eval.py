import asyncio
import pathlib
import time

from evaluation.activity_retrieval_eval_base import ActivityRetrievalEvalServiceBase
from evaluation.api_wfs.utils import load_api_workflow_datapoints
from services.studio._text_to_workflow.api_workflow.services.api_wf_activity_retrieval_service import APIActivityRetrievalService
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.schema import NON_DYNAMIC_ACTIVITY_TYPES, ApiWorkflowDataPoint
from services.studio._text_to_workflow.common.connections_loader import get_connections_data
from services.studio._text_to_workflow.common.schema import ActivitySearchOptions, Connection
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils.embedding_model import EmbeddingModel
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.activity_retrieval import (
    ActivityLocationIndex,
    ActivityRetrievalEval,
)
from services.studio._text_to_workflow.workflow_generation.evaluation.helpers.dataset import (
    get_corresponding_pkl_path,
)
from services.studio._text_to_workflow.workflow_generation.services.common.generation_settings_builder import GenerationSettingsBuilder
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.services.helpers.activity_retrieval import (
    APIActivityRetrievalResult,
)


class APIActivityRetrievalEvalService(ActivityRetrievalEvalServiceBase):
    def __init__(
        self,
        api_gen_activities_retriever: APIActivityRetrievalService,
        activities_retriever: APIActivitiesRetriever,
        output_file_prefix: str,
        embedding_model: EmbeddingModel,
        cache_eval_results: bool = False,  # caching these results might be useful, they can be used for an eval in the draft generation
        batch_size: int = 4,
        retry_count: int = 3,
    ):
        self.activities_retriever = activities_retriever
        self.api_gen_activities_retriever = api_gen_activities_retriever

        super().__init__(embedding_model, output_file_prefix, self.api_gen_activities_retriever.config, cache_eval_results, batch_size, retry_count)

    def serialize_demonstration(self, demonstration: ApiWorkflowDataPoint) -> str:
        return self.activity_retrieval_templates["sample_row_demonstrations_template"].format(
            query=demonstration.query,
            plan=demonstration.plan.replace("\n", "<br>"),
            score=0,
            ambiguities="",
            used_activities="<br>".join(demonstration.used_activities),
            used_triggers="<br>".join([]),
        )

    def _get_baseline_activities(
        self,
        sample: ApiWorkflowDataPoint,
        connections: list[Connection],
        ignored_namespaces: set[str],
    ) -> set[str]:
        """Get the activities extracted from the source of truth plan within the current dataset"""

        steps = self._embed_workflow_plan(sample.plan)

        steps, _, _, _ = self.activities_retriever.get_relevant(
            steps,
            connections,
            ActivitySearchOptions(
                mode=sample.mode,
                target_framework="Api",
                ignored_namespaces=ignored_namespaces,
                ignored_activities=set(),
            ),
        )

        all_activities = [item for step in steps for item in step["activities"]]
        baseline_retrieved_activities = set([item["fullClassName"] for item in all_activities])

        return baseline_retrieved_activities

    async def _evaluate_sample(
        self,
        path: pathlib.Path,
        sample: ApiWorkflowDataPoint,
        connections: list[Connection],
    ) -> ActivityRetrievalEval:
        start = time.time()

        generation_settings = GenerationSettingsBuilder.build_generation_settings(sample.query, "Api", None, sample.used_activities, [], [])
        retrieval_result: APIActivityRetrievalResult = await self.api_gen_activities_retriever.generate_relevant_activities(
            sample.query, sample.existing_workflow, connections, True
        )
        elapsed = time.time() - start

        if self.cache_eval_results:
            # get path to the corresponding .pkl file
            result_path = get_corresponding_pkl_path(path)
            self._serialize_result(result_path, retrieval_result)

        # get the triggers/activities extracted from the source of truth plan within the current dataset
        baseline_retrieved_activities = self._get_baseline_activities(sample, connections, set())

        # these are the activities/triggers that we expect the model to identify as relevant to build the workflow for the current dataset
        relevant_used_activities = [item for item in sample.used_activities if item not in NON_DYNAMIC_ACTIVITY_TYPES]

        raw_matched_activities = set(retrieval_result.unprocessed_retrieved_activities) & set(relevant_used_activities)

        extended_matched_activities = set(retrieval_result.retrieved_activities) & set(relevant_used_activities)
        baseline_matched_activities = baseline_retrieved_activities & set(relevant_used_activities)

        activity_indexes: dict[str, ActivityLocationIndex] = {}
        generation_details = retrieval_result.generation_details

        # for each relevant activity, we will track its location in the proposal dataset, as well as if it was correctly identified
        for activity_full_name in relevant_used_activities:
            activity_indexes[activity_full_name] = ActivityLocationIndex(
                sample.query,
                activity_full_name in extended_matched_activities,
                self._get_first_match_index(generation_details.query_proposal_activities, activity_full_name),
                self._get_first_match_index(generation_details.workflow_proposal_activities, activity_full_name),
            )

        # embed plan tokens, we will use cosine distance to compare them with the model's output
        embeddings = self.embedding_model.encode_batch(
            [
                sample.plan,
                retrieval_result.generation.plan if retrieval_result.generation.plan is not None else "",
            ],
            2,
        )

        total_retrieved_activities = len(retrieval_result.unprocessed_retrieved_activities)
        total_extended_retrieved_activities = len(retrieval_result.retrieved_activities)

        return ActivityRetrievalEval(
            sample=sample,
            elapsed_time=elapsed,
            # for precision, lets consider "all" activities that were retrieved
            raw_activities_precision=(len(raw_matched_activities) / total_retrieved_activities) if total_retrieved_activities > 0 else 0,
            extended_activities_precision=(len(extended_matched_activities) / total_extended_retrieved_activities)
            if total_extended_retrieved_activities > 0
            else 0,
            raw_activities_recall=(len(raw_matched_activities) / len(relevant_used_activities)) if len(relevant_used_activities) > 0 else None,
            extended_activities_recall=(len(extended_matched_activities) / len(relevant_used_activities)) if len(relevant_used_activities) > 0 else None,
            baseline_activities_recall=(len(baseline_matched_activities) / len(relevant_used_activities)) if len(relevant_used_activities) > 0 else None,
            extended_trigger_precision=None,
            extended_trigger_recall=None,
            raw_trigger_recall=None,
            baseline_trigger_recall=None,
            score_precision=(100 - abs(100 - max(100, retrieval_result.generation.score))) / 100,
            ambiguities_similarity=-1,  # ambiguities not yet supported
            plan_similarity=embeddings[0] @ embeddings[1],
            proposed_triggers=[],
            proposed_activities=retrieval_result.proposed_activities,
            retrieved_triggers=[],
            retrieved_activities=retrieval_result.retrieved_activities,
            input_activity_indexes=activity_indexes,
            generation_settings=generation_settings,
            generation=retrieval_result.generation,
            token_usage=retrieval_result.token_usage,
            demonstrations=retrieval_result.demonstrations,
            prompt=retrieval_result.prompt,
            raw_response=retrieval_result.raw_response,
        )


if __name__ == "__main__":
    activities_retriever = APIActivitiesRetriever()
    embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")
    connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activities_retriever, embedding_model)

    api_activities_retriever_service = APIActivityRetrievalService(activities_retriever, connection_embeddings_retriever)
    activity_eval_service = APIActivityRetrievalEvalService(
        api_activities_retriever_service, activities_retriever, "api_activity_retrieval_eval", embedding_model, True, 8
    )
    connections = get_connections_data()

    # Edit Evaluation
    datapoints = load_api_workflow_datapoints(dataset_type="edit")
    activity_eval_service.output_file_prefix = "api_activity_retrieval_edit_eval"
    result = asyncio.run(activity_eval_service.run(datapoints, connections))

    # Generate Evaluation
    dataset_dict = load_api_workflow_datapoints()
    activity_eval_service.output_file_prefix = "api_activity_retrieval_eval"
    result = asyncio.run(activity_eval_service.run(dataset_dict, connections))
