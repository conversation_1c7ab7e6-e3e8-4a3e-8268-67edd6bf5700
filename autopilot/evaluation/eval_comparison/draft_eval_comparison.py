import argpar<PERSON>
import pathlib

from evaluation.api_wfs.api_wf_draft_generation_eval import ApiWfDraftEvalService
from services.studio._text_to_workflow.api_workflow.services.api_wf_activity_retrieval_service import APIActivityRetrievalService
from services.studio._text_to_workflow.api_workflow.services.api_wf_draft_service import ApiWfDraftService
from services.studio._text_to_workflow.common.activity_retriever import ActivitiesRetriever
from services.studio._text_to_workflow.common.api_activity_retriever import APIActivitiesRetriever
from services.studio._text_to_workflow.common.api_workflow.post_generation_processing_service import ApiWfPostGenerationProcessingService
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.workflow_generation.evaluation.draft_generation_eval import DraftGenerationEvalService
from services.studio._text_to_workflow.workflow_generation.services.connection_embeddings_retriever import ConnectionEmbeddingsRetriever
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_activity_retrieval_service import (
    WorkflowGenerationActivityRetrievalService,
)
from services.studio._text_to_workflow.workflow_generation.services.workflow_generation_draft_service import WorkflowGenerationDraftService
from services.studio._text_to_workflow.workflow_generation.workflow_generation_dynamic_activities_component import WorkflowGenerationDynamicActivitiesComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_postprocess_component import WorkflowGenerationPostProcessComponent
from services.studio._text_to_workflow.workflow_generation.workflow_generation_prompt_builder_component import WorkflowGenerationPromptBuilderComponent


def validate_evaluation_type(value: str) -> str:
    if value not in ("rpa", "api"):
        raise argparse.ArgumentTypeError(f"Invalid evaluation type: {value}. Must be 'rpa' or 'api'")
    return value


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--evaluation-dir", type=pathlib.Path, default=pathlib.Path(".").absolute(), help="The directory to save the evaluation results")
    parser.add_argument("--comparison-type", type=validate_evaluation_type, default="api", help="The type of evaluation to perform, should be 'api' or 'rpa'")
    parser.add_argument("--name", type=str, default="draft_eval_comparison", help="The name of the evaluation")
    parser.add_argument("--first-dataset-path", type=pathlib.Path, help="The path to the first dataset")
    parser.add_argument("--second-dataset-path", type=pathlib.Path, help="The path to the second dataset")

    args = parser.parse_args()
    embedding_model = ModelManager().get_embeddings_model("activities_embedding_model")

    # TO DO: add some sort of Dependency Injection that will pass the correct services for the evaluation type, instead of hardcoding them here
    if args.comparison_type == "api":
        activities_fetcher = APIActivitiesRetriever()
        post_gen_processing_service = ApiWfPostGenerationProcessingService(activities_fetcher)
        draft_creation_service = ApiWfDraftService(activities_fetcher, post_gen_processing_service)
        connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activities_fetcher, embedding_model)
        api_activities_retrieval_service = APIActivityRetrievalService(activities_fetcher, connection_embeddings_retriever)

        draft_eval_service = ApiWfDraftEvalService(api_activities_retrieval_service, draft_creation_service, args.name, 1)
    elif args.comparison_type == "rpa":
        activities_fetcher = ActivitiesRetriever()
        prompt_builder_component = WorkflowGenerationPromptBuilderComponent(activities_fetcher)
        connection_embeddings_retriever = ConnectionEmbeddingsRetriever(activities_fetcher, embedding_model)
        activities_retrieval_service = WorkflowGenerationActivityRetrievalService(activities_fetcher, connection_embeddings_retriever, prompt_builder_component)
        dynamic_activities_component = WorkflowGenerationDynamicActivitiesComponent(activities_fetcher)
        postprocess_component = WorkflowGenerationPostProcessComponent(
            activities_fetcher, dynamic_activities_component, activities_retrieval_service.ignored_activities
        )
        draft_creation_service = WorkflowGenerationDraftService(activities_fetcher, postprocess_component, prompt_builder_component)
        draft_eval_service = DraftGenerationEvalService(activities_retrieval_service, draft_creation_service, postprocess_component, args.name, 1)
    else:
        raise ValueError(f"Invalid evaluation type: {args.comparison_type}")

    if args.first_dataset_path and args.second_dataset_path:
        draft_eval_service.create_comparison_report(args.first_dataset_path, args.second_dataset_path, args.evaluation_dir)
    else:
        print("One or both datasets not provided, skipping comparison")
