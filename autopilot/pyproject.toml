[project]
name = "studio"
version = "0.1.0"
description = "Autopilot for Developers Project. Code name Wingman."
readme = "README.md"
requires-python = "~=3.10"
dependencies = [
    "anthropic==0.45.0",
    "apted==1.0.3",
    "azure-ai-inference==1.0.0b4",
    "azure-core==1.31.0",
    "azure-monitor-events-extension==0.1.0",
    "azure-monitor-opentelemetry==1.6.4",
    "azure-storage-blob==12.24.1",
    "azure-identity>=1.20.0",
    "contextvars==2.4",
    "debugpy>=1.8.12",
    "esprima==4.0.1",
    "fastapi>=0.115.7",
    "fastjsonschema==2.21.1",
    "httptools==0.6.4",
    "httpx==0.28.1",
    "immutables==0.21",
    "jq==1.8.0",
    "jsbeautifier==1.15.4",
    "json-minify==0.3.0",
    "langchain>=0.3.18",
    "langchain-aws>=0.2.15",
    "langchain-community>=0.3.17",
    "langchain-core>=0.3.35",
    "langchain-openai>=0.3.5",
    "langchain-google-vertexai>=2.0.23",
    "langdetect==1.0.9",
    "levenshtein==0.26.1",
    "lxml==5.3.0",
    "markitdown==0.0.1a4",
    "matplotlib==3.10.0",
    "numpy==1.26.4",
    "omegaconf==2.3.0",
    "openai==1.66.3",
    "opencv-python==4.11.0.86",
    "opentelemetry-instrumentation-httpx==0.50b0",
    "opentelemetry-sdk==1.29.0",
    "overrides==7.7.0",
    "pdfplumber>=0.7.0,<0.12.0",
    "pillow==11.1.0",
    "pybase64==1.4.0",
    "pydantic>=2.9.2",
    "pydantic-settings==2.7.1",
    "pyjwt==2.10.1",
    "python-dotenv==1.0.1",
    "python-magic==0.4.27",
    "python-docx>=0.8.11",
    "pyyaml==6.0.2",
    "sacrebleu==2.5.1",
    "scikit-learn==1.6.1",
    "sentence-transformers==4.1.0",
    "sse-starlette==2.2.1",
    "tempenv==2.0.0",
    "tiktoken==0.8.0",
    "torch==2.6.0",
    "torchao==0.6.1; sys_platform == 'linux'",
    "torchao==0.6.1; sys_platform == 'macos'",
    "torchao==0.8.0; sys_platform == 'windows'",
    "torchdata>=0.10.1",
    "torchtune==0.5.0",
    "transformers==4.49.0",
    "uipath-cv-client==1.0.19",
    "uvicorn==0.34.0",
    "uvloop==0.21.0 ; sys_platform == 'linux'",
    "uvloop==0.21.0 ; sys_platform == 'macos'",
    "xformers==0.0.29.post3 ; sys_platform == 'linux'",
    "unidiff==0.7.5",
    "aiofiles>=23.2.1",
    "typer>=0.15.1",
    "rich>=13.9.4",
    "redis==5.2.1",
    "aiocache==0.12.3",
    "langgraph>=0.3.27",
    "json-repair>=0.42.0",
    "langfuse>=2.60.4",
]

[dependency-groups]
dev = [
    "aiolimiter>=1.2.1",
    "autoevals>=0.0.119",
    "basedpyright>=1.29.1",
    "coolname>=2.2.0",
    "debugpy>=1.8.12",
    "flask>=3.1.0",
    "google-cloud-aiplatform>=1.82.0",
    "jsonargparse[argcomplete,signatures]>=4.37.0",
    "jupyter>=1.1.1",
    "lm-eval>=0.4.7",
    "lxml>=5.3.0",
    "nltk>=3.9.1",
    "omegaconf>=2.3.0",
    "pandas[excel,feather,hdf5,output-formatting,parquet,performance,plot,spss]>=2.2.3",
    "plotly>=6.0.0",
    "py-spy>=0.4.0",
    "pytest>=8.3.4",
    "pytest-asyncio>=0.25.3",
    "pytest-mock>=3.14.0",
    "pytest-retry>=1.7.0",
    "pytest-xdist>=3.6.1",
    "rich>=13.9.4",
    "ruff>=0.11.10",
    "st-diff-viewer>=1.0.7",
    "streamlit>=1.42.2",
    "tabulate>=0.9.0",
    "termcolor>=2.5.0",
    "torchmetrics>=1.6.1",
    "tree-sitter==0.24.0",
    "tree-sitter-c-sharp==0.23.1",
    "typer>=0.15.1",
    "wandb==0.17.8",
]

[[tool.uv.index]]
name = "uipath"
url = "https://uipath.pkgs.visualstudio.com/_packaging/ml-packages/pypi/simple/"

[tool.uv.sources]
uipath-cv-client = { path = "../computer_vision/release/uipath_cv_client", editable = true }

[tool.pytest.ini_options]
pythonpath = ["."]
testpaths = [
    "services/studio/_text_to_workflow/tests/unit",
    "services/studio/_text_to_workflow/tests/integration",
    "services/studio/_text_to_workflow/ui_automation/tests",
    "experimental/tests/unit",
    "experimental/tests/integration",
]
filterwarnings = ["ignore::DeprecationWarning", "ignore::UserWarning"]
asyncio_default_fixture_loop_scope = "session"
asyncio_mode = "auto"
retries = 2
retry_delay = 0.5
cumulative_timing = false
junit_suite_name = "Autopilot Unit Tests"

[tool.ruff]
line-length = 160
exclude = [
    "**/.pytest_cache",
    "**/.ruff_cache",
    "**/__pycache__",
    "**/*.ipynb",
    ".data",
    ".devcontainer",
    ".venv",
    ".vscode",
]

[tool.ruff.lint]
extend-select = ["I", "ASYNC", "B"]

[tool.ruff.lint.extend-per-file-ignores]
"services/studio/_text_to_workflow/**/*.py" = [
    "ASYNC230",
    "ASYNC251",
    "B904",
    "B005",
    "B006",
    "B008",
    "B027",
]

[tool.pylint.messages_control]
disable = [
    "C0301",  # line-too-long
    "C0114",  # missing-module-docstring
]

[tool.basedpyright]
typeCheckingMode = "standard"
pythonVersion = "3.10"
pythonPlatform = "Linux"
exclude = [
    "**/.pytest_cache",
    "**/.ruff_cache",
    "**/__pycache__",
    ".basedpyright",
    ".client",
    ".data",
    ".devcontainer",
    ".venv",
    ".vscode"
]
ignore = [
    "services/studio/_text_to_workflow/ui_automation/",
    "**/*.ipynb"
]