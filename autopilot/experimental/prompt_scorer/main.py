import csv
import time

import flask
from sklearn.metrics import precision_recall_curve

import experimental.prompt_scorer.prompt_scorer_model as psm
from services.studio._text_to_workflow.utils import request_utils
from services.studio._text_to_workflow.utils.paths import get_autopilot_samples_dataset_path
from services.studio._text_to_workflow.utils.testing import get_testing_request_context


def evaluate():
    dataset_path = get_autopilot_samples_dataset_path() / "PromptScorer" / "Workflow" / "workflow_test_queries.csv"
    datapoints = []

    with open(dataset_path, mode="r", encoding="utf-8-sig") as file:
        csvFile = csv.DictReader(file)
        for line in csvFile:
            if line["is_good"] is not None:
                value = line["is_good"].strip()
                unsupported_values = set(["", "duplicate", "?", "requires extra knowledge", "maybe"])
                if value not in unsupported_values:
                    datapoint = {"query": line["translation"]}
                    datapoint["is_not_good"] = value != "1" and value != "uia"
                    datapoint["raw_info"] = line
                    datapoints.append(datapoint)
    app = flask.Flask(__name__)
    app.config.update({"TESTING": True})
    request_context = get_testing_request_context(client_name="Workflow Generation Evaluation")
    request_utils.set_request_context(request_context)

    headers = {"Authorization": request_context.raw_jwt}
    durations = []
    is_tps = []
    is_fps = []
    positive_predictions = []
    positive_groundtruths = []
    y_true = []
    prediction_scores = []
    with app.test_request_context("/", method="GET", headers=headers):
        model = psm.PromptScorerModel("prompt.yaml")
        model.predict_scores = True
        for datapoint in datapoints:
            try:
                start = time.time()
                out = model.predict(datapoint["query"], type="workflow")
                elapsed = time.time() - start
                durations.append(elapsed)
                was_predicted_not_good = out.value != "GOOD"
                is_tp = was_predicted_not_good and datapoint["is_not_good"]
                is_fp = was_predicted_not_good and not datapoint["is_not_good"]
                is_tps.append(is_tp)
                is_fps.append(is_fp)
                positive_predictions.append(was_predicted_not_good)
                positive_groundtruths.append(datapoint["is_not_good"])
                print("Groundtruth:", datapoint["is_not_good"], "Prediction:", was_predicted_not_good, "Duration:", elapsed)

                datapoint["prediction"] = was_predicted_not_good
                datapoint["raw_prediction"] = out
                datapoint["prediction_score"] = 1 - out.scores["GOOD"]
                y_true.append(datapoint["is_not_good"])
                prediction_scores.append(datapoint["prediction_score"])
                if datapoint["is_not_good"] != was_predicted_not_good:
                    print("Query:", datapoint["query"], ".Prediction:", out.value, out.scores)
            except:  # noqa
                continue

        precision = sum(is_tps) / sum(positive_predictions)
        recall = sum(is_tps) / sum(positive_groundtruths)
    f1score = 2 * (precision * recall) / (precision + recall)
    print("F1 score:", f1score)
    print("Average prediction time:", sum(durations) / len(durations))
    precision, recall, thresholds = precision_recall_curve(y_true, prediction_scores)
    print(precision)
    print(recall)
    print(thresholds)


if __name__ == "__main__":
    evaluate()
