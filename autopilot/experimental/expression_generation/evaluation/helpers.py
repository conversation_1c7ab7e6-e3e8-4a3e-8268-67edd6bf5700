import tree_sitter_c_sharp as csharp_module
from tree_sitter import Language, Parser

from services.studio._text_to_workflow.common.typedefs_parser import parse_namespaces


def validate_csharp_expression(expression: str, used_variables: list[dict[str, str]], additional_typedefinitions: str, return_type: str) -> bool:
    try:
        parsed_namespaces = parse_namespaces(additional_typedefinitions)
        generic_imports = ["System", "System.Collections.Generic", "System.Linq", "System.Text", "System.IO", "System.Threading", "System.Threading.Tasks"]
        to_evaluate = ""
        for import_namespace in generic_imports:
            to_evaluate += f"using {import_namespace};\n"

        for namespace in parsed_namespaces.keys():
            to_evaluate += f"using {namespace};\n"
        variable_list = []
        included_types = set()
        for variable in used_variables:
            if variable["type"] not in included_types:
                to_evaluate += f"using {'.'.join(variable['type'].split('.')[:-1])};\n"
                included_types.add(variable["type"])

            variable_list.append(f"{variable['type']} {variable['name']}")

        to_evaluate += f"{additional_typedefinitions}\n"

        expression_function = f"public {return_type} MyFunction({', '.join(variable_list)})\n{{\n return {expression}; \n}}"

        expression_function = f"class Program\n{{static void Main(string[] args)\n{{\n}}\n {expression_function}\n}}"
        to_evaluate += expression_function
        CSHARP_LANGUAGE = Language(csharp_module.language())
        parser = Parser(CSHARP_LANGUAGE)

        tree = parser.parse(to_evaluate.encode("utf-8"))
        return not tree.root_node.has_error
    except Exception:
        return False
