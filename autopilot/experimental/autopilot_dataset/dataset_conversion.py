import asyncio
import concurrent.futures
import json
import os
import pathlib
import shutil
import tempfile
from pathlib import Path
from typing import get_args
from zipfile import ZipFile

import tqdm

from experimental.autopilot_dataset import online_client
from services.studio._text_to_workflow.common import build_embeddings, connections_loader, walkers, workflow
from services.studio._text_to_workflow.common.helpers import filter_projects, target_framework_to_target_platform
from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.common.workflow_parser import build_yaml_object
from services.studio._text_to_workflow.utils import dotnet_dynamic_activities_discovery, paths
from services.studio._text_to_workflow.utils.dotnet_workflow_converter import convert_workflow2yaml
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load


def load_conversion_result(conversion_path: str | Path) -> tuple[dict | None, dict | None, dict | None, dict | None]:
    conversion_path = Path(conversion_path)
    main_json_path = conversion_path / "Main.json"
    main_error_json_path = conversion_path / "Main.error.json"
    project_json_path = conversion_path / "project.json"
    typedef_json_path = conversion_path / "Main.typedef.json"
    main_result = None
    if main_json_path.exists():
        with open(main_json_path, "r", encoding="utf-8-sig") as f:
            main_result = json.load(f)
    main_error_result = None
    if main_error_json_path.exists():
        with open(main_error_json_path, "r", encoding="utf-8-sig") as f:
            main_error_result = json.load(f)
    project_result = None
    if project_json_path.exists():
        with open(project_json_path, "r", encoding="utf-8-sig") as f:
            project_result = json.load(f)
    typedef_result = None
    if typedef_json_path.exists():
        with open(typedef_json_path, "r", encoding="utf-8-sig") as f:
            typedef_result = json.load(f)
    return main_result, main_error_result, project_result, typedef_result


def extract_uips(uips_path: str, extract_path: str | Path):
    extract_path = Path(extract_path)
    for uip_path in Path(uips_path).glob("*.uip"):
        with ZipFile(uip_path, "r") as zipObj:
            zipObj.extractall(extract_path / uip_path.stem)


def convert_downloads(
    download_path: str | Path, only_new: bool, contains: str | None = None, local_converter: bool = False, num_workers: int = 0, only_errors: bool = False
):
    """
    Converts the raw XAML files to YAML files using the local converter or the online converter
    """
    studio_desktop_path = Path(download_path) / "StudioDesktop"
    converted_path = studio_desktop_path.parent.parent / "Converted"
    tmpdir = tempfile.mkdtemp()
    # gather jobs for conversion
    jobs = []

    projects_to_convert = filter_projects(studio_desktop_path, converted_path, contains, only_new, only_errors)
    print(f"temporarily storing files in {tmpdir}")
    for i, (project_json_path, output_folder, project_json) in enumerate(projects_to_convert):
        print(f"{i} / {len(projects_to_convert)} Converting {project_json_path}")
        target_framework = project_json["targetFramework"]  # Portable, Windows, Legacy
        target_platform = target_framework_to_target_platform(target_framework)

        if output_folder.exists():
            shutil.rmtree(output_folder)
        os.makedirs(output_folder, exist_ok=True)
        shutil.copyfile(project_json_path, output_folder / "project.json")

        project_path = project_json_path.parent
        shutil.make_archive(f"{tmpdir}/input{i}", "zip", project_path)
        zip_path = f"{tmpdir}/input{i}.zip"

        print(f"Converting job for {output_folder}")
        jobs.append(
            {
                "project_json_path": project_json_path.as_posix(),
                "output_folder": output_folder.as_posix(),
                "target_platform": target_platform,
                "zip_path": zip_path,
                "jobid": len(jobs),
            }
        )

    # do the conversion
    if local_converter:
        if num_workers == 0:
            print("local converter no concurency")
            for job in tqdm.tqdm(jobs):
                project_json_path = job["project_json_path"]
                parent_path = os.path.dirname(project_json_path)
                convert_workflow2yaml(
                    job["project_json_path"], f"{parent_path}/{job['xaml_path']}", job["output_path"], job["typedef_output_path"], job["target_framework"]
                )
        else:
            print("local converter with concurency", num_workers)
            with concurrent.futures.ThreadPoolExecutor(max_workers=num_workers) as executor:
                futures = {
                    executor.submit(
                        convert_workflow2yaml,
                        job["project_json_path"],
                        f"{os.path.dirname(job['project_json_path'])}/{job['xaml_path']}",
                        job["output_path"],
                        job["typedef_output_path"],
                        job["target_framework"],
                    ): job
                    for job in jobs
                }
                for future in concurrent.futures.as_completed(futures):
                    file = futures[future]
                    try:
                        _ = future.result()
                    except Exception as exc:
                        print(f"{file} generated an exception: {exc}")
    else:
        online_client.convert_multiple_workflows(jobs, num_workers)

    # post processing
    for job in jobs:
        output_folder = Path(job["output_folder"])
        for fpath in output_folder.glob("**/*.json"):
            if fpath.name == "project.json":
                continue
            if fpath.name.endswith(".typedef.json"):
                continue
            if fpath.name.endswith(".error.json"):
                continue
            if fpath.name.endswith(".typeDef.json"):
                with open(fpath, "r", encoding="utf-8") as f:
                    typedef = json.load(f)
                with open(fpath, "w", encoding="utf-8") as f:
                    json.dump(typedef, f, indent=2, sort_keys=True, ensure_ascii=False)
            else:
                print("job", output_folder, fpath.relative_to(output_folder))
                with open(fpath, "r", encoding="utf-8-sig") as f:
                    workflow_result = json.load(f)
                conversion_success = workflow_result["Success"]
                has_errors = len(workflow_result["Errors"]) > 0
                if not conversion_success:
                    status = "error"
                elif has_errors:
                    status = "warning"
                else:
                    status = "success"

                if status != "success":
                    print(f"Conversion {status}: {fpath}")
                if status == "error":
                    os.rename(fpath, fpath.with_suffix(".error.json"))
                else:
                    workflow = yaml_load(workflow_result["result"])
                    yaml_dump(workflow, fpath.with_suffix(".yaml"))


def build_dataset_connections_json(show_progress_bar=True):
    """
    Creates a cached json file with the DAP CLI connections for the dataset
    """
    # region load all activities ids from workflow dataset

    target_frameworks = get_args(TargetFramework)

    def load_workflow(workflow_example_dict: dict, workflow_path: str | pathlib.Path) -> workflow.Workflow | None:
        if workflow_example_dict.get("conversion_errors", ""):
            tqdm.tqdm.write(f"WARNING: Skipping due to conversion errors: {workflow_path}")
            workflow_example = None
        else:
            workflow_example = workflow.Workflow(workflow_example_dict["description"], workflow_example_dict["plan"], workflow_example_dict["process"])
            workflow_example.lmyaml()
        return workflow_example

    all_activities_and_triggers = []

    for target_framework in target_frameworks:
        converted_dataset_path = paths.get_converted_dataset_path(target_framework)
        project_json_paths = sorted(converted_dataset_path.glob("*/project.json"))

        for project_json_path in tqdm.tqdm(project_json_paths, dynamic_ncols=True):
            converted_workflow_dir_path = project_json_path.parent
            workflow_name = converted_workflow_dir_path.name
            # load workflow
            workflow_main, workflow_main_error, workflow_project, workflow_typedefs = load_conversion_result(converted_workflow_dir_path)

            if workflow_main is None:
                tqdm.tqdm.write(f"Skipping {workflow_name} because it has no Main.json")
                continue
            workflow_yaml_object = build_yaml_object(workflow_main, workflow_project)
            workflow_example = load_workflow(workflow_yaml_object, converted_workflow_dir_path)

            if workflow_example is None:
                tqdm.tqdm.write(f"Skipping {workflow_name} because the workflow could not be loaded.")
                continue

            activities = walkers.ActivitiesAndTriggersCollector().collect(workflow_example)
            all_activities_and_triggers.extend(activities["activity"])
            all_activities_and_triggers.extend(activities["trigger"])

    activities_and_triggers_ids = set()

    for item in all_activities_and_triggers:
        if item.activity_id is not None:
            activities_and_triggers_ids.add(item.activity_id)
        else:
            print(f"WARNING! Activity_id is None for {item.fqn}")
            activities_and_triggers_ids.add(item.fqn)

    # endregion

    activity_documents = build_embeddings.build("activity", show_progress_bar=show_progress_bar)["activities"]
    trigger_documents = build_embeddings.build("trigger", show_progress_bar=show_progress_bar)["activities"]
    # load and split documents
    documents = activity_documents + trigger_documents

    if show_progress_bar:
        documents = tqdm.tqdm(documents, desc="Typedefs: splitting documents", unit="documents", dynamic_ncols=True)

    connections = connections_loader.get_connections_data()
    connections_by_key = {c["connector"]: c for c in connections}
    is_package_name, is_package_version, is_connections = None, None, []

    # region load is_connections

    for document in documents:
        if document["activityTypeId"] is None:
            # print(f"Skipping {document['fullActivityId']} because it has no activityTypeId.")
            continue

        if document["fullActivityId"] not in activities_and_triggers_ids:
            # print(f"Skipping {document['fullActivityId']} because it is not in the dataset. ({document['className']})")
            continue

        if is_package_version is None or document["packageVersion"] > is_package_version:
            is_package_name = document["packageName"]
            is_package_version = document["packageVersion"]

        if document["connectorKey"] in connections_by_key:
            is_connections.append(
                {
                    "ConnectionId": connections_by_key[document["connectorKey"]]["connectionId"],
                    "Configuration": document["activityConfiguration"],
                    "ClassDefinition": document["typeDefinition"],
                    "ActivityFullName": document["fullActivityName"],
                    "UiPathActivityTypeId": document["activityTypeId"],
                }
            )
        else:
            print(f"Skipping {document['fullActivityId']} because the connection is missing {document['connectorKey']}.")
    # endregion

    is_generated_connections = asyncio.run(
        dotnet_dynamic_activities_discovery.get_type_definitions(is_package_name, is_package_version, is_connections, capture_output=False, disable_cache=True)
    )

    dataset_connections_path = paths.get_dataset_connections_cache_path()

    with open(dataset_connections_path, "w", encoding="utf-8") as f:
        json.dump(is_generated_connections, f, ensure_ascii=False, indent=4)

    return is_generated_connections
