import json
import pathlib

import streamlit as st

import experimental.api_activity_edit.chat as aec
import services.studio._text_to_workflow.api_activity_edit.api_activity_edit_schema as aes
import services.studio._text_to_workflow.utils.request_utils as ru
import services.studio._text_to_workflow.utils.testing as ut
import services.studio._text_to_workflow.utils.uipath_cloud_platform as ucp
from services.studio._text_to_workflow.utils import paths

_activity_edit_dataset_dir = paths.get_autopilot_samples_dataset_path() / "APIActivityEdit"


@st.cache_resource
def get_data() -> tuple[list[pathlib.Path], dict[str, dict[str, list[str]]], dict[str, list[str]]]:
    datapoints = sorted((path.relative_to(_activity_edit_dataset_dir) for path in _activity_edit_dataset_dir.rglob("*/*.json")))
    index = {"languages": {}, "workflows": {}, "activities": {}}
    for path in datapoints:
        datapoint = load_datapoint(path)
        workflow_name = path.parent.name
        language = datapoint["input"]["language"]
        activity = datapoint["expected"]["activity"]["activity"]
        if language not in index["languages"]:
            index["languages"][language] = []
        index["languages"][language].append(path)
        if workflow_name not in index["workflows"]:
            index["workflows"][workflow_name] = []
        index["workflows"][workflow_name].append(path)
        if activity not in index["activities"]:
            index["activities"][activity] = []
        index["activities"][activity].append(path)

    # Sort the index
    for k in tuple(index.keys()):
        index[k] = {kk: vv for kk, vv in sorted(index[k].items())}

    filters = {
        "languages": ["*"] + list(index["languages"].keys()),
        "workflows": ["*"] + list(index["workflows"].keys()),
        "activities": ["*"] + list(index["activities"].keys()),
    }

    return datapoints, index, filters


@st.cache_data
def get_supported_models() -> tuple[str, ...]:
    return aes.MODELS


def filter_datapoints() -> list[pathlib.Path]:
    if st.session_state.datapoints is None:
        st.error("No datapoints found.")
        return []
    if st.session_state.selected_filters is None:
        st.error("No filters selected.")
        return st.session_state.datapoints

    filtered_datapoints = {}
    for filter_type, filter_value in st.session_state.selected_filters.items():
        if filter_value == "*":
            filtered_datapoints[filter_type] = set(st.session_state.datapoints)
        else:
            filtered_datapoints[filter_type] = set(st.session_state.index[filter_type][filter_value])

    return sorted(set.intersection(*filtered_datapoints.values()))


def load_datapoint(datapoint_path: pathlib.Path) -> dict:
    with open(_activity_edit_dataset_dir / datapoint_path, "r") as f:
        return json.load(f)


def create_chat():
    if not st.session_state.model_name:
        st.error("No model selected.")
        return
    if not st.session_state.selected_datapoint:
        st.error("No datapoint selected.")
        return
    datapoint = load_datapoint(st.session_state.selected_datapoint)
    return aec.ActivityEditChatClient(model_name=st.session_state.model_name, datapoint=datapoint)


async def send_message(prompt: str) -> None:
    if not st.session_state.tenant_id:
        st.error("No tenant ID selected.")
        return
    if not ucp.is_token_valid(st.session_state.token):
        st.error("Invalid token.")
        return
    if st.session_state.chat_client is None:
        st.error("No chat selected.")
        return
    request_context = ut.get_testing_request_context("en", "API Activity Edit")
    ru.set_request_context(request_context)
    await st.session_state.chat_client.ask({"role": "user", "content": prompt})
